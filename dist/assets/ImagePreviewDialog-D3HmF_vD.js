import{d as w,e as C,q as V,j as n,h as j,c as d,M as c,a as e,n as B,t as o,F as M,k as _,C as k,E as i,o as s,_ as I}from"./index-84KJ6UvQ.js";const z={key:0,class:"p-6 space-y-6"},E={class:"flex justify-center"},T={class:"relative"},D=["src","alt"],N={class:"absolute top-4 left-4"},U={class:"bg-gray-50 dark:bg-dark-card rounded-lg p-4"},F={class:"grid grid-cols-2 gap-4"},L={class:"text-gray-900 dark:text-dark-text"},P={class:"text-gray-900 dark:text-dark-text"},S={class:"text-gray-900 dark:text-dark-text"},q={class:"text-gray-900 dark:text-dark-text"},H={key:0,class:"mt-4"},K={class:"flex flex-wrap gap-2 mt-2"},$=w({__name:"ImagePreviewDialog",props:{modelValue:{type:Boolean},image:{}},emits:["update:modelValue"],setup(p,{emit:b}){const a=p,g=b,m=C({get:()=>a.modelValue,set:t=>g("update:modelValue",t)}),y=t=>({main:"bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300",detail:"bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300",sku:"bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300",material:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300",background:"bg-indigo-100 text-indigo-800 dark:bg-indigo-900/30 dark:text-indigo-300",result:"bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300"})[t]||"bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300",u=t=>({main:"主图",detail:"详情图",sku:"SKU图",material:"素材",background:"背景",result:"处理结果"})[t]||"未知",f=()=>{if(!a.image)return;const t=document.createElement("a");t.href=a.image.url,t.download=a.image.name,document.body.appendChild(t),t.click(),document.body.removeChild(t),i.success(`正在下载 ${a.image.name}`)},v=async()=>{if(a.image)try{await navigator.clipboard.writeText(a.image.url),i.success("图片链接已复制到剪贴板")}catch{i.error("复制失败，请手动复制")}},x=()=>{g("update:modelValue",!1)};return(t,r)=>{const h=j("el-dialog");return s(),V(h,{modelValue:m.value,"onUpdate:modelValue":r[0]||(r[0]=l=>m.value=l),width:"800px","align-center":"","show-close":!1,class:"modern-dialog"},{header:n(()=>[e("div",{class:"flex items-center justify-between p-6 border-b border-gray-100 dark:border-dark-border"},[r[2]||(r[2]=e("div",{class:"flex items-center space-x-3"},[e("div",{class:"w-10 h-10 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"}),e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"})])]),e("div",null,[e("h3",{class:"text-xl font-bold text-gray-900 dark:text-dark-text"},"图片预览"),e("p",{class:"text-sm text-gray-600 dark:text-dark-text-secondary"},"查看图片详细信息")])],-1)),e("button",{onClick:x,class:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"},r[1]||(r[1]=[e("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))])]),footer:n(()=>[e("div",{class:"flex items-center justify-between p-6 border-t border-gray-100 dark:border-dark-border bg-gray-50 dark:bg-dark-card/50"},[e("div",{class:"flex space-x-3"},[e("button",{onClick:f,class:"inline-flex items-center px-4 py-2 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-medium rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"},r[9]||(r[9]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"})],-1),k(" 下载图片 ")])),e("button",{onClick:v,class:"inline-flex items-center px-4 py-2 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-medium rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"},r[10]||(r[10]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"})],-1),k(" 复制链接 ")]))]),e("button",{onClick:x,class:"px-6 py-2.5 text-gray-700 dark:text-dark-text font-medium rounded-lg border border-gray-300 dark:border-dark-border hover:bg-gray-50 dark:hover:bg-dark-border transition-all duration-200"}," 关闭 ")])]),default:n(()=>[t.image?(s(),d("div",z,[e("div",E,[e("div",T,[e("img",{src:t.image.url,alt:t.image.name,class:"max-w-full max-h-96 object-contain rounded-lg shadow-lg"},null,8,D),e("div",N,[e("span",{class:B([y(t.image.category),"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium"])},o(u(t.image.category)),3)])])]),e("div",U,[r[8]||(r[8]=e("h4",{class:"text-lg font-semibold text-gray-900 dark:text-dark-text mb-4"},"图片信息",-1)),e("div",F,[e("div",null,[r[3]||(r[3]=e("label",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"文件名",-1)),e("p",L,o(t.image.name),1)]),e("div",null,[r[4]||(r[4]=e("label",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"文件大小",-1)),e("p",P,o(t.image.size),1)]),e("div",null,[r[5]||(r[5]=e("label",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"上传时间",-1)),e("p",S,o(t.image.uploadTime),1)]),e("div",null,[r[6]||(r[6]=e("label",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"图片分类",-1)),e("p",q,o(u(t.image.category)),1)])]),t.image.tags&&t.image.tags.length>0?(s(),d("div",H,[r[7]||(r[7]=e("label",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"标签",-1)),e("div",K,[(s(!0),d(M,null,_(t.image.tags,l=>(s(),d("span",{key:l,class:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300"},o(l),1))),128))])])):c("",!0)])])):c("",!0)]),_:1},8,["modelValue"])}}}),G=I($,[["__scopeId","data-v-238d5848"]]);export{G as I};
