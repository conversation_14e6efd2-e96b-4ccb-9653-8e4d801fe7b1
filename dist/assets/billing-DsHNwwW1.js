import{r as s,e as p}from"./index-BTucjZQh.js";import{B as a}from"./billing-Ciq-WFQK.js";const g=[{appId:"product-collection",appName:"商品采集",billingMode:a.PER_ITEM,unitPrice:.1,description:"按采集商品数量计费，每个商品0.1元",freeQuota:10,minimumCharge:1},{appId:"smart-crop",appName:"智能裁图",billingMode:a.PER_ITEM,unitPrice:.05,description:"按处理图片数量计费，每张图片0.05元",freeQuota:20,minimumCharge:.5},{appId:"one-click-cutout",appName:"一键抠图",billingMode:a.PER_ITEM,unitPrice:.08,description:"按处理图片数量计费，每张图片0.08元",freeQuota:15,minimumCharge:.8},{appId:"super-split",appName:"超级裂变",billingMode:a.PER_ITEM,unitPrice:.12,description:"按处理图片数量计费，每张图片0.12元",freeQuota:10,minimumCharge:1.2},{appId:"title-generator",appName:"标题生成",billingMode:a.PER_ITEM,unitPrice:.03,description:"按生成标题数量计费，每个标题0.03元",freeQuota:50,minimumCharge:.3},{appId:"batch-listing",appName:"批量刊登",billingMode:a.PER_ITEM,unitPrice:.2,description:"按刊登商品数量计费，每个商品0.2元",freeQuota:5,minimumCharge:2},{appId:"pod-compose",appName:"POD合成",billingMode:a.PER_ITEM,unitPrice:.15,description:"按合成商品数量计费，每个商品0.15元",freeQuota:8,minimumCharge:1.5},{appId:"copyright-detection",appName:"侵权检测",billingMode:a.PER_ITEM,unitPrice:.06,description:"按检测图片数量计费，每张图片0.06元",freeQuota:20,minimumCharge:.6}],d=s({balance:156.78,frozenBalance:23.45,availableBalance:133.33,lastUpdateTime:new Date().toISOString()}),l=s({"product-collection":5,"smart-crop":12,"one-click-cutout":8,"super-split":3,"title-generator":25,"batch-listing":2,"pod-compose":4,"copyright-detection":15});p(()=>d.value.availableBalance);const m=i=>g.find(t=>t.appId===i)||null,Q=(i,t)=>{const e=m(i);if(!e)return 0;const n=l.value[i]||0,o=e.freeQuota||0,r=Math.max(0,o-n),u=Math.max(0,t-r)*e.unitPrice;return u>0&&e.minimumCharge&&u<e.minimumCharge?e.minimumCharge:u},f=i=>{const t=m(i);if(!t||!t.freeQuota)return null;const e=l.value[i]||0,n=Math.max(0,t.freeQuota-e);return{total:t.freeQuota,used:e,remaining:n,percentage:e/t.freeQuota*100}},I=(i,t)=>{const e=m(i);if(!e)return{totalCost:0,freeItems:0,chargeableItems:0,unitPrice:0,hasFreeQuota:!1,quotaInfo:null};const n=f(i),o=n?Math.min(t,n.remaining):0,r=Math.max(0,t-o),c=r*e.unitPrice;return{totalCost:c>0&&e.minimumCharge&&c<e.minimumCharge?e.minimumCharge:c,freeItems:o,chargeableItems:r,unitPrice:e.unitPrice,hasFreeQuota:!!e.freeQuota,quotaInfo:n,minimumCharge:e.minimumCharge}};export{I as a,Q as c,m as g};
