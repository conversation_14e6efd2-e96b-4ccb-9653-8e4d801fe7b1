import{c as r,o as s,a as e,d as H,r as x,e as F,f as N,g as k,h as L,j as V,F as h,k as y,t as a,n as w,l as U,m as G,p as ee,q as $,s as C,x as S,y as D,z as te,A as T,B as q,E as u,C as Z,D as O,G as Q,H as J,b as se,I as re,J as ae}from"./index-84KJ6UvQ.js";import{r as P}from"./DocumentTextIcon-DrpWdS_r.js";import{r as R}from"./InformationCircleIcon-DvB6crIG.js";import{r as K}from"./CheckCircleIcon-Jgkg8RrJ.js";import{r as W}from"./ClipboardDocumentListIcon-BYBIBzXP.js";import{r as oe}from"./ArrowDownTrayIcon-C6iGhkUM.js";import{r as X}from"./ChartBarIcon-nW-6KFkg.js";import{r as ne}from"./ShieldCheckIcon-CNdvEWoV.js";import{r as de}from"./ClockIcon-CfVUFk2U.js";import{r as ie}from"./CreditCardIcon-CsWaKFOb.js";function le(v,c){return s(),r("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5"})])}function ce(v,c){return s(),r("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5"})])}function ue(v,c){return s(),r("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.857 17.082a23.848 23.848 0 0 0 5.454-1.31A8.967 8.967 0 0 1 18 9.75V9A6 6 0 0 0 6 9v.75a8.967 8.967 0 0 1-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 0 1-5.714 0m5.714 0a3 3 0 1 1-5.714 0"})])}function xe(v,c){return s(),r("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m19.5 8.25-7.5 7.5-7.5-7.5"})])}function E(v,c){return s(),r("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m8.25 4.5 7.5 7.5-7.5 7.5"})])}function ge(v,c){return s(),r("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m4.5 15.75 7.5-7.5 7.5 7.5"})])}function me(v,c){return s(),r("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M20.25 6.375c0 2.278-3.694 4.125-8.25 4.125S3.75 8.653 3.75 6.375m16.5 0c0-2.278-3.694-4.125-8.25-4.125S3.75 4.097 3.75 6.375m16.5 0v11.25c0 2.278-3.694 4.125-8.25 4.125s-8.25-1.847-8.25-4.125V6.375m16.5 0v3.75m-16.5-3.75v3.75m16.5 0v3.75C20.25 16.153 16.556 18 12 18s-8.25-1.847-8.25-4.125v-3.75m16.5 0c0 2.278-3.694 4.125-8.25 4.125s-8.25-1.847-8.25-4.125"})])}function ke(v,c){return s(),r("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M2.25 15a4.5 4.5 0 0 0 4.5 4.5H18a3.75 3.75 0 0 0 1.332-7.257 3 3 0 0 0-3.758-3.848 5.25 5.25 0 0 0-10.233 2.33A4.502 4.502 0 0 0 2.25 15Z"})])}function pe(v,c){return s(),r("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M10.343 3.94c.09-.542.56-.94 1.11-.94h1.093c.55 0 1.02.398 1.11.94l.149.894c.07.424.384.764.78.93.398.164.855.142 1.205-.108l.737-.527a1.125 1.125 0 0 1 1.45.12l.773.774c.39.389.44 1.002.12 1.45l-.527.737c-.25.35-.272.806-.107 1.204.165.397.505.71.93.78l.893.15c.543.09.94.559.94 1.109v1.094c0 .55-.397 1.02-.94 1.11l-.894.149c-.424.07-.764.383-.929.78-.165.398-.143.854.107 1.204l.527.738c.32.447.269 1.06-.12 1.45l-.774.773a1.125 1.125 0 0 1-1.449.12l-.738-.527c-.35-.25-.806-.272-1.203-.107-.398.165-.71.505-.781.929l-.149.894c-.09.542-.56.94-1.11.94h-1.094c-.55 0-1.019-.398-1.11-.94l-.148-.894c-.071-.424-.384-.764-.781-.93-.398-.164-.854-.142-1.204.108l-.738.527c-.447.32-1.06.269-1.45-.12l-.773-.774a1.125 1.125 0 0 1-.12-1.45l.527-.737c.25-.35.272-.806.108-1.204-.165-.397-.506-.71-.93-.78l-.894-.15c-.542-.09-.94-.56-.94-1.109v-1.094c0-.55.398-1.02.94-1.11l.894-.149c.424-.07.765-.383.93-.78.165-.398.143-.854-.108-1.204l-.526-.738a1.125 1.125 0 0 1 .12-1.45l.773-.773a1.125 1.125 0 0 1 1.45-.12l.737.527c.35.25.807.272 1.204.107.397-.165.71-.505.78-.929l.15-.894Z"}),e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"})])}function ve(v,c){return s(),r("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M8.25 3v1.5M4.5 8.25H3m18 0h-1.5M4.5 12H3m18 0h-1.5m-15 3.75H3m18 0h-1.5M8.25 19.5V21M12 3v1.5m0 15V21m3.75-18v1.5m0 15V21m-9-1.5h10.5a2.25 2.25 0 0 0 2.25-2.25V6.75a2.25 2.25 0 0 0-2.25-2.25H6.75A2.25 2.25 0 0 0 4.5 6.75v10.5a2.25 2.25 0 0 0 2.25 2.25Zm.75-12h9v9h-9v-9Z"})])}function be(v,c){return s(),r("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M2.25 12.75V12A2.25 2.25 0 0 1 4.5 9.75h15A2.25 2.25 0 0 1 21.75 12v.75m-8.69-6.44-2.12-2.12a1.5 1.5 0 0 0-1.061-.44H4.5A2.25 2.25 0 0 0 2.25 6v12a2.25 2.25 0 0 0 2.25 2.25h15A2.25 2.25 0 0 0 21.75 18V9a2.25 2.25 0 0 0-2.25-2.25h-5.379a1.5 1.5 0 0 1-1.06-.44Z"})])}function he(v,c){return s(),r("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0"})])}function ye(v,c){return s(),r("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"})])}const fe={class:"bg-white dark:bg-dark-card rounded-xl p-6 shadow-sm border border-gray-100 dark:border-dark-border"},_e={class:"flex items-center justify-between mb-6"},we={class:"flex items-center space-x-2"},$e={class:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-8"},Ce={class:"space-y-3"},Me={class:"flex items-center space-x-3"},je={class:"font-medium text-gray-900 dark:text-dark-text"},Te={class:"text-sm text-gray-500 dark:text-dark-text-secondary"},Be={class:"text-right"},Ae={class:"text-sm font-medium text-gray-900 dark:text-dark-text"},Ve={class:"w-16 bg-gray-200 dark:bg-gray-700 rounded-full h-2 mt-1"},Se={class:"relative"},De={class:"w-32 h-32 mx-auto mb-4 relative"},Ie={viewBox:"0 0 100 100",class:"w-full h-full transform -rotate-90"},ze=["stroke-dashoffset"],Ue=["stroke-dashoffset"],He={class:"absolute inset-0 flex items-center justify-center"},Le={class:"text-center"},Ze={class:"text-2xl font-bold text-gray-900 dark:text-dark-text"},Ee={class:"space-y-2"},Ne={class:"flex items-center justify-between"},Pe={class:"text-sm font-medium text-gray-900 dark:text-dark-text"},Re={class:"flex items-center justify-between"},Fe={class:"text-sm font-medium text-gray-900 dark:text-dark-text"},Ge={class:"flex items-center justify-between"},qe={class:"text-sm font-medium text-gray-900 dark:text-dark-text"},Oe={class:"h-64 bg-gray-50 dark:bg-dark-border rounded-lg p-4"},Qe={class:"h-full flex items-end justify-between space-x-2"},Je=["title"],Ke={class:"flex justify-between mt-2 text-xs text-gray-500 dark:text-dark-text-secondary"},We={class:"mt-8"},Xe={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},Ye={class:"flex items-center justify-between mb-2"},et={class:"text-sm font-medium text-gray-700 dark:text-dark-text-secondary"},tt={class:"text-lg font-bold text-gray-900 dark:text-dark-text"},st={class:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2"},rt={class:"flex justify-between mt-1 text-xs text-gray-500 dark:text-dark-text-secondary"},at=H({__name:"DataDashboard",setup(v){const c=x("today"),b=x([{id:"product-collection",name:"商品采集",usage:156,percentage:35},{id:"smart-crop",name:"智能裁图",usage:128,percentage:28},{id:"one-click-cutout",name:"一键抠图",usage:89,percentage:20},{id:"title-generator",name:"标题生成",usage:67,percentage:15},{id:"batch-listing",name:"批量刊登",usage:23,percentage:5}]),p=x({completed:75,processing:18,failed:7}),_=x([{label:"周一",value:120},{label:"周二",value:156},{label:"周三",value:189},{label:"周四",value:167},{label:"周五",value:234},{label:"周六",value:198},{label:"周日",value:145}]),j=x([{id:"success-rate",label:"成功率",value:"94.2%",percentage:94,status:"excellent"},{id:"avg-time",label:"平均处理时间",value:"2.3s",percentage:85,status:"good"},{id:"resource-usage",label:"资源使用率",value:"67%",percentage:67,status:"warning"}]),B=F(()=>b.value.reduce((f,d)=>f+d.usage,0)),M=f=>["bg-gradient-to-br from-yellow-400 to-yellow-500","bg-gradient-to-br from-gray-400 to-gray-500","bg-gradient-to-br from-amber-600 to-amber-700","bg-gradient-to-br from-blue-500 to-blue-600","bg-gradient-to-br from-purple-500 to-purple-600"][f]||"bg-gradient-to-br from-gray-500 to-gray-600",A=f=>({excellent:"bg-gradient-to-r from-green-500 to-green-600",good:"bg-gradient-to-r from-blue-500 to-blue-600",warning:"bg-gradient-to-r from-amber-500 to-amber-600",poor:"bg-gradient-to-r from-red-500 to-red-600"})[f]||"bg-gray-500";return N(()=>{}),(f,d)=>{const g=L("el-option"),m=L("el-select");return s(),r("div",fe,[e("div",_e,[d[1]||(d[1]=e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-dark-text"},"数据看板",-1)),e("div",we,[k(m,{modelValue:c.value,"onUpdate:modelValue":d[0]||(d[0]=n=>c.value=n),size:"small",class:"w-24"},{default:V(()=>[k(g,{label:"今日",value:"today"}),k(g,{label:"本周",value:"week"}),k(g,{label:"本月",value:"month"})]),_:1},8,["modelValue"])])]),e("div",$e,[e("div",null,[d[2]||(d[2]=e("h4",{class:"text-sm font-medium text-gray-700 dark:text-dark-text-secondary mb-4"},"应用使用排行",-1)),e("div",Ce,[(s(!0),r(h,null,y(b.value,(n,i)=>(s(),r("div",{key:n.id,class:"flex items-center justify-between p-3 bg-gray-50 dark:bg-dark-border rounded-lg"},[e("div",Me,[e("div",{class:w(["w-8 h-8 rounded-lg flex items-center justify-center text-sm font-bold text-white",M(i)])},a(i+1),3),e("div",null,[e("div",je,a(n.name),1),e("div",Te,a(n.usage)+"次使用",1)])]),e("div",Be,[e("div",Ae,a(n.percentage)+"%",1),e("div",Ve,[e("div",{class:"bg-gradient-to-r from-blue-500 to-blue-600 h-2 rounded-full transition-all duration-300",style:U({width:`${n.percentage}%`})},null,4)])])]))),128))])]),e("div",null,[d[8]||(d[8]=e("h4",{class:"text-sm font-medium text-gray-700 dark:text-dark-text-secondary mb-4"},"任务状态分布",-1)),e("div",Se,[e("div",De,[(s(),r("svg",Ie,[d[3]||(d[3]=e("circle",{cx:"50",cy:"50",r:"40",fill:"none",stroke:"#e5e7eb","stroke-width":"8",class:"dark:stroke-gray-700"},null,-1)),e("circle",{cx:"50",cy:"50",r:"40",fill:"none",stroke:"#10b981","stroke-width":"8","stroke-dasharray":"188.5","stroke-dashoffset":188.5-188.5*p.value.completed/100,class:"transition-all duration-500"},null,8,ze),e("circle",{cx:"50",cy:"50",r:"40",fill:"none",stroke:"#f59e0b","stroke-width":"8","stroke-dasharray":"188.5","stroke-dashoffset":188.5-188.5*(p.value.completed+p.value.processing)/100,class:"transition-all duration-500"},null,8,Ue)])),e("div",He,[e("div",Le,[e("div",Ze,a(B.value),1),d[4]||(d[4]=e("div",{class:"text-sm text-gray-500 dark:text-dark-text-secondary"},"总任务",-1))])])]),e("div",Ee,[e("div",Ne,[d[5]||(d[5]=e("div",{class:"flex items-center space-x-2"},[e("div",{class:"w-3 h-3 bg-green-500 rounded-full"}),e("span",{class:"text-sm text-gray-600 dark:text-dark-text-secondary"},"已完成")],-1)),e("span",Pe,a(p.value.completed)+"% ",1)]),e("div",Re,[d[6]||(d[6]=e("div",{class:"flex items-center space-x-2"},[e("div",{class:"w-3 h-3 bg-amber-500 rounded-full"}),e("span",{class:"text-sm text-gray-600 dark:text-dark-text-secondary"},"处理中")],-1)),e("span",Fe,a(p.value.processing)+"% ",1)]),e("div",Ge,[d[7]||(d[7]=e("div",{class:"flex items-center space-x-2"},[e("div",{class:"w-3 h-3 bg-red-500 rounded-full"}),e("span",{class:"text-sm text-gray-600 dark:text-dark-text-secondary"},"失败")],-1)),e("span",qe,a(p.value.failed)+"% ",1)])])])])]),e("div",null,[d[9]||(d[9]=e("h4",{class:"text-sm font-medium text-gray-700 dark:text-dark-text-secondary mb-4"},"使用趋势",-1)),e("div",Oe,[e("div",Qe,[(s(!0),r(h,null,y(_.value,(n,i)=>(s(),r("div",{key:i,class:"flex-1 bg-gradient-to-t from-blue-500 to-blue-400 rounded-t-sm transition-all duration-300 hover:from-blue-600 hover:to-blue-500",style:U({height:`${n.value/Math.max(..._.value.map(t=>t.value))*100}%`}),title:`${n.label}: ${n.value}`},null,12,Je))),128))]),e("div",Ke,[(s(!0),r(h,null,y(_.value,n=>(s(),r("span",{key:n.label},a(n.label),1))),128))])])]),e("div",We,[d[10]||(d[10]=e("h4",{class:"text-sm font-medium text-gray-700 dark:text-dark-text-secondary mb-4"},"性能指标",-1)),e("div",Xe,[(s(!0),r(h,null,y(j.value,n=>(s(),r("div",{key:n.id,class:"p-4 bg-gray-50 dark:bg-dark-border rounded-lg"},[e("div",Ye,[e("span",et,a(n.label),1),e("span",tt,a(n.value),1)]),e("div",st,[e("div",{class:w(["h-2 rounded-full transition-all duration-300",A(n.status)]),style:U({width:`${n.percentage}%`})},null,6)]),e("div",rt,[e("span",null,a(n.status),1),e("span",null,a(n.percentage)+"%",1)])]))),128))])])])}}}),ot={class:"bg-white dark:bg-dark-card rounded-xl p-6 shadow-sm border border-gray-100 dark:border-dark-border"},nt={class:"mb-8"},dt={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},it={class:"flex items-center justify-between mb-2"},lt={class:"text-sm font-medium text-gray-700 dark:text-dark-text-secondary"},ct={class:"text-lg font-bold text-gray-900 dark:text-dark-text"},ut={class:"text-xs text-gray-500 dark:text-dark-text-secondary mt-1"},xt={class:"mb-8"},gt={class:"flex items-center justify-between mb-4"},mt={class:"text-sm text-gray-500 dark:text-dark-text-secondary"},kt={class:"space-y-3 max-h-64 overflow-y-auto"},pt={class:"flex items-center justify-between mb-2"},vt={class:"flex items-center space-x-2"},bt={class:"text-sm font-medium text-gray-900 dark:text-dark-text"},ht={class:"text-xs text-gray-500 dark:text-dark-text-secondary"},yt={class:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mb-2"},ft={class:"flex justify-between text-xs text-gray-500 dark:text-dark-text-secondary"},_t={class:"mb-8"},wt={class:"space-y-4"},$t={class:"flex items-center space-x-3"},Ct={class:"text-sm font-medium text-gray-900 dark:text-dark-text"},Mt={class:"text-xs text-gray-500 dark:text-dark-text-secondary"},jt={class:"text-right"},Tt={class:"text-sm font-medium text-gray-900 dark:text-dark-text"},Bt={class:"w-20 bg-gray-200 dark:bg-gray-700 rounded-full h-2 mt-1"},At={class:"space-y-3 max-h-48 overflow-y-auto"},Vt={class:"flex-1 min-w-0"},St={class:"text-sm text-gray-900 dark:text-dark-text"},Dt={class:"text-xs text-gray-500 dark:text-dark-text-secondary"},It=H({__name:"RealTimeMonitoring",setup(v){const c=x([{id:"api",name:"API服务",value:"正常",status:"healthy",description:"响应时间: 120ms"},{id:"database",name:"数据库",value:"正常",status:"healthy",description:"连接数: 45/100"},{id:"storage",name:"存储服务",value:"警告",status:"warning",description:"使用率: 85%"}]),b=x([{id:"task1",name:"商品采集 - Amazon平台",type:"product-collection",progress:75,operator:"张三",estimatedTime:"5分钟"},{id:"task2",name:"智能裁图批处理",type:"smart-crop",progress:45,operator:"李四",estimatedTime:"12分钟"},{id:"task3",name:"标题生成任务",type:"title-generator",progress:90,operator:"王五",estimatedTime:"2分钟"}]),p=x([{id:"cpu",name:"CPU使用率",description:"处理器负载",usage:65,icon:ve},{id:"memory",name:"内存使用率",description:"8GB / 16GB",usage:50,icon:me},{id:"storage",name:"存储使用率",description:"850GB / 1TB",usage:85,icon:ke}]),_=x([{id:"act1",type:"success",message:"商品采集任务 #12345 已完成",time:"2分钟前"},{id:"act2",type:"info",message:"用户 张三 登录系统",time:"5分钟前"},{id:"act3",type:"warning",message:"存储空间使用率超过80%",time:"10分钟前"},{id:"act4",type:"success",message:"智能裁图任务 #12344 已完成",time:"15分钟前"},{id:"act5",type:"error",message:"批量刊登任务 #12343 执行失败",time:"20分钟前"}]),j=n=>({healthy:"bg-green-500",warning:"bg-amber-500",error:"bg-red-500"})[n]||"bg-gray-500",B=n=>({healthy:"border-green-200 dark:border-green-800 bg-green-50 dark:bg-green-900/10",warning:"border-amber-200 dark:border-amber-800 bg-amber-50 dark:bg-amber-900/10",error:"border-red-200 dark:border-red-800 bg-red-50 dark:bg-red-900/10"})[n]||"border-gray-200 dark:border-gray-700",M=n=>({"product-collection":D,"smart-crop":S,"title-generator":P})[n]||D,A=n=>n>=80?"bg-gradient-to-r from-red-500 to-red-600":n>=60?"bg-gradient-to-r from-amber-500 to-amber-600":"bg-gradient-to-r from-green-500 to-green-600",f=n=>({success:"bg-green-500",warning:"bg-amber-500",error:"bg-red-500",info:"bg-blue-500"})[n]||"bg-gray-500",d=n=>({success:K,warning:te,error:ye,info:R})[n]||R,g=()=>{b.value.forEach(n=>{n.progress<100&&(n.progress=Math.min(100,n.progress+Math.random()*5))}),p.value.forEach(n=>{const i=(Math.random()-.5)*10;n.usage=Math.max(0,Math.min(100,n.usage+i))})};let m;return N(()=>{m=setInterval(g,3e3)}),G(()=>{m&&clearInterval(m)}),(n,i)=>(s(),r("div",ot,[i[4]||(i[4]=ee('<div class="flex items-center justify-between mb-6"><h3 class="text-lg font-semibold text-gray-900 dark:text-dark-text">实时监控</h3><div class="flex items-center space-x-2"><div class="flex items-center space-x-1"><div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div><span class="text-sm text-gray-600 dark:text-dark-text-secondary">实时更新</span></div></div></div>',1)),e("div",nt,[i[0]||(i[0]=e("h4",{class:"text-sm font-medium text-gray-700 dark:text-dark-text-secondary mb-4"},"系统状态",-1)),e("div",dt,[(s(!0),r(h,null,y(c.value,t=>(s(),r("div",{key:t.id,class:w(["p-4 rounded-lg border",B(t.status)])},[e("div",it,[e("span",lt,a(t.name),1),e("div",{class:w(["w-3 h-3 rounded-full",j(t.status)])},null,2)]),e("div",ct,a(t.value),1),e("div",ut,a(t.description),1)],2))),128))])]),e("div",xt,[e("div",gt,[i[1]||(i[1]=e("h4",{class:"text-sm font-medium text-gray-700 dark:text-dark-text-secondary"},"正在处理的任务",-1)),e("span",mt,a(b.value.length)+" 个任务",1)]),e("div",kt,[(s(!0),r(h,null,y(b.value,t=>(s(),r("div",{key:t.id,class:"p-3 bg-gray-50 dark:bg-dark-border rounded-lg"},[e("div",pt,[e("div",vt,[(s(),$(C(M(t.type)),{class:"w-4 h-4 text-gray-600 dark:text-dark-text-secondary"})),e("span",bt,a(t.name),1)]),e("span",ht,a(t.progress)+"%",1)]),e("div",yt,[e("div",{class:"bg-gradient-to-r from-blue-500 to-blue-600 h-2 rounded-full transition-all duration-300",style:U({width:`${t.progress}%`})},null,4)]),e("div",ft,[e("span",null,a(t.operator),1),e("span",null,"预计剩余: "+a(t.estimatedTime),1)])]))),128))])]),e("div",_t,[i[2]||(i[2]=e("h4",{class:"text-sm font-medium text-gray-700 dark:text-dark-text-secondary mb-4"},"资源使用情况",-1)),e("div",wt,[(s(!0),r(h,null,y(p.value,t=>(s(),r("div",{key:t.id,class:"flex items-center justify-between"},[e("div",$t,[(s(),$(C(t.icon),{class:"w-5 h-5 text-gray-600 dark:text-dark-text-secondary"})),e("div",null,[e("div",Ct,a(t.name),1),e("div",Mt,a(t.description),1)])]),e("div",jt,[e("div",Tt,a(t.usage)+"%",1),e("div",Bt,[e("div",{class:w(["h-2 rounded-full transition-all duration-300",A(t.usage)]),style:U({width:`${t.usage}%`})},null,6)])])]))),128))])]),e("div",null,[i[3]||(i[3]=e("h4",{class:"text-sm font-medium text-gray-700 dark:text-dark-text-secondary mb-4"},"最近活动",-1)),e("div",At,[(s(!0),r(h,null,y(_.value,t=>(s(),r("div",{key:t.id,class:"flex items-start space-x-3 p-2 hover:bg-gray-50 dark:hover:bg-dark-border rounded-lg transition-colors"},[e("div",{class:w(["w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0",f(t.type)])},[(s(),$(C(d(t.type)),{class:"w-4 h-4 text-white"}))],2),e("div",Vt,[e("div",St,a(t.message),1),e("div",Dt,a(t.time),1)])]))),128))])])]))}}),zt={class:"bg-white dark:bg-dark-card rounded-xl p-6 shadow-sm border border-gray-100 dark:border-dark-border"},Ut={class:"flex items-center justify-between mb-6"},Ht={class:"mb-6"},Lt={class:"flex items-center justify-between mb-3"},Zt={class:"text-xs text-gray-500 dark:text-dark-text-secondary"},Et={key:0,class:"text-center py-4"},Nt={key:1,class:"space-y-2 max-h-48 overflow-y-auto"},Pt=["onClick"],Rt={class:"flex items-center space-x-3"},Ft={class:"text-sm font-medium text-gray-900 dark:text-dark-text"},Gt={class:"text-xs text-gray-500 dark:text-dark-text-secondary"},qt={class:"mb-6"},Ot={class:"space-y-2"},Qt={class:"flex items-center space-x-3"},Jt={class:"text-sm text-gray-900 dark:text-dark-text"},Kt={class:"text-xs text-gray-500 dark:text-dark-text-secondary"},Wt={class:"flex items-center space-x-2"},Xt=["onClick"],Yt={class:"border-t border-gray-200 dark:border-dark-border pt-4"},es={class:"grid grid-cols-2 gap-2"},ts=["onClick"],ss={class:"mt-6 pt-4 border-t border-gray-200 dark:border-dark-border"},rs={class:"grid grid-cols-3 gap-4 text-center"},as={class:"text-lg font-bold text-gray-900 dark:text-dark-text"},os={class:"text-lg font-bold text-gray-900 dark:text-dark-text"},ns={class:"text-lg font-bold text-gray-900 dark:text-dark-text"},ds=H({__name:"CommonTasks",setup(v){const c=x([{id:"pending-1",title:"审核商品采集结果",type:"商品采集",status:"urgent",createTime:"30分钟前"},{id:"pending-2",title:"处理图片裁剪失败",type:"智能裁图",status:"normal",createTime:"1小时前"},{id:"pending-3",title:"确认批量刊登设置",type:"批量刊登",status:"normal",createTime:"2小时前"}]),b=x([{id:"recent-1",title:"Amazon商品采集 #12345",type:"product-collection",status:"completed",finishTime:"5分钟前"},{id:"recent-2",title:"智能裁图批处理 #12344",type:"smart-crop",status:"completed",finishTime:"15分钟前"},{id:"recent-3",title:"标题生成任务 #12343",type:"title-generator",status:"failed",finishTime:"25分钟前"},{id:"recent-4",title:"一键抠图任务 #12342",type:"one-click-cutout",status:"completed",finishTime:"35分钟前"}]),p=x([{id:"new-collection",name:"新建采集",icon:Q,action:"create-collection"},{id:"batch-process",name:"批量处理",icon:W,action:"batch-process"},{id:"view-gallery",name:"查看图库",icon:S,action:"view-gallery"},{id:"system-settings",name:"系统设置",icon:J,action:"system-settings"}]),_=x({completed:156,processing:23,pending:8}),j=i=>({urgent:"bg-red-500",normal:"bg-blue-500",pending:"bg-gray-500"})[i]||"bg-gray-500",B=i=>({"product-collection":D,"smart-crop":S,"title-generator":P,"one-click-cutout":S,"batch-listing":O})[i]||D,M=i=>({completed:"bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-400",failed:"bg-red-100 dark:bg-red-900/20 text-red-800 dark:text-red-400",cancelled:"bg-gray-100 dark:bg-gray-900/20 text-gray-800 dark:text-gray-400"})[i]||"bg-gray-100 text-gray-800",A=i=>({completed:"已完成",failed:"失败",cancelled:"已取消"})[i]||i,f=i=>{u.info(`处理待办事项: ${i.title}`)},d=i=>{u.info(`查看任务详情: ${i.title}`)},g=()=>{u.info("跳转到任务历史页面")},m=i=>{switch(i.action){case"create-collection":u.info("跳转到商品采集页面");break;case"batch-process":u.info("打开批量处理工具");break;case"view-gallery":u.info("跳转到图库管理页面");break;case"system-settings":u.info("跳转到系统设置页面");break;default:u.info(`执行操作: ${i.name}`)}},n=()=>{u.success("任务列表已刷新")};return(i,t)=>(s(),r("div",zt,[e("div",Ut,[t[0]||(t[0]=e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-dark-text"},"常用事务",-1)),e("button",{onClick:n,class:"text-sm text-gray-500 dark:text-dark-text-secondary hover:text-gray-700 dark:hover:text-dark-text"},[k(T(q),{class:"w-4 h-4"})])]),e("div",Ht,[e("div",Lt,[t[1]||(t[1]=e("h4",{class:"text-sm font-medium text-gray-700 dark:text-dark-text-secondary"},"待处理事项",-1)),e("span",Zt,a(c.value.length)+" 项",1)]),c.value.length===0?(s(),r("div",Et,[k(T(K),{class:"w-8 h-8 text-green-500 mx-auto mb-2"}),t[2]||(t[2]=e("p",{class:"text-sm text-gray-500 dark:text-dark-text-secondary"},"暂无待处理事项",-1))])):(s(),r("div",Nt,[(s(!0),r(h,null,y(c.value,o=>(s(),r("div",{key:o.id,class:"flex items-center justify-between p-3 bg-gray-50 dark:bg-dark-border rounded-lg hover:bg-gray-100 dark:hover:bg-dark-card transition-colors cursor-pointer",onClick:I=>f(o)},[e("div",Rt,[e("div",{class:w(["w-2 h-2 rounded-full",j(o.status)])},null,2),e("div",null,[e("div",Ft,a(o.title),1),e("div",Gt,a(o.type)+" · "+a(o.createTime),1)])]),k(T(E),{class:"w-4 h-4 text-gray-400"})],8,Pt))),128))]))]),e("div",qt,[e("div",{class:"flex items-center justify-between mb-3"},[t[3]||(t[3]=e("h4",{class:"text-sm font-medium text-gray-700 dark:text-dark-text-secondary"},"最近任务",-1)),e("button",{onClick:g,class:"text-xs text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300"}," 查看全部 ")]),e("div",Ot,[(s(!0),r(h,null,y(b.value,o=>(s(),r("div",{key:o.id,class:"flex items-center justify-between p-2 hover:bg-gray-50 dark:hover:bg-dark-border rounded-lg transition-colors"},[e("div",Qt,[(s(),$(C(B(o.type)),{class:"w-4 h-4 text-gray-600 dark:text-dark-text-secondary"})),e("div",null,[e("div",Jt,a(o.title),1),e("div",Kt,a(o.finishTime),1)])]),e("div",Wt,[e("span",{class:w(["inline-flex items-center px-2 py-0.5 rounded text-xs font-medium",M(o.status)])},a(A(o.status)),3),e("button",{onClick:I=>d(o),class:"text-xs text-gray-500 dark:text-dark-text-secondary hover:text-gray-700 dark:hover:text-dark-text"}," 详情 ",8,Xt)])]))),128))])]),e("div",Yt,[t[4]||(t[4]=e("h4",{class:"text-sm font-medium text-gray-700 dark:text-dark-text-secondary mb-3"},"快速操作",-1)),e("div",es,[(s(!0),r(h,null,y(p.value,o=>(s(),r("button",{key:o.id,onClick:I=>m(o),class:"flex items-center justify-center p-3 text-sm font-medium text-gray-700 dark:text-dark-text-secondary bg-gray-50 dark:bg-dark-border hover:bg-gray-100 dark:hover:bg-dark-card rounded-lg transition-colors"},[(s(),$(C(o.icon),{class:"w-4 h-4 mr-2"})),Z(" "+a(o.name),1)],8,ts))),128))])]),e("div",ss,[t[8]||(t[8]=e("h4",{class:"text-sm font-medium text-gray-700 dark:text-dark-text-secondary mb-3"},"今日统计",-1)),e("div",rs,[e("div",null,[e("div",as,a(_.value.completed),1),t[5]||(t[5]=e("div",{class:"text-xs text-gray-500 dark:text-dark-text-secondary"},"已完成",-1))]),e("div",null,[e("div",os,a(_.value.processing),1),t[6]||(t[6]=e("div",{class:"text-xs text-gray-500 dark:text-dark-text-secondary"},"处理中",-1))]),e("div",null,[e("div",ns,a(_.value.pending),1),t[7]||(t[7]=e("div",{class:"text-xs text-gray-500 dark:text-dark-text-secondary"},"待处理",-1))])])])]))}}),is={class:"bg-white dark:bg-dark-card rounded-xl p-6 shadow-sm border border-gray-100 dark:border-dark-border"},ls={class:"flex items-center justify-between mb-6"},cs={class:"grid grid-cols-1 gap-3 mb-6"},us=["onClick"],xs={class:"flex items-center space-x-3"},gs={class:"text-left"},ms={class:"font-medium"},ks={class:"text-sm opacity-90"},ps={class:"mb-6"},vs={class:"grid grid-cols-2 gap-2"},bs=["onClick"],hs={class:"text-xs font-medium text-gray-700 dark:text-dark-text-secondary"},ys={class:"mb-6"},fs={class:"space-y-2"},_s=["onClick"],ws={class:"flex items-center space-x-3"},$s={class:"text-left"},Cs={class:"text-sm font-medium text-gray-900 dark:text-dark-text"},Ms={class:"text-xs text-gray-500 dark:text-dark-text-secondary"},js={class:"border-t border-gray-200 dark:border-dark-border pt-4"},Ts={class:"grid grid-cols-3 gap-2"},Bs=["onClick","title"],As={class:"text-xs text-gray-600 dark:text-dark-text-secondary"},Vs={class:"space-y-4"},Ss={class:"grid grid-cols-2 gap-2"},Ds=["value"],Is={class:"text-sm text-gray-700 dark:text-dark-text-secondary"},zs={class:"flex items-center space-x-2 p-2 bg-gray-50 dark:bg-dark-border rounded-lg cursor-move"},Us={class:"text-sm text-gray-700 dark:text-dark-text-secondary"},Hs={class:"flex justify-end space-x-3"},Ls=H({__name:"QuickActions",setup(v){const c=x(!1),b=x([]),p=x([{id:"new-collection",name:"新建采集任务",description:"快速创建商品采集任务",icon:Q,action:"create-collection",gradient:"from-blue-500 to-blue-600"},{id:"batch-process",name:"批量处理图片",description:"智能裁图、抠图等批量操作",icon:S,action:"batch-image-process",gradient:"from-green-500 to-green-600"}]),_=x([{id:"gallery",name:"图库",description:"管理图片资源",icon:S,action:"open-gallery"},{id:"products",name:"商品",description:"商品管理",icon:D,action:"open-products"},{id:"titles",name:"标题",description:"标题生成",icon:P,action:"open-titles"},{id:"listing",name:"刊登",description:"批量刊登",icon:O,action:"open-listing"}]),j=x([{id:"batch-export",name:"批量导出数据",description:"导出选中的任务数据",icon:oe,action:"batch-export"},{id:"batch-import",name:"批量导入数据",description:"从文件导入数据",icon:le,action:"batch-import"},{id:"batch-delete",name:"批量清理任务",description:"清理过期或失败的任务",icon:he,action:"batch-cleanup"}]),B=x([{id:"reports",name:"报表",description:"查看数据报表",icon:X,action:"view-reports"},{id:"settings",name:"设置",description:"系统设置",icon:pe,action:"open-settings"},{id:"security",name:"安全",description:"安全中心",icon:ne,action:"security-center"},{id:"notifications",name:"通知",description:"消息通知",icon:ue,action:"view-notifications"},{id:"backup",name:"备份",description:"数据备份",icon:be,action:"data-backup"},{id:"logs",name:"日志",description:"系统日志",icon:W,action:"view-logs"}]),M=x([...p.value,..._.value,...j.value,...B.value]),A=F({get:()=>M.value.filter(t=>b.value.includes(t.id)),set:t=>{b.value=t.map(o=>o.id)}}),f=t=>{switch(t.action){case"create-collection":u.info("跳转到商品采集页面");break;case"batch-image-process":u.info("打开批量图片处理工具");break;default:u.info(`执行操作: ${t.name}`)}},d=t=>{switch(t.action){case"open-gallery":u.info("跳转到图库管理页面");break;case"open-products":u.info("跳转到商品管理页面");break;case"open-titles":u.info("跳转到标题生成页面");break;case"open-listing":u.info("跳转到批量刊登页面");break;default:u.info(`打开工具: ${t.name}`)}},g=t=>{switch(t.action){case"batch-export":u.info("开始批量导出数据");break;case"batch-import":u.info("打开批量导入工具");break;case"batch-cleanup":u.info("开始清理过期任务");break;default:u.info(`执行批量操作: ${t.name}`)}},m=t=>{switch(t.action){case"view-reports":u.info("跳转到数据报表页面");break;case"open-settings":u.info("跳转到系统设置页面");break;case"security-center":u.info("跳转到安全中心");break;case"view-notifications":u.info("打开消息通知");break;case"data-backup":u.info("开始数据备份");break;case"view-logs":u.info("查看系统日志");break;default:u.info(`执行系统工具: ${t.name}`)}},n=()=>{b.value=[...p.value.map(t=>t.id),..._.value.slice(0,4).map(t=>t.id)],c.value=!0},i=()=>{u.success("快捷操作设置已保存"),c.value=!1};return(t,o)=>{const I=L("el-button"),Y=L("el-dialog");return s(),r("div",is,[e("div",ls,[o[4]||(o[4]=e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-dark-text"},"快捷操作",-1)),e("button",{onClick:n,class:"text-sm text-gray-500 dark:text-dark-text-secondary hover:text-gray-700 dark:hover:text-dark-text"},[k(T(J),{class:"w-4 h-4"})])]),e("div",cs,[(s(!0),r(h,null,y(p.value,l=>(s(),r("button",{key:l.id,onClick:z=>f(l),class:w(["flex items-center justify-between p-4 bg-gradient-to-r rounded-lg text-white transition-all duration-200 hover:shadow-lg transform hover:scale-[1.02]",l.gradient])},[e("div",xs,[(s(),$(C(l.icon),{class:"w-6 h-6"})),e("div",gs,[e("div",ms,a(l.name),1),e("div",ks,a(l.description),1)])]),k(T(E),{class:"w-5 h-5 opacity-70"})],10,us))),128))]),e("div",ps,[o[5]||(o[5]=e("h4",{class:"text-sm font-medium text-gray-700 dark:text-dark-text-secondary mb-3"},"常用工具",-1)),e("div",vs,[(s(!0),r(h,null,y(_.value,l=>(s(),r("button",{key:l.id,onClick:z=>d(l),class:"flex flex-col items-center p-3 bg-gray-50 dark:bg-dark-border hover:bg-gray-100 dark:hover:bg-dark-card rounded-lg transition-colors"},[(s(),$(C(l.icon),{class:"w-6 h-6 text-gray-600 dark:text-dark-text-secondary mb-2"})),e("span",hs,a(l.name),1)],8,bs))),128))])]),e("div",ys,[o[6]||(o[6]=e("h4",{class:"text-sm font-medium text-gray-700 dark:text-dark-text-secondary mb-3"},"批量操作",-1)),e("div",fs,[(s(!0),r(h,null,y(j.value,l=>(s(),r("button",{key:l.id,onClick:z=>g(l),class:"w-full flex items-center justify-between p-3 bg-gray-50 dark:bg-dark-border hover:bg-gray-100 dark:hover:bg-dark-card rounded-lg transition-colors"},[e("div",ws,[(s(),$(C(l.icon),{class:"w-5 h-5 text-gray-600 dark:text-dark-text-secondary"})),e("div",$s,[e("div",Cs,a(l.name),1),e("div",Ms,a(l.description),1)])]),k(T(E),{class:"w-4 h-4 text-gray-400"})],8,_s))),128))])]),e("div",js,[o[7]||(o[7]=e("h4",{class:"text-sm font-medium text-gray-700 dark:text-dark-text-secondary mb-3"},"系统工具",-1)),e("div",Ts,[(s(!0),r(h,null,y(B.value,l=>(s(),r("button",{key:l.id,onClick:z=>m(l),class:"flex flex-col items-center p-2 hover:bg-gray-50 dark:hover:bg-dark-border rounded-lg transition-colors",title:l.description},[(s(),$(C(l.icon),{class:"w-5 h-5 text-gray-600 dark:text-dark-text-secondary mb-1"})),e("span",As,a(l.name),1)],8,Bs))),128))])]),k(Y,{modelValue:c.value,"onUpdate:modelValue":o[3]||(o[3]=l=>c.value=l),title:"自定义快捷操作",width:"600px"},{footer:V(()=>[e("div",Hs,[k(I,{onClick:o[2]||(o[2]=l=>c.value=!1)},{default:V(()=>o[11]||(o[11]=[Z("取消")])),_:1,__:[11]}),k(I,{type:"primary",onClick:i},{default:V(()=>o[12]||(o[12]=[Z(" 保存设置 ")])),_:1,__:[12]})])]),default:V(()=>[e("div",Vs,[e("div",null,[o[8]||(o[8]=e("h4",{class:"text-sm font-medium text-gray-700 dark:text-dark-text-secondary mb-2"},"选择要显示的操作",-1)),e("div",Ss,[(s(!0),r(h,null,y(M.value,l=>(s(),r("label",{key:l.id,class:"flex items-center space-x-2 p-2 border rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-dark-border"},[se(e("input",{type:"checkbox",value:l.id,"onUpdate:modelValue":o[0]||(o[0]=z=>b.value=z),class:"text-blue-600 focus:ring-blue-500"},null,8,Ds),[[re,b.value]]),(s(),$(C(l.icon),{class:"w-4 h-4 text-gray-600 dark:text-dark-text-secondary"})),e("span",Is,a(l.name),1)]))),128))])]),e("div",null,[o[9]||(o[9]=e("h4",{class:"text-sm font-medium text-gray-700 dark:text-dark-text-secondary mb-2"},"操作顺序",-1)),o[10]||(o[10]=e("p",{class:"text-xs text-gray-500 dark:text-dark-text-secondary mb-2"},"拖拽调整操作顺序",-1)),k(T(ae),{modelValue:A.value,"onUpdate:modelValue":o[1]||(o[1]=l=>A.value=l),"item-key":"id",class:"space-y-2"},{item:V(({element:l})=>[e("div",zs,[k(T(ce),{class:"w-4 h-4 text-gray-400"}),(s(),$(C(l.icon),{class:"w-4 h-4 text-gray-600 dark:text-dark-text-secondary"})),e("span",Us,a(l.name),1)])]),_:1},8,["modelValue"])])])]),_:1},8,["modelValue"])])}}}),Zs={class:"p-6 bg-gray-50 dark:bg-dark-bg min-h-screen"},Es={class:"flex items-center justify-between mb-8"},Ns={class:"text-gray-600 dark:text-dark-text-secondary mt-1"},Ps={class:"flex items-center space-x-4"},Rs=["loading"],Fs={class:"text-sm text-gray-500 dark:text-dark-text-secondary"},Gs={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"},qs={class:"flex items-center justify-between"},Os={class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},Qs={class:"text-2xl font-bold text-gray-900 dark:text-dark-text mt-1"},Js={class:"flex items-center mt-2"},Ks={class:"grid grid-cols-1 lg:grid-cols-3 gap-8"},Ws={class:"lg:col-span-2 space-y-8"},Xs={class:"space-y-8"},lr=H({__name:"dashboard",setup(v){const c=x(!1),b=x(""),p=x(""),_=x({nickname:"张三",avatar:"/default-avatar.png"}),j=x([{id:"total-tasks",label:"今日任务",value:"156",change:"+12.5%",trend:"up",icon:X,iconBg:"bg-blue-100 dark:bg-blue-900/20",iconColor:"text-blue-600 dark:text-blue-400"},{id:"processing",label:"处理中",value:"23",change:"+8.2%",trend:"up",icon:D,iconBg:"bg-amber-100 dark:bg-amber-900/20",iconColor:"text-amber-600 dark:text-amber-400"},{id:"completed",label:"已完成",value:"128",change:"+15.3%",trend:"up",icon:de,iconBg:"bg-green-100 dark:bg-green-900/20",iconColor:"text-green-600 dark:text-green-400"},{id:"revenue",label:"今日支出",value:"¥2,456",change:"-3.1%",trend:"down",icon:ie,iconBg:"bg-purple-100 dark:bg-purple-900/20",iconColor:"text-purple-600 dark:text-purple-400"}]),B=d=>d.toLocaleString("zh-CN",{year:"numeric",month:"long",day:"numeric",weekday:"long",hour:"2-digit",minute:"2-digit"}),M=()=>{const d=new Date;b.value=B(d),p.value=d.toLocaleTimeString("zh-CN")},A=async()=>{c.value=!0;try{await new Promise(d=>setTimeout(d,1e3)),M(),j.value.forEach(d=>{const g=parseFloat((Math.random()*20-10).toFixed(1));d.change=`${g>0?"+":""}${g}%`,d.trend=g>0?"up":"down"})}finally{c.value=!1}};let f;return N(()=>{M(),f=setInterval(M,6e4)}),G(()=>{f&&clearInterval(f)}),(d,g)=>(s(),r("div",Zs,[e("div",Es,[e("div",null,[g[0]||(g[0]=e("h1",{class:"text-3xl font-bold text-gray-900 dark:text-dark-text"},"驾驶舱",-1)),e("p",Ns,a(b.value)+" · 欢迎回来，"+a(_.value.nickname),1)]),e("div",Ps,[e("button",{onClick:A,loading:c.value,class:"inline-flex items-center px-4 py-2 text-sm font-medium text-gray-600 dark:text-dark-text-secondary hover:text-gray-900 dark:hover:text-dark-text bg-white dark:bg-dark-card hover:bg-gray-50 dark:hover:bg-dark-border rounded-lg shadow-sm transition-all duration-200"},[k(T(q),{class:w(["w-4 h-4 mr-2",{"animate-spin":c.value}])},null,8,["class"]),g[1]||(g[1]=Z(" 刷新数据 "))],8,Rs),e("div",Fs," 最后更新: "+a(p.value),1)])]),e("div",Gs,[(s(!0),r(h,null,y(j.value,m=>(s(),r("div",{key:m.id,class:"bg-white dark:bg-dark-card rounded-xl p-6 shadow-sm border border-gray-100 dark:border-dark-border hover:shadow-md transition-all duration-200"},[e("div",qs,[e("div",null,[e("p",Os,a(m.label),1),e("p",Qs,a(m.value),1),e("div",Js,[(s(),$(C(m.trend==="up"?T(ge):T(xe)),{class:w(["w-4 h-4 mr-1",[m.trend==="up"?"text-green-500":"text-red-500"]])},null,8,["class"])),e("span",{class:w(["text-sm font-medium",[m.trend==="up"?"text-green-600 dark:text-green-400":"text-red-600 dark:text-red-400"]])},a(m.change),3),g[2]||(g[2]=e("span",{class:"text-sm text-gray-500 dark:text-dark-text-secondary ml-1"},"vs 昨日",-1))])]),e("div",{class:w(["w-12 h-12 rounded-lg flex items-center justify-center",m.iconBg])},[(s(),$(C(m.icon),{class:w(["w-6 h-6",m.iconColor])},null,8,["class"]))],2)])]))),128))]),e("div",Ks,[e("div",Ws,[k(Ls),k(at)]),e("div",Xs,[k(ds),k(It)])])]))}});export{lr as default};
