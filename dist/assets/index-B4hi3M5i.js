import{d as u,r,c as m,a as e,w as p,b as a,v as d,u as f,o as c,i as g}from"./index-84KJ6UvQ.js";const b={class:"flex items-center justify-center min-h-screen bg-gray-100"},x={class:"w-full max-w-md p-8 space-y-6 bg-white rounded-lg shadow-md"},v={class:"mt-1"},w={class:"mt-1"},_=u({__name:"index",setup(y){const o=r(""),t=r(""),l=f(),i=()=>{o.value&&t.value?(localStorage.setItem("user-token","mock-token"),g.value=!0,l.push("/dashboard")):alert("请输入用户名和密码")};return(h,s)=>(c(),m("div",b,[e("div",x,[s[5]||(s[5]=e("h2",{class:"text-2xl font-bold text-center text-gray-900"},"登录",-1)),e("form",{class:"space-y-6",onSubmit:p(i,["prevent"])},[e("div",null,[s[2]||(s[2]=e("label",{for:"username",class:"block text-sm font-medium text-gray-700"},"用户名",-1)),e("div",v,[a(e("input",{id:"username",name:"username",type:"text",required:"","onUpdate:modelValue":s[0]||(s[0]=n=>o.value=n),class:"w-full px-3 py-2 placeholder-gray-400 border border-gray-300 rounded-md shadow-sm appearance-none focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"},null,512),[[d,o.value]])])]),e("div",null,[s[3]||(s[3]=e("label",{for:"password",class:"block text-sm font-medium text-gray-700"},"密码",-1)),e("div",w,[a(e("input",{id:"password",name:"password",type:"password",required:"","onUpdate:modelValue":s[1]||(s[1]=n=>t.value=n),class:"w-full px-3 py-2 placeholder-gray-400 border border-gray-300 rounded-md shadow-sm appearance-none focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"},null,512),[[d,t.value]])])]),s[4]||(s[4]=e("div",null,[e("button",{type:"submit",class:"w-full px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"}," 登录 ")],-1))],32)])]))}});export{_ as default};
