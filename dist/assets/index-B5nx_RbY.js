import{c as o,a as e,o as t,d as K,e as T,n as P,q as I,M as A,h as V,j as h,C as w,A as N,t as l,_ as Q,w as re,g as u,F as E,k as D,r as x,L as Ie,p as xe,b as ke,N as Ve,E as X,x as be,l as Ne,H as Oe,f as Se,v as Ee,s as me}from"./index-BTucjZQh.js";import{B as Z}from"./billing-Ciq-WFQK.js";import{r as ge}from"./CheckCircleIcon-CsS0iB1X.js";import{r as _e}from"./InformationCircleIcon-DtxfjwNf.js";import{r as De}from"./CheckIcon-CDTB2c1-.js";import{r as ze}from"./ChartBarIcon-bkXzkP-U.js";import{r as je}from"./MagnifyingGlassIcon-TSA1g2un.js";function Pe(n,p){return t(),o("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M19.5 13.5 12 21m0 0-7.5-7.5M12 21V3"})])}function Ue(n,p){return t(),o("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M4.5 10.5 12 3m0 0 7.5 7.5M12 3v18"})])}function Fe(n,p){return t(),o("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m3.75 13.5 10.5-11.25L12 10.5h8.25L9.75 21.75 12 13.5H3.75Z"})])}function Be(n,p){return t(),o("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M12 6v12m-3-2.818.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.45-.22-2.003-.659-1.106-.879-1.106-2.303 0-3.182s2.9-.879 4.006 0l.415.33M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"})])}function Le(n,p){return t(),o("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"}),e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"})])}function we(n,p){return t(),o("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M21 8.25c0-2.485-2.099-4.5-4.688-4.5-1.935 0-3.597 1.126-4.312 2.733-.715-1.607-2.377-2.733-4.313-2.733C5.1 3.75 3 5.765 3 8.25c0 7.22 9 12 9 12s9-4.78 9-12Z"})])}function Ye(n,p){return t(),o("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m15.75 15.75-2.489-2.489m0 0a3.375 3.375 0 1 0-4.773-4.773 3.375 3.375 0 0 0 4.774 4.774ZM21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"})])}function Re(n,p){return t(),o("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L6.832 19.82a4.5 4.5 0 0 1-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 0 1 1.13-1.897L16.863 4.487Zm0 0L19.5 7.125"})])}function He(n,p){return t(),o("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3.75 3v11.25A2.25 2.25 0 0 0 6 16.5h2.25M3.75 3h-1.5m1.5 0h16.5m0 0h1.5m-1.5 0v11.25A2.25 2.25 0 0 1 18 16.5h-2.25m-7.5 0h7.5m-7.5 0-1 3m8.5-3 1 3m0 0 .5 1.5m-.5-1.5h-9.5m0 0-.5 1.5m.75-9 3-3 2.148 2.148A12.061 12.061 0 0 1 16.5 7.605"})])}function oe(n,p){return t(),o("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M11.48 3.499a.562.562 0 0 1 1.04 0l2.125 5.111a.563.563 0 0 0 .475.345l5.518.442c.499.04.701.663.321.988l-4.204 3.602a.563.563 0 0 0-.182.557l1.285 5.385a.562.562 0 0 1-.84.61l-4.725-2.885a.562.562 0 0 0-.586 0L6.982 20.54a.562.562 0 0 1-.84-.61l1.285-5.386a.562.562 0 0 0-.182-.557l-4.204-3.602a.562.562 0 0 1 .321-.988l5.518-.442a.563.563 0 0 0 .475-.345L11.48 3.5Z"})])}const Ge={key:0,class:"space-y-1 flex flex-col items-center"},Ze={class:"flex items-baseline space-x-2"},Je={key:1,class:"space-y-1 flex flex-col items-center"},qe={class:"flex items-baseline space-x-2"},Ke={class:"price-text text-primary-600 dark:text-primary-400 font-bold"},Qe={key:0,class:"original-price text-gray-400 dark:text-gray-500 line-through text-sm"},We={key:2,class:"space-y-1 flex flex-col items-center"},Xe={class:"flex items-baseline space-x-2"},et={class:"price-text text-purple-600 dark:text-purple-400 font-bold"},tt={class:"price-unit text-gray-500 dark:text-dark-text-secondary"},at={key:0,class:"original-price text-gray-400 dark:text-gray-500 line-through text-sm"},st={class:"price-desc text-gray-500 dark:text-dark-text-secondary"},rt={key:3,class:"space-y-1 flex flex-col items-center"},ot={class:"flex items-baseline space-x-2"},nt={class:"price-text text-orange-600 dark:text-orange-400 font-bold"},lt={class:"price-unit text-gray-500 dark:text-dark-text-secondary"},it={class:"price-desc text-gray-500 dark:text-dark-text-secondary"},dt={key:4,class:"space-y-1"},pt=K({__name:"PriceDisplay",props:{price:{},size:{},showDescription:{type:Boolean}},setup(n){const p=n,i=T(()=>{switch(p.size||"medium"){case"small":return"price-small";case"large":return"price-large";default:return"price-medium"}});return(s,a)=>{const c=V("el-tag");return t(),o("div",{class:P(["price-display",i.value])},[s.price.type==="free"?(t(),o("div",Ge,[e("div",Ze,[a[1]||(a[1]=e("span",{class:"price-text text-green-600 dark:text-green-400 font-bold"}," 免费 ",-1)),s.size!=="small"?(t(),I(c,{key:0,type:"success",size:"small",effect:"plain"},{default:h(()=>a[0]||(a[0]=[w(" FREE ")])),_:1,__:[0]})):A("",!0)])])):s.price.type===N(Z).ONE_TIME?(t(),o("div",Je,[e("div",qe,[e("span",Ke," ¥"+l(s.price.amount),1),s.price.originalAmount&&s.price.originalAmount>s.price.amount?(t(),o("span",Qe," ¥"+l(s.price.originalAmount),1)):A("",!0)]),a[2]||(a[2]=e("div",{class:"price-desc text-gray-500 dark:text-dark-text-secondary"}," 一次购买，终身使用 ",-1)),s.price.originalAmount&&s.price.originalAmount>s.price.amount&&s.size!=="small"?(t(),I(c,{key:0,type:"danger",size:"small",effect:"plain",class:"discount-tag"},{default:h(()=>[w(l(Math.round((1-s.price.amount/s.price.originalAmount)*100))+"% OFF ",1)]),_:1})):A("",!0)])):s.price.type==="monthly"?(t(),o("div",We,[e("div",Xe,[e("span",et," ¥"+l(s.price.amount),1),e("span",tt,l(s.price.unit||"/月"),1),s.price.originalAmount&&s.price.originalAmount>s.price.amount?(t(),o("span",at," ¥"+l(s.price.originalAmount),1)):A("",!0)]),e("div",st,l(s.price.description||"按月订阅，随时取消"),1),s.price.originalAmount&&s.price.originalAmount>s.price.amount&&s.size!=="small"?(t(),I(c,{key:0,type:"warning",size:"small",effect:"plain",class:"discount-tag"},{default:h(()=>a[3]||(a[3]=[w(" 限时优惠 ")])),_:1,__:[3]})):A("",!0)])):s.price.type==="per_use"?(t(),o("div",rt,[e("div",ot,[e("span",nt," ¥"+l(s.price.amount),1),e("span",lt,l(s.price.unit||"/次"),1)]),e("div",it,l(s.price.description||"按使用次数计费"),1),s.size!=="small"?(t(),I(c,{key:0,type:"info",size:"small",effect:"plain"},{default:h(()=>a[4]||(a[4]=[w(" 按需付费 ")])),_:1,__:[4]})):A("",!0)])):(t(),o("div",dt,a[5]||(a[5]=[e("span",{class:"price-text text-gray-600 dark:text-gray-400"}," 价格待定 ",-1)])))],2)}}}),pe=Q(pt,[["__scopeId","data-v-fd3c1118"]]),ct={class:"relative p-6 flex-1 flex flex-col"},ut={class:"flex justify-center mb-4"},mt={class:"w-20 h-20 bg-gradient-to-br from-primary-500 to-blue-500 rounded-2xl flex items-center justify-center text-3xl font-bold text-white shadow-lg transform group-hover:scale-105 transition-transform duration-300"},gt={class:"text-center mb-4"},vt={class:"flex items-center justify-center space-x-2 mb-2"},yt={class:"text-xl font-bold text-gray-900 dark:text-dark-text"},ht={class:"text-sm text-gray-500 dark:text-dark-text-secondary mb-3"},ft={class:"flex items-center justify-center space-x-2 mb-3"},xt={class:"flex items-center"},kt={class:"text-sm font-medium text-gray-700 dark:text-dark-text"},bt={class:"text-xs text-gray-500 dark:text-dark-text-secondary"},_t={class:"mb-4"},wt={class:"text-sm text-gray-600 dark:text-dark-text-secondary line-clamp-2 leading-relaxed text-center"},$t={class:"flex-1 flex items-end"},Ct={class:"w-full"},At={class:"flex flex-wrap justify-center gap-2"},Mt={class:"px-6 pb-6 mt-auto"},Tt={class:"text-center mb-4"},It={class:"flex flex-col space-y-2"},Vt={class:"flex space-x-2 hidden"},Nt=K({__name:"AppCard",props:{app:{}},emits:["click","favorite-toggle","install-toggle","purchase"],setup(n){const p=n,i=()=>{switch(p.app.price.type){case"free":return"安装";case Z.ONE_TIME:return"购买";case"monthly":return"订阅";case"per_use":return"充值";default:return"安装"}},s=a=>a>=1e4?`${(a/1e4).toFixed(1)}万`:a>=1e3?`${(a/1e3).toFixed(1)}k`:a.toString();return(a,c)=>{const O=V("el-tag");return t(),o("div",{class:"app-card group relative bg-white dark:bg-dark-surface rounded-2xl shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-xl dark:hover:shadow-2xl transition-all duration-300 cursor-pointer overflow-hidden transform hover:-translate-y-1 flex flex-col h-full",onClick:c[4]||(c[4]=f=>a.$emit("click",a.app))},[c[8]||(c[8]=e("div",{class:"absolute top-0 left-0 right-0 h-24 bg-gradient-to-br from-primary-500/10 to-blue-500/10 dark:from-primary-400/20 dark:to-blue-400/20"},null,-1)),e("button",{onClick:c[0]||(c[0]=re(f=>a.$emit("favorite-toggle",a.app.id),["stop"])),class:P(["absolute top-4 right-4 z-10 p-2 rounded-full bg-white/90 dark:bg-dark-surface/90 backdrop-blur-md shadow-lg border border-white/20 dark:border-dark-border/50 hover:bg-white dark:hover:bg-dark-surface transition-all duration-200 group/fav",a.app.isFavorited?"text-red-500 hover:text-red-600":"text-gray-400 hover:text-red-500"])},[u(N(we),{class:P(["w-4 h-4 transition-transform duration-200 group-hover/fav:scale-110",a.app.isFavorited?"fill-current":""])},null,8,["class"])],2),e("div",ct,[e("div",ut,[e("div",mt,l(a.app.icon),1)]),e("div",gt,[e("div",vt,[e("h3",yt,l(a.app.name),1),a.app.status==="maintenance"?(t(),I(O,{key:0,type:"warning",size:"small",effect:"light",round:""},{default:h(()=>c[5]||(c[5]=[w(" 维护中 ")])),_:1,__:[5]})):a.app.status==="deprecated"?(t(),I(O,{key:1,type:"danger",size:"small",effect:"light",round:""},{default:h(()=>c[6]||(c[6]=[w(" 已废弃 ")])),_:1,__:[6]})):A("",!0)]),e("p",ht,l(a.app.developer),1),e("div",ft,[e("div",xt,[(t(),o(E,null,D(5,f=>u(N(oe),{key:f,class:P(["w-4 h-4",f<=Math.floor(a.app.rating)?"text-yellow-400 fill-current":"text-gray-300 dark:text-gray-600"])},null,8,["class"])),64))]),e("span",kt,l(a.app.rating),1),e("span",bt," ("+l(s(a.app.downloadCount))+") ",1)])]),e("div",_t,[e("p",wt,l(a.app.description),1)]),e("div",$t,[e("div",Ct,[e("div",At,[(t(!0),o(E,null,D(a.app.tags.slice(0,3),f=>(t(),I(O,{key:f,size:"small",effect:"light",round:"",class:"text-xs"},{default:h(()=>[w(l(f),1)]),_:2},1024))),128)),a.app.tags.length>3?(t(),I(O,{key:0,size:"small",effect:"plain",round:"",class:"text-xs"},{default:h(()=>[w(" +"+l(a.app.tags.length-3),1)]),_:1})):A("",!0)])])])]),e("div",Mt,[e("div",Tt,[u(pe,{price:a.app.price,size:"medium"},null,8,["price"])]),e("div",It,[a.app.isInstalled?(t(),o("button",{key:1,onClick:c[2]||(c[2]=re(f=>a.$emit("install-toggle",a.app.id),["stop"])),class:"w-full py-3 px-4 bg-green-500 hover:bg-green-600 text-white font-medium rounded-xl transition-all duration-200 flex items-center justify-center space-x-2"},c[7]||(c[7]=[e("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 13l4 4L19 7"})],-1),e("span",null,"已安装",-1)]))):(t(),o("button",{key:0,onClick:c[1]||(c[1]=re(f=>a.$emit("purchase",a.app),["stop"])),class:"w-full py-3 px-4 bg-gradient-to-r from-primary-500 to-blue-500 hover:from-primary-600 hover:to-blue-600 text-white font-medium rounded-xl transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl"},l(i()),1)),e("div",Vt,[e("button",{onClick:c[3]||(c[3]=re(f=>a.$emit("click",a.app),["stop"])),class:"flex-1 py-2 px-3 text-sm font-medium text-gray-600 dark:text-dark-text-secondary hover:text-primary-600 dark:hover:text-primary-400 bg-gray-100 dark:bg-dark-card hover:bg-gray-200 dark:hover:bg-dark-border rounded-lg transition-all duration-200"}," 详情 ")])])]),c[9]||(c[9]=e("div",{class:"absolute inset-0 bg-gradient-to-t from-primary-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none rounded-2xl"},null,-1))])}}}),Ot=Q(Nt,[["__scopeId","data-v-53fce589"]]),St={class:"app-grid-container"},Et={key:0,class:"loading-container"},Dt={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"},zt={key:1,class:"apps-container"},jt={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8"},Pt={key:0,class:"pagination-container mt-8"},Ut={key:2,class:"empty-state"},Ft={class:"text-center py-16"},Bt={class:"text-lg font-medium text-gray-900 dark:text-dark-text mb-2"},Lt={class:"text-gray-500 dark:text-dark-text-secondary max-w-md mx-auto"},Yt={class:"mt-6"},Rt=K({__name:"AppGrid",props:{apps:{},loading:{type:Boolean},emptyTitle:{},emptyDescription:{}},emits:["app-click","favorite-toggle","install-toggle","purchase","clear-filters"],setup(n){const p=n,i=x(1),s=x(12),a=T(()=>Math.ceil(p.apps.length/s.value)),c=T(()=>{const _=(i.value-1)*s.value,k=_+s.value;return p.apps.slice(_,k)}),O=T(()=>p.emptyTitle||"暂无应用"),f=T(()=>p.emptyDescription||"当前筛选条件下没有找到相关应用，请尝试调整筛选条件或搜索关键词。"),z=_=>{s.value=_,i.value=1},U=_=>{i.value=_,window.scrollTo({top:0,behavior:"smooth"})};return Ie(()=>p.apps.length,()=>{i.value=1}),(_,k)=>{const F=V("el-pagination"),B=V("el-button"),y=V("el-backtop");return t(),o("div",St,[_.loading?(t(),o("div",Et,[e("div",Dt,[(t(),o(E,null,D(8,d=>e("div",{key:d,class:"app-card-skeleton bg-white dark:bg-dark-surface rounded-xl border border-gray-200 dark:border-dark-border overflow-hidden"},k[6]||(k[6]=[xe('<div class="p-6 space-y-4" data-v-28b3ab2e><div class="flex items-start space-x-4" data-v-28b3ab2e><div class="w-16 h-16 bg-gray-200 dark:bg-gray-700 rounded-xl animate-pulse" data-v-28b3ab2e></div><div class="flex-1 space-y-2" data-v-28b3ab2e><div class="h-5 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" data-v-28b3ab2e></div><div class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 animate-pulse" data-v-28b3ab2e></div></div></div><div class="space-y-2" data-v-28b3ab2e><div class="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" data-v-28b3ab2e></div><div class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-5/6 animate-pulse" data-v-28b3ab2e></div></div><div class="flex space-x-2" data-v-28b3ab2e><div class="h-6 w-16 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" data-v-28b3ab2e></div><div class="h-6 w-12 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" data-v-28b3ab2e></div></div></div><div class="px-6 py-4 bg-gray-50 dark:bg-dark-card/50 border-t border-gray-100 dark:border-dark-border" data-v-28b3ab2e><div class="flex items-center justify-between" data-v-28b3ab2e><div class="h-6 w-20 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" data-v-28b3ab2e></div><div class="h-8 w-16 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" data-v-28b3ab2e></div></div></div>',2)]))),64))])])):_.apps.length>0?(t(),o("div",zt,[e("div",jt,[(t(!0),o(E,null,D(c.value,d=>(t(),I(Ot,{key:d.id,app:d,onClick:r=>_.$emit("app-click",d),onFavoriteToggle:k[0]||(k[0]=r=>_.$emit("favorite-toggle",r)),onInstallToggle:k[1]||(k[1]=r=>_.$emit("install-toggle",r)),onPurchase:k[2]||(k[2]=r=>_.$emit("purchase",r))},null,8,["app","onClick"]))),128))]),a.value>1?(t(),o("div",Pt,[u(F,{"current-page":i.value,"onUpdate:currentPage":k[3]||(k[3]=d=>i.value=d),"page-size":s.value,"onUpdate:pageSize":k[4]||(k[4]=d=>s.value=d),"page-sizes":[12,24,36,48],total:_.apps.length,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:z,onCurrentChange:U,class:"modern-pagination"},null,8,["current-page","page-size","total"])])):A("",!0)])):(t(),o("div",Ut,[e("div",Ft,[k[8]||(k[8]=e("div",{class:"w-24 h-24 mx-auto mb-6 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center"},[e("svg",{class:"w-12 h-12 text-gray-400 dark:text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2M4 13h2m13-8V4a1 1 0 00-1-1H7a1 1 0 00-1 1v1m8 0V4.5"})])],-1)),e("h3",Bt,l(O.value),1),e("p",Lt,l(f.value),1),e("div",Yt,[u(B,{onClick:k[5]||(k[5]=d=>_.$emit("clear-filters")),type:"primary",plain:""},{default:h(()=>k[7]||(k[7]=[w(" 清除筛选条件 ")])),_:1,__:[7]})])])])),u(y,{right:40,bottom:40,"visibility-height":300})])}}}),ve=Q(Rt,[["__scopeId","data-v-28b3ab2e"]]),Ht={key:0,class:"purchase-content"},Gt={class:"app-info flex items-center space-x-4 mb-6 p-4 bg-gray-50 dark:bg-dark-card rounded-lg"},Zt={class:"w-16 h-16 bg-gradient-to-br from-primary-100 to-primary-200 dark:from-primary-900/30 dark:to-primary-800/30 rounded-xl flex items-center justify-center text-2xl font-bold border border-primary-200 dark:border-primary-700"},Jt={class:"flex-1"},qt={class:"text-lg font-semibold text-gray-900 dark:text-dark-text"},Kt={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},Qt={class:"price-section mb-6"},Wt={class:"bg-white dark:bg-dark-surface border border-gray-200 dark:border-dark-border rounded-lg p-4"},Xt={key:0,class:"mt-4 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg"},ea={class:"flex items-center space-x-2"},ta={key:1,class:"mt-4 space-y-2"},aa={class:"flex items-center justify-between text-sm"},sa={class:"font-medium text-gray-900 dark:text-dark-text"},ra={key:0,class:"flex items-center justify-between text-sm"},oa={class:"text-gray-400 line-through"},na={class:"flex items-center justify-between text-sm font-medium border-t border-gray-200 dark:border-dark-border pt-2"},la={class:"text-lg text-primary-600 dark:text-primary-400"},ia={key:2,class:"mt-4 space-y-3"},da={class:"p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg"},pa={class:"flex items-center space-x-2 mb-2"},ca={class:"flex items-center justify-between text-sm font-medium border-t border-gray-200 dark:border-dark-border pt-2"},ua={class:"text-lg text-primary-600 dark:text-primary-400"},ma={key:3,class:"mt-4 space-y-3"},ga={class:"p-3 bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-lg"},va={class:"flex items-center space-x-2 mb-2"},ya={class:"space-y-2"},ha={class:"flex items-center justify-between text-sm"},fa={class:"font-medium text-gray-900 dark:text-dark-text"},xa={class:"flex items-center justify-between text-sm"},ka={class:"flex items-center justify-between text-sm"},ba={class:"font-medium text-gray-900 dark:text-dark-text"},_a={key:0,class:"payment-section mb-6"},wa={class:"grid grid-cols-3 gap-3"},$a=["value"],Ca={class:"flex flex-col items-center space-y-2"},Aa={class:"text-3xl"},Ma={class:"font-medium text-gray-900 dark:text-dark-text text-sm"},Ta={class:"text-xs text-gray-500 dark:text-dark-text-secondary mt-1"},Ia={key:0,class:"text-primary-600 dark:text-primary-400 mt-2"},Va={key:1,class:"agreement-section mb-6"},Na={class:"flex items-start space-x-3 cursor-pointer"},Oa={class:"flex justify-end space-x-3"},Sa=K({__name:"PurchaseDialog",props:{modelValue:{type:Boolean},app:{}},emits:["update:modelValue","purchase-success"],setup(n,{emit:p}){const i=n,s=p,a=x(!1),c=x("alipay"),O=x(!1),f=x(50),z=T({get:()=>i.modelValue,set:y=>s("update:modelValue",y)}),U=T(()=>i.app?i.app.price.type==="free"?!0:O.value&&c.value:!1),_=x([{id:"alipay",name:"支付宝",icon:"💙",description:"推荐使用，安全快捷"},{id:"wechat",name:"微信支付",icon:"💚",description:"微信扫码支付"},{id:"unionpay",name:"银联支付",icon:"💳",description:"银行卡支付"}]),k=()=>{if(!i.app)return"购买应用";switch(i.app.price.type){case"free":return"安装免费应用";case Z.ONE_TIME:return"购买应用";case"monthly":return"订阅应用";case"per_use":return"充值使用";default:return"购买应用"}},F=()=>{if(!i.app)return"确认";switch(i.app.price.type){case"free":return"立即安装";case Z.ONE_TIME:return`立即购买 ¥${i.app.price.amount}`;case"monthly":return`立即订阅 ¥${i.app.price.amount}/月`;case"per_use":return`充值 ¥${f.value}`;default:return"确认购买"}},B=async()=>{if(!(!i.app||!U.value)){a.value=!0;try{await new Promise(y=>setTimeout(y,2e3)),i.app.price.type==="free"?X.success(`${i.app.name} 安装成功！`):X.success(`${i.app.name} 购买成功！`),s("purchase-success",i.app.id),z.value=!1}catch{X.error("操作失败，请重试")}finally{a.value=!1}}};return(y,d)=>{const r=V("el-input-number"),v=V("el-checkbox"),Y=V("el-button"),R=V("el-dialog");return t(),I(R,{modelValue:z.value,"onUpdate:modelValue":d[4]||(d[4]=$=>z.value=$),title:k(),width:"600px","align-center":"","close-on-click-modal":!1,class:"purchase-dialog"},{footer:h(()=>[e("div",Oa,[u(Y,{onClick:d[3]||(d[3]=$=>z.value=!1),size:"large"},{default:h(()=>d[20]||(d[20]=[w(" 取消 ")])),_:1,__:[20]}),u(Y,{onClick:B,type:"primary",size:"large",loading:a.value,disabled:!U.value},{default:h(()=>[w(l(F()),1)]),_:1},8,["loading","disabled"])])]),default:h(()=>[y.app?(t(),o("div",Ht,[e("div",Gt,[e("div",Zt,l(y.app.icon),1),e("div",Jt,[e("h3",qt,l(y.app.name),1),e("p",Kt,l(y.app.developer),1)])]),e("div",Qt,[d[17]||(d[17]=e("h4",{class:"text-base font-medium text-gray-900 dark:text-dark-text mb-3"}," 价格详情 ",-1)),e("div",Wt,[u(pe,{price:y.app.price,size:"large"},null,8,["price"]),y.app.price.type==="free"?(t(),o("div",Xt,[e("div",ea,[u(N(ge),{class:"w-5 h-5 text-green-600 dark:text-green-400"}),d[5]||(d[5]=e("span",{class:"text-sm text-green-800 dark:text-green-300"}," 此应用完全免费，无需付费即可使用所有功能 ",-1))])])):y.app.price.type===N(Z).ONE_TIME?(t(),o("div",ta,[e("div",aa,[d[6]||(d[6]=e("span",{class:"text-gray-600 dark:text-dark-text-secondary"},"应用价格",-1)),e("span",sa,"¥"+l(y.app.price.amount),1)]),y.app.price.originalAmount&&y.app.price.originalAmount>y.app.price.amount?(t(),o("div",ra,[d[7]||(d[7]=e("span",{class:"text-gray-600 dark:text-dark-text-secondary"},"原价",-1)),e("span",oa,"¥"+l(y.app.price.originalAmount),1)])):A("",!0),e("div",na,[d[8]||(d[8]=e("span",{class:"text-gray-900 dark:text-dark-text"},"总计",-1)),e("span",la,"¥"+l(y.app.price.amount),1)])])):y.app.price.type==="monthly"?(t(),o("div",ia,[e("div",da,[e("div",pa,[u(N(_e),{class:"w-5 h-5 text-blue-600 dark:text-blue-400"}),d[9]||(d[9]=e("span",{class:"text-sm font-medium text-blue-800 dark:text-blue-300"},"订阅说明",-1))]),d[10]||(d[10]=e("ul",{class:"text-sm text-blue-700 dark:text-blue-300 space-y-1"},[e("li",null,"• 按月自动续费，可随时取消"),e("li",null,"• 享受所有高级功能"),e("li",null,"• 优先技术支持"),e("li",null,"• 无使用次数限制")],-1))]),e("div",ca,[d[11]||(d[11]=e("span",{class:"text-gray-900 dark:text-dark-text"},"月费",-1)),e("span",ua,"¥"+l(y.app.price.amount)+"/月",1)])])):y.app.price.type==="per_use"?(t(),o("div",ma,[e("div",ga,[e("div",va,[u(N(Be),{class:"w-5 h-5 text-orange-600 dark:text-orange-400"}),d[12]||(d[12]=e("span",{class:"text-sm font-medium text-orange-800 dark:text-orange-300"},"按需付费",-1))]),d[13]||(d[13]=e("p",{class:"text-sm text-orange-700 dark:text-orange-300"}," 根据实际使用次数计费，用多少付多少，经济实惠 ",-1))]),e("div",ya,[e("div",ha,[d[14]||(d[14]=e("span",{class:"text-gray-600 dark:text-dark-text-secondary"},"单次使用费用",-1)),e("span",fa,"¥"+l(y.app.price.amount),1)]),e("div",xa,[d[15]||(d[15]=e("span",{class:"text-gray-600 dark:text-dark-text-secondary"},"预充值金额",-1)),u(r,{modelValue:f.value,"onUpdate:modelValue":d[0]||(d[0]=$=>f.value=$),min:10,max:1e3,step:10,size:"small",style:{width:"120px"}},null,8,["modelValue"])]),e("div",ka,[d[16]||(d[16]=e("span",{class:"text-gray-600 dark:text-dark-text-secondary"},"可使用次数",-1)),e("span",ba," 约 "+l(Math.floor(f.value/y.app.price.amount))+" 次 ",1)])])])):A("",!0)])]),y.app.price.type!=="free"?(t(),o("div",_a,[d[18]||(d[18]=e("h4",{class:"text-base font-medium text-gray-900 dark:text-dark-text mb-3"}," 支付方式 ",-1)),e("div",wa,[(t(!0),o(E,null,D(_.value,$=>(t(),o("label",{key:$.id,class:P(["flex flex-col items-center p-4 border border-gray-200 dark:border-dark-border rounded-lg cursor-pointer hover:border-primary-300 dark:hover:border-primary-600 transition-colors text-center",c.value===$.id?"border-primary-500 bg-primary-50 dark:bg-primary-900/20":""])},[ke(e("input",{"onUpdate:modelValue":d[1]||(d[1]=W=>c.value=W),value:$.id,type:"radio",class:"sr-only"},null,8,$a),[[Ve,c.value]]),e("div",Ca,[e("div",Aa,l($.icon),1),e("div",null,[e("div",Ma,l($.name),1),e("div",Ta,l($.description),1)])]),c.value===$.id?(t(),o("div",Ia,[u(N(ge),{class:"w-4 h-4"})])):A("",!0)],2))),128))])])):A("",!0),y.app.price.type!=="free"?(t(),o("div",Va,[e("label",Na,[u(v,{modelValue:O.value,"onUpdate:modelValue":d[2]||(d[2]=$=>O.value=$)},null,8,["modelValue"]),d[19]||(d[19]=e("span",{class:"text-sm text-gray-600 dark:text-dark-text-secondary"},[w(" 我已阅读并同意 "),e("a",{href:"#",class:"text-primary-600 dark:text-primary-400 hover:underline"},"《用户服务协议》"),w(" 和 "),e("a",{href:"#",class:"text-primary-600 dark:text-primary-400 hover:underline"},"《隐私政策》")],-1))])])):A("",!0)])):A("",!0)]),_:1},8,["modelValue","title"])}}}),$e=Q(Sa,[["__scopeId","data-v-04184d35"]]),Ea={key:0,class:"app-details-content"},Da={class:"app-header flex items-start space-x-6 mb-6"},za={class:"flex-shrink-0"},ja={class:"w-20 h-20 bg-gradient-to-br from-primary-100 to-primary-200 dark:from-primary-900/30 dark:to-primary-800/30 rounded-2xl flex items-center justify-center text-3xl font-bold border border-primary-200 dark:border-primary-700"},Pa={class:"flex-1 min-w-0"},Ua={class:"flex items-center space-x-3 mb-2"},Fa={class:"text-2xl font-bold text-gray-900 dark:text-dark-text"},Ba={class:"text-gray-600 dark:text-dark-text-secondary mb-3"},La={class:"flex items-center space-x-6 text-sm mb-4"},Ya={class:"flex items-center space-x-2"},Ra={class:"flex items-center"},Ha={class:"font-medium text-gray-900 dark:text-dark-text"},Ga={class:"text-gray-500 dark:text-dark-text-secondary"},Za={class:"text-gray-500 dark:text-dark-text-secondary"},Ja={class:"text-gray-500 dark:text-dark-text-secondary"},qa={class:"flex flex-wrap gap-2"},Ka={class:"flex-shrink-0 space-y-3"},Qa={class:"text-right"},Wa={class:"flex flex-col space-y-2"},Xa={class:"space-y-6"},es={class:"text-gray-600 dark:text-dark-text-secondary leading-relaxed"},ts={key:0},as={class:"space-y-2"},ss={class:"text-gray-600 dark:text-dark-text-secondary"},rs={key:1},os={class:"space-y-2"},ns={class:"text-gray-600 dark:text-dark-text-secondary"},ls={key:0,class:"space-y-4"},is={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},ds=["onClick"],ps=["src","alt"],cs={class:"absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors duration-200 rounded-lg flex items-center justify-center"},us={key:1,class:"text-center py-12"},ms={class:"space-y-6"},gs={class:"bg-gray-50 dark:bg-dark-card rounded-lg p-6"},vs={class:"flex items-center space-x-8"},ys={class:"text-center"},hs={class:"text-3xl font-bold text-gray-900 dark:text-dark-text"},fs={class:"flex items-center justify-center mt-1"},xs={class:"text-sm text-gray-500 dark:text-dark-text-secondary mt-1"},ks={class:"flex-1 space-y-2"},bs={class:"text-sm text-gray-600 dark:text-dark-text-secondary w-8"},_s={class:"flex-1 bg-gray-200 dark:bg-gray-700 rounded-full h-2"},ws={class:"text-sm text-gray-500 dark:text-dark-text-secondary w-8"},$s={class:"space-y-4"},Cs={class:"flex items-start space-x-4"},As={class:"w-10 h-10 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full flex items-center justify-center text-white font-medium"},Ms={class:"flex-1"},Ts={class:"flex items-center space-x-3 mb-2"},Is={class:"font-medium text-gray-900 dark:text-dark-text"},Vs={class:"flex items-center"},Ns={class:"text-sm text-gray-500 dark:text-dark-text-secondary"},Os={class:"text-gray-600 dark:text-dark-text-secondary leading-relaxed"},Ss=K({__name:"AppDetailsDialog",props:{modelValue:{type:Boolean},app:{}},emits:["update:modelValue","favorite-toggle","install-toggle"],setup(n,{emit:p}){const i=n,s=p,a=x("overview"),c=x(!1),O=x(0),f=x(!1),z=T({get:()=>i.modelValue,set:r=>s("update:modelValue",r)}),U=x([{id:1,user:"张三",rating:5,date:"2024-01-15",content:"非常好用的工具，界面简洁，功能强大，大大提高了工作效率！"},{id:2,user:"李四",rating:4,date:"2024-01-12",content:"整体不错，就是有些功能还需要完善，期待后续更新。"},{id:3,user:"王五",rating:5,date:"2024-01-10",content:"强烈推荐！客服响应也很及时，解决问题很专业。"}]),_=r=>r>=1e4?`${(r/1e4).toFixed(1)}万`:r>=1e3?`${(r/1e3).toFixed(1)}k`:r.toString(),k=r=>new Date(r).toLocaleDateString("zh-CN"),F=r=>({5:65,4:20,3:10,2:3,1:2})[r]||0,B=()=>{if(!i.app)return"立即安装";switch(i.app.price.type){case"free":return"立即安装";case Z.ONE_TIME:return`立即购买 ¥${i.app.price.amount}`;case"monthly":return`立即订阅 ¥${i.app.price.amount}/月`;case"per_use":return"立即充值";default:return"立即安装"}},y=(r,v)=>{O.value=v,c.value=!0},d=r=>{s("install-toggle",r),f.value=!1};return(r,v)=>{var te;const Y=V("el-tag"),R=V("el-button"),$=V("el-tab-pane"),W=V("el-tabs"),ne=V("el-image-viewer"),le=V("el-dialog");return t(),I(le,{modelValue:z.value,"onUpdate:modelValue":v[6]||(v[6]=j=>z.value=j),title:((te=r.app)==null?void 0:te.name)||"应用详情",width:"900px","align-center":"","close-on-click-modal":!1,class:"app-details-dialog"},{default:h(()=>{var j;return[r.app?(t(),o("div",Ea,[e("div",Da,[e("div",za,[e("div",ja,l(r.app.icon),1)]),e("div",Pa,[e("div",Ua,[e("h2",Fa,l(r.app.name),1),r.app.status==="maintenance"?(t(),I(Y,{key:0,type:"warning",effect:"plain"},{default:h(()=>v[7]||(v[7]=[w(" 维护中 ")])),_:1,__:[7]})):r.app.status==="deprecated"?(t(),I(Y,{key:1,type:"danger",effect:"plain"},{default:h(()=>v[8]||(v[8]=[w(" 已废弃 ")])),_:1,__:[8]})):A("",!0)]),e("p",Ba,l(r.app.developer)+" • v"+l(r.app.version),1),e("div",La,[e("div",Ya,[e("div",Ra,[(t(),o(E,null,D(5,m=>u(N(oe),{key:m,class:P(["w-5 h-5",m<=Math.floor(r.app.rating)?"text-yellow-400 fill-current":"text-gray-300 dark:text-gray-600"])},null,8,["class"])),64))]),e("span",Ha,l(r.app.rating),1),e("span",Ga," ("+l(r.app.reviewCount)+" 评价) ",1)]),e("div",Za,l(_(r.app.downloadCount))+" 次使用 ",1),e("div",Ja," 更新于 "+l(k(r.app.lastUpdated)),1)]),e("div",qa,[(t(!0),o(E,null,D(r.app.tags,m=>(t(),I(Y,{key:m,size:"small",effect:"plain"},{default:h(()=>[w(l(m),1)]),_:2},1024))),128))])]),e("div",Ka,[e("div",Qa,[u(pe,{price:r.app.price,size:"large"},null,8,["price"])]),e("div",Wa,[r.app.isInstalled?(t(),I(R,{key:1,onClick:v[1]||(v[1]=m=>r.$emit("install-toggle",r.app.id)),type:"success",plain:"",size:"large",class:"w-full"},{default:h(()=>v[9]||(v[9]=[w(" 已安装 ")])),_:1,__:[9]})):(t(),I(R,{key:0,onClick:v[0]||(v[0]=m=>f.value=!0),type:"primary",size:"large",class:"w-full"},{default:h(()=>[w(l(B()),1)]),_:1})),u(R,{onClick:v[2]||(v[2]=m=>r.$emit("favorite-toggle",r.app.id)),type:r.app.isFavorited?"danger":"default",plain:!r.app.isFavorited,size:"large",class:"w-full !ml-0"},{default:h(()=>[u(N(we),{class:P(["w-4 h-4 mr-2",r.app.isFavorited?"fill-current":""])},null,8,["class"]),w(" "+l(r.app.isFavorited?"已收藏":"收藏"),1)]),_:1},8,["type","plain"])])])]),u(W,{modelValue:a.value,"onUpdate:modelValue":v[3]||(v[3]=m=>a.value=m),class:"app-details-tabs"},{default:h(()=>[u($,{label:"应用介绍",name:"overview"},{default:h(()=>[e("div",Xa,[e("div",null,[v[10]||(v[10]=e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-dark-text mb-3"}," 应用简介 ",-1)),e("p",es,l(r.app.longDescription||r.app.description),1)]),r.app.features&&r.app.features.length>0?(t(),o("div",ts,[v[11]||(v[11]=e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-dark-text mb-3"}," 主要功能 ",-1)),e("ul",as,[(t(!0),o(E,null,D(r.app.features,m=>(t(),o("li",{key:m,class:"flex items-center space-x-3"},[u(N(De),{class:"w-5 h-5 text-green-500 flex-shrink-0"}),e("span",ss,l(m),1)]))),128))])])):A("",!0),r.app.requirements&&r.app.requirements.length>0?(t(),o("div",rs,[v[12]||(v[12]=e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-dark-text mb-3"}," 使用要求 ",-1)),e("ul",os,[(t(!0),o(E,null,D(r.app.requirements,m=>(t(),o("li",{key:m,class:"flex items-center space-x-3"},[u(N(_e),{class:"w-5 h-5 text-blue-500 flex-shrink-0"}),e("span",ns,l(m),1)]))),128))])])):A("",!0)])]),_:1}),u($,{label:"应用截图",name:"screenshots"},{default:h(()=>[r.app.screenshots&&r.app.screenshots.length>0?(t(),o("div",ls,[e("div",is,[(t(!0),o(E,null,D(r.app.screenshots,(m,L)=>(t(),o("div",{key:L,class:"relative group cursor-pointer",onClick:ae=>y(m,L)},[e("img",{src:m,alt:`应用截图 ${L+1}`,class:"w-full h-48 object-cover rounded-lg border border-gray-200 dark:border-dark-border group-hover:border-primary-300 dark:group-hover:border-primary-600 transition-colors duration-200"},null,8,ps),e("div",cs,[u(N(Le),{class:"w-8 h-8 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-200"})])],8,ds))),128))])])):(t(),o("div",us,[u(N(be),{class:"w-16 h-16 text-gray-300 dark:text-gray-600 mx-auto mb-4"}),v[13]||(v[13]=e("p",{class:"text-gray-500 dark:text-dark-text-secondary"},"暂无应用截图",-1))]))]),_:1}),u($,{label:"用户评价",name:"reviews"},{default:h(()=>[e("div",ms,[e("div",gs,[e("div",vs,[e("div",ys,[e("div",hs,l(r.app.rating),1),e("div",fs,[(t(),o(E,null,D(5,m=>u(N(oe),{key:m,class:P(["w-4 h-4",m<=Math.floor(r.app.rating)?"text-yellow-400 fill-current":"text-gray-300 dark:text-gray-600"])},null,8,["class"])),64))]),e("div",xs,l(r.app.reviewCount)+" 条评价 ",1)]),e("div",ks,[(t(),o(E,null,D([5,4,3,2,1],m=>e("div",{key:m,class:"flex items-center space-x-3"},[e("span",bs,l(m)+"星 ",1),e("div",_s,[e("div",{class:"bg-yellow-400 h-2 rounded-full",style:Ne({width:`${F(m)}%`})},null,4)]),e("span",ws,l(F(m))+"% ",1)])),64))])])]),e("div",$s,[(t(!0),o(E,null,D(U.value,m=>(t(),o("div",{key:m.id,class:"border border-gray-200 dark:border-dark-border rounded-lg p-4"},[e("div",Cs,[e("div",As,l(m.user.charAt(0)),1),e("div",Ms,[e("div",Ts,[e("span",Is,l(m.user),1),e("div",Vs,[(t(),o(E,null,D(5,L=>u(N(oe),{key:L,class:P(["w-4 h-4",L<=m.rating?"text-yellow-400 fill-current":"text-gray-300 dark:text-gray-600"])},null,8,["class"])),64))]),e("span",Ns,l(m.date),1)]),e("p",Os,l(m.content),1)])])]))),128))])])]),_:1})]),_:1},8,["modelValue"])])):A("",!0),c.value?(t(),I(ne,{key:1,"url-list":((j=r.app)==null?void 0:j.screenshots)||[],"initial-index":O.value,onClose:v[4]||(v[4]=m=>c.value=!1)},null,8,["url-list","initial-index"])):A("",!0),u($e,{modelValue:f.value,"onUpdate:modelValue":v[5]||(v[5]=m=>f.value=m),app:r.app,onPurchaseSuccess:d},null,8,["modelValue","app"])]}),_:1},8,["modelValue","title"])}}}),Es=Q(Ss,[["__scopeId","data-v-2e4717d6"]]),M=Z;var H=(n=>(n.IMAGE_PROCESSING="image_processing",n.DATA_ANALYSIS="data_analysis",n.SEO_TOOLS="seo_tools",n.MARKET_ANALYSIS="market_analysis",n.MANAGEMENT_TOOLS="management_tools",n.AUTOMATION="automation",n.CONTENT_CREATION="content_creation",n))(H||{});const G=x([]),J=x([]),q=x([]),de=x(!1),ee=x({}),Ds=[{id:"product-collection",name:"商品采集",description:"智能采集全球电商平台商品信息，支持Amazon、Temu、Shein等主流平台",longDescription:"商品采集是一款专业的电商数据采集工具，支持从Amazon、Temu、Shein等主流电商平台批量采集商品信息。具备智能去重、数据清洗、格式转换等功能，是电商从业者的必备工具。",icon:"🛒",screenshots:["https://picsum.photos/800/600?random=1","https://picsum.photos/800/600?random=2","https://picsum.photos/800/600?random=3"],category:"data_analysis",tags:["数据采集","电商","批量处理","多平台"],price:{type:M.PER_ITEM,amount:.1,currency:"CNY",originalAmount:.15,unit:"/个商品",description:"按采集商品数量计费"},rating:4.9,reviewCount:2341,downloadCount:28750,developer:"RiinAI团队",version:"3.2.1",lastUpdated:"2024-01-16",status:"active",features:["多平台支持","智能去重","数据清洗","批量导出","定时采集"],requirements:["需要网络连接","支持Chrome浏览器插件"]},{id:"smart-crop",name:"智能裁图",description:"AI智能图片裁剪和优化，自动识别主体，支持多种裁剪比例",longDescription:"智能裁图是一款基于深度学习的图片裁剪工具，能够自动识别图片主体，进行智能裁剪。支持批量处理、多种裁剪比例、自定义裁剪区域等功能。",icon:"✂️",screenshots:["https://picsum.photos/800/600?random=4","https://picsum.photos/800/600?random=5","https://picsum.photos/800/600?random=6"],category:"image_processing",tags:["AI","图片处理","批量处理","智能裁剪"],price:{type:M.PER_ITEM,amount:.05,currency:"CNY",unit:"/张",description:"按处理图片数量计费"},rating:4.8,reviewCount:1856,downloadCount:22420,developer:"RiinAI团队",version:"2.1.0",lastUpdated:"2024-01-15",status:"active",features:["智能主体识别","批量处理支持","多种裁剪比例","高质量输出","云端处理"],requirements:["需要网络连接","支持JPG/PNG格式"]},{id:"one-click-cutout",name:"一键抠图",description:"一键智能抠图，去除背景，AI驱动的背景移除工具",longDescription:"一键抠图使用先进的AI算法，能够精确识别图片主体，自动移除背景。支持批量处理，输出高质量透明背景图片，是设计师和电商从业者的得力助手。",icon:"🎨",screenshots:["https://picsum.photos/800/600?random=7","https://picsum.photos/800/600?random=8"],category:"image_processing",tags:["AI","抠图","背景移除","图片处理"],price:{type:M.PER_USE,amount:.5,currency:"CNY",unit:"/张",description:"按使用次数计费，高质量处理"},rating:4.9,reviewCount:3241,downloadCount:45750,developer:"RiinAI团队",version:"1.8.0",lastUpdated:"2024-01-14",status:"active",features:["AI智能识别","高精度抠图","批量处理","多格式支持","云端处理"]},{id:"super-split",name:"超级裂变",description:"营销裂变工具，快速传播，支持多种裂变模式和数据分析",longDescription:"超级裂变是一款专业的营销裂变工具，支持多种裂变模式，包括分享裂变、任务裂变、拼团裂变等。提供详细的数据分析和用户行为追踪，帮助企业快速扩大用户规模。",icon:"🚀",screenshots:["https://picsum.photos/800/600?random=9","https://picsum.photos/800/600?random=10","https://picsum.photos/800/600?random=11"],category:"automation",tags:["营销","裂变","传播","数据分析"],price:{type:M.MONTHLY,amount:199,currency:"CNY",unit:"/月",description:"专业营销工具，支持无限裂变活动"},rating:4.7,reviewCount:1567,downloadCount:18920,developer:"RiinAI团队",version:"2.5.0",lastUpdated:"2024-01-13",status:"active",features:["多种裂变模式","数据分析","用户追踪","活动管理","效果统计"]},{id:"title-generator",name:"标题生成",description:"AI智能生成吸引人的标题，支持多种模板和平台优化",longDescription:"AI标题生成器基于大语言模型，能够为不同平台生成吸引人的标题。支持电商、自媒体、广告等多种场景，提供模板库和个性化定制功能。",icon:"📝",screenshots:["https://picsum.photos/800/600?random=12","https://picsum.photos/800/600?random=13"],category:"content_creation",tags:["AI","标题生成","内容创作","营销"],price:{type:M.PER_USE,amount:.1,currency:"CNY",unit:"/次",description:"按生成次数计费，经济实惠"},rating:4.6,reviewCount:2890,downloadCount:35670,developer:"RiinAI团队",version:"1.9.2",lastUpdated:"2024-01-12",status:"active",features:["AI智能生成","多平台优化","模板库","批量生成","效果预测"]},{id:"pod-compose",name:"POD合成",description:"按需印刷商品合成工具，支持图案与产品的智能合成",longDescription:"POD合成工具专为按需印刷业务设计，支持将设计图案与各种产品模板进行智能合成。提供丰富的产品库、智能排版、批量处理等功能。",icon:"🎨",screenshots:["https://picsum.photos/800/600?random=14","https://picsum.photos/800/600?random=15","https://picsum.photos/800/600?random=16"],category:"image_processing",tags:["POD","合成","印刷","设计"],price:{type:M.MONTHLY,amount:89,currency:"CNY",unit:"/月",description:"专业POD工具，支持无限合成"},rating:4.5,reviewCount:1234,downloadCount:12450,developer:"RiinAI团队",version:"1.6.0",lastUpdated:"2024-01-11",status:"active",features:["智能合成","产品模板库","批量处理","高清输出","自动排版"]},{id:"batch-listing",name:"批量刊登",description:"批量发布商品到各大平台，支持模板导入和自动化发布",longDescription:"批量刊登工具支持将商品信息批量发布到Amazon、eBay、Shopify等多个电商平台。提供模板导入、数据映射、自动化发布等功能，大大提高运营效率。",icon:"📦",screenshots:["https://picsum.photos/800/600?random=17","https://picsum.photos/800/600?random=18"],category:"automation",tags:["批量发布","电商","自动化","多平台"],price:{type:M.MONTHLY,amount:159,currency:"CNY",unit:"/月",description:"专业电商工具，支持无限发布"},rating:4.4,reviewCount:987,downloadCount:15680,developer:"RiinAI团队",version:"2.3.1",lastUpdated:"2024-01-10",status:"active",features:["多平台支持","模板导入","批量发布","数据映射","发布监控"]},{id:"price-monitor",name:"价格监控",description:"实时监控商品价格变化，支持多平台价格对比和降价提醒",longDescription:"价格监控工具帮助您实时跟踪商品价格变化，支持Amazon、Temu、Shein等主流电商平台。提供价格历史图表、降价提醒、竞品对比等功能。",icon:"📊",screenshots:["https://picsum.photos/800/600?random=19","https://picsum.photos/800/600?random=20"],category:"data_analysis",tags:["价格监控","数据分析","电商","提醒"],price:{type:M.FREE,amount:0,currency:"CNY",description:"免费版本，每日可监控10个商品"},rating:4.2,reviewCount:1892,downloadCount:28750,developer:"第三方开发者",version:"1.5.2",lastUpdated:"2024-01-10",status:"active",features:["多平台支持","价格历史图表","降价提醒","数据导出"],requirements:["需要网络连接"]},{id:"keyword-research",name:"关键词研究",description:"专业的关键词挖掘和分析工具，助力SEO优化",longDescription:"关键词研究专家是一款专业的SEO工具，提供关键词挖掘、竞争度分析、搜索量预测等功能。帮助您找到高价值的关键词，提升网站排名。",icon:"🔍",screenshots:["https://picsum.photos/800/600?random=21","https://picsum.photos/800/600?random=22","https://picsum.photos/800/600?random=23"],category:"seo_tools",tags:["SEO","关键词","搜索优化","竞争分析"],price:{type:M.ONE_TIME,amount:199,currency:"CNY",originalAmount:299,description:"一次购买，终身使用"},rating:4.6,reviewCount:1567,downloadCount:13240,developer:"SEO专家团队",version:"3.0.1",lastUpdated:"2024-01-12",status:"active",features:["关键词挖掘","竞争度分析","搜索量预测","长尾词推荐","数据报告"]},{id:"review-analyzer",name:"评论分析",description:"智能分析商品评论情感，提取用户反馈洞察",longDescription:"评论分析工具使用自然语言处理技术，智能分析商品评论的情感倾向、关键词提取、用户满意度等。帮助商家了解产品优缺点，优化产品和服务。",icon:"💬",screenshots:["https://picsum.photos/800/600?random=24","https://picsum.photos/800/600?random=25"],category:"data_analysis",tags:["评论分析","NLP","情感分析","用户洞察"],price:{type:M.MONTHLY,amount:79,currency:"CNY",unit:"/月",description:"专业分析工具，无限制使用"},rating:4.3,reviewCount:756,downloadCount:9870,developer:"AI分析团队",version:"2.1.0",lastUpdated:"2024-01-09",status:"active",features:["情感分析","关键词提取","满意度评分","趋势分析","报告生成"]},{id:"competitor-analysis",name:"竞品分析",description:"深度分析竞争对手策略，洞察市场机会",longDescription:"竞品分析大师帮助您深入了解竞争对手的产品策略、价格策略、营销手段等。提供详细的分析报告和市场洞察，助力商业决策。",icon:"🎯",screenshots:["https://picsum.photos/800/600?random=26","https://picsum.photos/800/600?random=27"],category:"market_analysis",tags:["竞品分析","市场研究","策略分析","商业智能"],price:{type:M.MONTHLY,amount:299,currency:"CNY",unit:"/月",description:"专业版功能，深度分析报告"},rating:4.4,reviewCount:623,downloadCount:7890,developer:"商业分析专家",version:"2.3.0",lastUpdated:"2024-01-08",status:"active",features:["竞品监控","价格对比","营销策略分析","市场趋势预测","定制报告"]},{id:"inventory-management",name:"库存管理",description:"智能库存管理系统，支持多仓库、多渠道库存同步",longDescription:"智能库存管理系统提供全面的库存控制功能，支持多仓库管理、库存预警、自动补货、销售预测等。帮助企业优化库存结构，降低运营成本。",icon:"📋",screenshots:["https://picsum.photos/800/600?random=28","https://picsum.photos/800/600?random=29","https://picsum.photos/800/600?random=30"],category:"management_tools",tags:["库存管理","仓储","供应链","预测"],price:{type:M.MONTHLY,amount:399,currency:"CNY",unit:"/月",description:"企业级库存管理解决方案"},rating:4.5,reviewCount:445,downloadCount:5670,developer:"企业管理专家",version:"3.1.2",lastUpdated:"2024-01-07",status:"active",features:["多仓库管理","库存预警","自动补货","销售预测","报表分析"]},{id:"customer-service",name:"客服助手",description:"AI智能客服机器人，24小时自动回复客户咨询",longDescription:"AI客服助手基于大语言模型，能够理解客户问题并提供准确回复。支持多平台接入、知识库管理、人工客服转接等功能，大幅提升客服效率。",icon:"🎧",screenshots:["https://picsum.photos/800/600?random=31","https://picsum.photos/800/600?random=32"],category:"automation",tags:["AI客服","自动回复","知识库","多平台"],price:{type:M.MONTHLY,amount:199,currency:"CNY",unit:"/月",description:"AI客服解决方案，支持无限对话"},rating:4.6,reviewCount:1234,downloadCount:15670,developer:"AI服务团队",version:"2.0.5",lastUpdated:"2024-01-06",status:"active",features:["AI智能回复","多平台接入","知识库管理","人工转接","对话分析"]},{id:"data-export",name:"数据导出",description:"多格式数据导出工具，支持Excel、CSV、JSON等格式",longDescription:"数据导出工具支持将各种业务数据导出为多种格式，包括Excel、CSV、JSON、PDF等。提供数据清洗、格式转换、定时导出等功能。",icon:"📤",screenshots:["https://picsum.photos/800/600?random=33","https://picsum.photos/800/600?random=34"],category:"data_analysis",tags:["数据导出","格式转换","数据清洗","自动化"],price:{type:M.FREE,amount:0,currency:"CNY",description:"免费工具，基础导出功能"},rating:4.1,reviewCount:567,downloadCount:12340,developer:"数据工具团队",version:"1.4.0",lastUpdated:"2024-01-05",status:"active",features:["多格式支持","数据清洗","批量导出","定时任务","模板定制"]},{id:"report-generator",name:"报表生成",description:"自动生成业务报表，支持多种图表和数据可视化",longDescription:"报表生成器能够自动收集业务数据，生成专业的分析报表。支持多种图表类型、数据可视化、定时发送等功能，让数据分析更简单。",icon:"📈",screenshots:["https://picsum.photos/800/600?random=35","https://picsum.photos/800/600?random=36","https://picsum.photos/800/600?random=37"],category:"data_analysis",tags:["报表生成","数据可视化","图表","自动化"],price:{type:M.MONTHLY,amount:129,currency:"CNY",unit:"/月",description:"专业报表工具，无限制生成"},rating:4.7,reviewCount:890,downloadCount:11230,developer:"数据分析专家",version:"2.2.1",lastUpdated:"2024-01-04",status:"active",features:["自动数据收集","多种图表","数据可视化","定时发送","模板库"]},{id:"workflow-automation",name:"工作流自动化",description:"无代码工作流自动化平台，连接各种应用和服务",longDescription:"工作流自动化平台让您无需编程即可创建复杂的自动化流程。支持连接数百种应用和服务，实现数据同步、任务自动化、通知提醒等功能。",icon:"⚡",screenshots:["https://picsum.photos/800/600?random=38","https://picsum.photos/800/600?random=39","https://picsum.photos/800/600?random=40"],category:"automation",tags:["工作流","自动化","无代码","集成"],price:{type:M.MONTHLY,amount:299,currency:"CNY",originalAmount:399,unit:"/月",description:"企业级自动化解决方案"},rating:4.8,reviewCount:1567,downloadCount:8900,developer:"自动化专家",version:"3.0.0",lastUpdated:"2024-01-03",status:"active",features:["无代码设计","应用集成","条件触发","数据转换","监控告警"]}],zs=()=>{G.value=Ds.map(n=>({...n,isFavorited:J.value.includes(n.id),isInstalled:q.value.includes(n.id)}))},ye=()=>G.value,he=T(()=>G.value.filter(n=>n.isFavorited)),js=T(()=>{let n=G.value;const p=ee.value;if(p.searchKeyword){const i=p.searchKeyword.toLowerCase();n=n.filter(s=>s.name.toLowerCase().includes(i)||s.description.toLowerCase().includes(i)||s.tags.some(a=>a.toLowerCase().includes(i)))}return p.category&&(n=n.filter(i=>i.category===p.category)),p.priceType&&(n=n.filter(i=>i.price.type===p.priceType)),p.rating!==void 0&&(n=n.filter(i=>i.rating>=p.rating)),p.sortBy&&n.sort((i,s)=>{let a,c;switch(p.sortBy){case"name":a=i.name,c=s.name;break;case"rating":a=i.rating,c=s.rating;break;case"downloadCount":a=i.downloadCount,c=s.downloadCount;break;case"lastUpdated":a=new Date(i.lastUpdated),c=new Date(s.lastUpdated);break;case"price":a=i.price.amount,c=s.price.amount;break;default:return 0}return p.sortOrder==="desc"?a>c?-1:a<c?1:0:a<c?-1:a>c?1:0}),n}),Ps=n=>{ee.value={...ee.value,...n}},Us=()=>{ee.value={}},Fs=n=>{const p=G.value.find(s=>s.id===n);if(!p)return!1;const i=J.value.indexOf(n);return i>-1?(J.value.splice(i,1),p.isFavorited=!1):(J.value.push(n),p.isFavorited=!0),localStorage.setItem("app-market-favorites",JSON.stringify(J.value)),p.isFavorited},Bs=n=>{const p=G.value.find(s=>s.id===n);if(!p)return!1;const i=q.value.indexOf(n);return i>-1?(q.value.splice(i,1),p.isInstalled=!1):(q.value.push(n),p.isInstalled=!0),localStorage.setItem("app-market-installed",JSON.stringify(q.value)),p.isInstalled},fe=n=>G.value.find(p=>p.id===n),Ls=()=>{de.value=!0;const n=localStorage.getItem("app-market-favorites");n&&(J.value=JSON.parse(n));const p=localStorage.getItem("app-market-installed");p&&(q.value=JSON.parse(p)),zs(),de.value=!1},Ys={apps:T(()=>G.value),loading:T(()=>de.value),currentFilter:T(()=>ee.value)},Rs={class:"space-y-8"},Hs={class:"bg-white dark:bg-dark-card rounded-2xl shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border p-6"},Gs={class:"flex items-center justify-between mb-6"},Zs={class:"text-sm text-gray-500 dark:text-dark-text-secondary"},Js={class:"flex flex-wrap gap-3"},qs={class:"ml-2 px-2 py-0.5 bg-white/20 rounded-full text-xs"},Ks=["onClick"],Qs={class:"ml-2 px-2 py-0.5 bg-white/20 rounded-full text-xs"},Ws={class:"bg-white dark:bg-dark-card rounded-2xl shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border p-6"},Xs={class:"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6"},er={class:"flex-1 max-w-2xl"},tr={class:"relative"},ar={class:"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none"},sr={class:"flex flex-wrap items-end gap-4 sm:gap-6"},rr={class:"flex flex-col"},or={class:"flex flex-col"},nr={class:"flex items-center space-x-2"},lr={class:"flex flex-col"},ir=["title"],dr={class:"bg-white dark:bg-dark-card rounded-xl shadow-sm border border-gray-100 dark:border-dark-border overflow-hidden"},pr={class:"flex items-center"},cr=K({__name:"index",setup(n){const p=x("all"),i=x(""),s=x(""),a=x(""),c=x(""),O=x("name"),f=x("asc"),z=x(!1),U=x(null),_=x(!1),k=x(null),F=T(()=>Ys.loading.value),B=T(()=>ye().length),y=T(()=>he.value.length),d=T(()=>js.value),r=T(()=>he.value),v=T(()=>[{label:"图像处理",value:H.IMAGE_PROCESSING,icon:be},{label:"数据分析",value:H.DATA_ANALYSIS,icon:ze},{label:"SEO工具",value:H.SEO_TOOLS,icon:Ye},{label:"市场分析",value:H.MARKET_ANALYSIS,icon:He},{label:"管理工具",value:H.MANAGEMENT_TOOLS,icon:Oe},{label:"自动化工具",value:H.AUTOMATION,icon:Fe},{label:"内容创作",value:H.CONTENT_CREATION,icon:Re}]),Y=T(()=>[{label:"免费",value:M.FREE},{label:"一口价",value:M.ONE_TIME},{label:"包月",value:M.MONTHLY},{label:"按次计费",value:M.PER_USE}]),R=()=>{j()},$=()=>{j()},W=C=>{C===void 0&&(a.value=""),j()},ne=C=>{C===void 0&&(c.value=""),j()},le=()=>{j()},te=()=>{f.value=f.value==="asc"?"desc":"asc",j()},j=()=>{const C={searchKeyword:i.value||void 0,category:s.value||void 0,priceType:a.value||void 0,rating:c.value||void 0,sortBy:O.value,sortOrder:f.value};Ps(C)},m=()=>{i.value="",s.value="",a.value="",c.value="",O.value="name",f.value="asc",Us()},L=C=>{U.value=C,z.value=!0},ae=C=>{const g=Fs(C),S=fe(C);S&&X.success(g?`已收藏 ${S.name}`:`已取消收藏 ${S.name}`)},se=C=>{const g=Bs(C),S=fe(C);S&&X.success(g?`已安装 ${S.name}`:`已卸载 ${S.name}`)},ce=C=>{k.value=C,_.value=!0},Ce=C=>{se(C),_.value=!1},Ae=C=>C?ye().filter(g=>g.category===C).length:B.value;return Se(()=>{Ls()}),(C,g)=>{const S=V("el-option"),ie=V("el-select"),ue=V("el-tab-pane"),Me=V("el-badge"),Te=V("el-tabs");return t(),o("div",Rs,[g[15]||(g[15]=xe('<div class="bg-gradient-to-r from-primary-50 to-blue-50 dark:from-primary-900/20 dark:to-blue-900/20 rounded-2xl p-6 border border-primary-100 dark:border-primary-800" data-v-680fdc9c><div class="flex items-center space-x-3" data-v-680fdc9c><div class="w-10 h-10 bg-gradient-to-br from-primary-500 to-primary-600 rounded-xl flex items-center justify-center" data-v-680fdc9c><svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-680fdc9c><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" data-v-680fdc9c></path></svg></div><div data-v-680fdc9c><h1 class="text-2xl font-bold text-gray-900 dark:text-dark-text" data-v-680fdc9c>应用市场</h1><p class="mt-1 text-sm text-gray-600 dark:text-dark-text-secondary" data-v-680fdc9c>发现、安装和管理您的应用工具</p></div></div></div>',1)),e("div",Hs,[e("div",Gs,[g[9]||(g[9]=e("h2",{class:"text-xl font-bold text-gray-900 dark:text-dark-text flex items-center"},[e("svg",{class:"w-5 h-5 mr-2 text-purple-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"})]),w(" 应用分类 ")],-1)),e("div",Zs,l(d.value.length)+" / "+l(B.value)+" 个应用 ",1)]),e("div",Js,[e("button",{onClick:g[0]||(g[0]=b=>{s.value="",$()}),class:P(["px-4 py-2 rounded-xl text-sm font-medium transition-all duration-200 transform hover:scale-105",s.value===""?"bg-gradient-to-r from-purple-500 to-pink-500 text-white shadow-lg":"bg-gray-100 dark:bg-dark-surface text-gray-700 dark:text-dark-text hover:bg-gray-200 dark:hover:bg-dark-border"])},[g[10]||(g[10]=e("svg",{class:"w-4 h-4 mr-2 inline",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"})],-1)),g[11]||(g[11]=w(" 全部应用 ")),e("span",qs,l(B.value),1)],2),(t(!0),o(E,null,D(v.value,b=>(t(),o("button",{key:b.value,onClick:ur=>{s.value=b.value,$()},class:P(["px-4 py-2 rounded-xl text-sm font-medium transition-all duration-200 transform hover:scale-105 flex items-center",s.value===b.value?"bg-gradient-to-r from-purple-500 to-pink-500 text-white shadow-lg":"bg-gray-100 dark:bg-dark-surface text-gray-700 dark:text-dark-text hover:bg-gray-200 dark:hover:bg-dark-border"])},[(t(),I(me(b.icon),{class:"w-4 h-4 mr-2"})),w(" "+l(b.label)+" ",1),e("span",Qs,l(Ae(b.value)),1)],10,Ks))),128))])]),e("div",Ws,[e("div",Xs,[e("div",er,[e("div",tr,[e("div",ar,[u(N(je),{class:"w-5 h-5 text-gray-400"})]),ke(e("input",{"onUpdate:modelValue":g[1]||(g[1]=b=>i.value=b),onInput:R,type:"text",placeholder:"搜索应用名称、描述或标签...",class:"w-full pl-12 pr-12 py-4 bg-gray-50 dark:bg-dark-surface border border-gray-200 dark:border-dark-border rounded-2xl focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200 placeholder-gray-400 dark:placeholder-gray-500 text-gray-900 dark:text-dark-text hover:border-gray-300 dark:hover:border-dark-text-secondary"},null,544),[[Ee,i.value]]),i.value?(t(),o("button",{key:0,onClick:g[2]||(g[2]=b=>{i.value="",R()}),class:"absolute inset-y-0 right-0 pr-4 flex items-center text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors duration-200"},g[12]||(g[12]=[e("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))):A("",!0)])]),e("div",sr,[e("div",rr,[u(ie,{modelValue:a.value,"onUpdate:modelValue":g[3]||(g[3]=b=>a.value=b),onChange:W,placeholder:"全部价格",clearable:"",class:"app-market-select",style:{width:"140px","min-width":"120px"}},{default:h(()=>[(t(!0),o(E,null,D(Y.value,b=>(t(),I(S,{key:b.value,label:b.label,value:b.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),e("div",or,[u(ie,{modelValue:c.value,"onUpdate:modelValue":g[4]||(g[4]=b=>c.value=b),onChange:ne,placeholder:"全部评分",clearable:"",class:"app-market-select",style:{width:"140px","min-width":"120px"}},{default:h(()=>[u(S,{label:"4星以上",value:4}),u(S,{label:"3星以上",value:3}),u(S,{label:"2星以上",value:2})]),_:1},8,["modelValue"])]),e("div",nr,[e("div",lr,[u(ie,{modelValue:O.value,"onUpdate:modelValue":g[5]||(g[5]=b=>O.value=b),onChange:le,class:"app-market-select",style:{width:"140px","min-width":"120px"}},{default:h(()=>[u(S,{label:"按名称",value:"name"}),u(S,{label:"按评分",value:"rating"}),u(S,{label:"按下载量",value:"downloadCount"}),u(S,{label:"按更新时间",value:"lastUpdated"}),u(S,{label:"按价格",value:"price"})]),_:1},8,["modelValue"])]),e("button",{onClick:te,class:"p-3 rounded-xl bg-gray-50 dark:bg-dark-surface hover:bg-gray-100 dark:hover:bg-dark-border transition-all duration-200 border border-gray-200 dark:border-dark-border transform hover:scale-105",title:f.value==="asc"?"升序":"降序"},[(t(),I(me(f.value==="asc"?N(Ue):N(Pe)),{class:"w-4 h-4 text-gray-600 dark:text-dark-text-secondary"}))],8,ir)]),e("button",{onClick:m,class:"px-4 py-3 text-sm font-medium text-gray-600 dark:text-dark-text-secondary hover:text-purple-600 dark:hover:text-purple-400 bg-gray-50 dark:bg-dark-surface hover:bg-gray-100 dark:hover:bg-dark-border rounded-xl transition-all duration-200 border border-gray-200 dark:border-dark-border transform hover:scale-105"},g[13]||(g[13]=[e("svg",{class:"w-4 h-4 mr-2 inline",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1),w(" 清除筛选 ")]))])])]),e("div",dr,[u(Te,{modelValue:p.value,"onUpdate:modelValue":g[6]||(g[6]=b=>p.value=b),class:"app-market-tabs"},{default:h(()=>[u(ue,{label:"全部应用",name:"all"},{default:h(()=>[u(ve,{apps:d.value,loading:F.value,onAppClick:L,onFavoriteToggle:ae,onInstallToggle:se,onPurchase:ce},null,8,["apps","loading"])]),_:1}),u(ue,{name:"favorites"},{label:h(()=>[e("span",pr,[g[14]||(g[14]=w(" 我的收藏 ")),u(Me,{value:y.value,hidden:y.value===0,class:"ml-2"},null,8,["value","hidden"])])]),default:h(()=>[u(ve,{apps:r.value,loading:F.value,onAppClick:L,onFavoriteToggle:ae,onInstallToggle:se,onPurchase:ce},null,8,["apps","loading"])]),_:1})]),_:1},8,["modelValue"])]),u(Es,{modelValue:z.value,"onUpdate:modelValue":g[7]||(g[7]=b=>z.value=b),app:U.value,onFavoriteToggle:ae,onInstallToggle:se},null,8,["modelValue","app"]),u($e,{modelValue:_.value,"onUpdate:modelValue":g[8]||(g[8]=b=>_.value=b),app:k.value,onPurchaseSuccess:Ce},null,8,["modelValue","app"])])}}}),kr=Q(cr,[["__scopeId","data-v-680fdc9c"]]);export{kr as default};
