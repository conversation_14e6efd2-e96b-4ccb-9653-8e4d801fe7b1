import{d as X,e as D,r as x,q as K,j as l,h as f,a as e,g as s,C as _,o as u,E as h,_ as Y,c as k,F as O,M as F,k as se,t as n,A as Q,b as ae,Q as le,f as ne,p as de,G as ie,n as ue}from"./index-BTucjZQh.js";import{_ as ce}from"./GallerySelectDialog.vue_vue_type_script_setup_true_lang-CrzPKmtJ.js";import{g as me,c as ge,a as xe}from"./billing-DsHNwwW1.js";import{r as oe}from"./ArrowDownTrayIcon-B03b1yfF.js";import"./billing-Ciq-WFQK.js";const pe={class:"space-y-6"},ke={class:"space-y-2"},ve={class:"space-y-2"},be={class:"space-y-2"},he={class:"flex justify-end space-x-3"},fe=X({__name:"CreatePresetDialog",props:{modelValue:{type:Boolean}},emits:["update:modelValue","success"],setup(H,{emit:M}){const S=H,C=M,y=D({get:()=>S.modelValue,set:$=>C("update:modelValue",$)}),g=x({name:"",description:"",rules:""}),V=()=>{if(!g.value.name.trim()){h.warning("请输入预设名称");return}if(!g.value.rules.trim()){h.warning("请输入生成规则");return}const $={id:Date.now().toString(),name:g.value.name.trim(),description:g.value.description.trim(),rules:g.value.rules.trim()};C("success",$),C("update:modelValue",!1),B()},B=()=>{g.value={name:"",description:"",rules:""}};return($,i)=>{const a=f("el-input"),w=f("el-button"),j=f("el-dialog");return u(),K(j,{modelValue:y.value,"onUpdate:modelValue":i[4]||(i[4]=v=>y.value=v),title:"新建预设",width:"600px","align-center":"",onClose:B},{footer:l(()=>[e("div",he,[s(w,{onClick:i[3]||(i[3]=v=>y.value=!1)},{default:l(()=>i[9]||(i[9]=[_("取消")])),_:1,__:[9]}),s(w,{type:"primary",onClick:V,disabled:!g.value.name.trim()||!g.value.rules.trim()},{default:l(()=>i[10]||(i[10]=[_(" 创建预设 ")])),_:1,__:[10]},8,["disabled"])])]),default:l(()=>[e("div",pe,[e("div",ke,[i[5]||(i[5]=e("label",{class:"block text-sm font-medium text-gray-900 dark:text-dark-text"},[_(" 预设名称 "),e("span",{class:"text-red-500"},"*")],-1)),s(a,{modelValue:g.value.name,"onUpdate:modelValue":i[0]||(i[0]=v=>g.value.name=v),placeholder:"请输入预设名称，如：亚马逊标题",class:"modern-input"},null,8,["modelValue"])]),e("div",ve,[i[6]||(i[6]=e("label",{class:"block text-sm font-medium text-gray-900 dark:text-dark-text"}," 预设描述 ",-1)),s(a,{modelValue:g.value.description,"onUpdate:modelValue":i[1]||(i[1]=v=>g.value.description=v),placeholder:"请输入预设描述，简要说明此预设的用途和特点",class:"modern-input"},null,8,["modelValue"])]),e("div",be,[i[7]||(i[7]=e("label",{class:"block text-sm font-medium text-gray-900 dark:text-dark-text"},[_(" 生成规则 "),e("span",{class:"text-red-500"},"*")],-1)),s(a,{modelValue:g.value.rules,"onUpdate:modelValue":i[2]||(i[2]=v=>g.value.rules=v),type:"textarea",rows:8,placeholder:"请输入详细的生成规则...",class:"modern-textarea"},null,8,["modelValue"]),i[8]||(i[8]=e("div",{class:"mt-3 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800"},[e("h4",{class:"text-sm font-medium text-blue-900 dark:text-blue-100 mb-2"},"预设示例："),e("div",{class:"text-sm text-blue-800 dark:text-blue-200 space-y-2"},[e("p",null,[e("strong",null,"【亚马逊标题】")]),e("p",null,"任务：根据图片内容拟定标题；"),e("p",null,"要求：商品款式+商品颜色+印花图案+使用场景，遵循<亚马逊>平台要求，输出<英文>，总字符量不超过120个字符")])],-1))])])]),_:1},8,["modelValue"])}}}),we=Y(fe,[["__scopeId","data-v-34a6ef34"]]),ye={class:"space-y-6"},_e={class:"flex justify-start space-x-3"},Ce={class:"bg-gray-50 dark:bg-dark-card rounded-lg p-4"},$e={class:"flex items-center justify-between mb-3"},Ve={class:"relative"},je={key:0,class:"absolute right-0 top-8 w-80 bg-white dark:bg-dark-surface border border-gray-200 dark:border-dark-border rounded-lg shadow-lg z-10"},Te={class:"p-4"},Me={class:"flex items-center justify-between mb-3"},Se={class:"space-y-2 max-h-48 overflow-y-auto"},Be=["onClick"],ze={class:"font-medium text-sm text-gray-900 dark:text-dark-text"},De={class:"text-xs text-gray-500 dark:text-dark-text-secondary mt-1 line-clamp-2"},Le={key:0,class:"space-y-4"},Pe={class:"flex items-center justify-between"},He={class:"text-sm font-medium text-gray-900 dark:text-dark-text"},Ie={class:"text-xs text-gray-500 dark:text-dark-text-secondary"},Ge={class:"border border-gray-200 dark:border-dark-border rounded-lg p-4 max-h-80 overflow-y-auto"},Ue={class:"grid grid-cols-6 gap-4"},Fe={class:"relative"},Ae=["src","alt"],Ne=["onClick"],Re={class:"text-xs text-gray-600 dark:text-dark-text-secondary mt-1 truncate"},We={key:0,class:"mt-4 flex justify-center"},Ee={key:1,class:"text-center py-12 border-2 border-dashed border-gray-200 dark:border-dark-border rounded-lg"},Oe={class:"flex justify-between items-center"},Qe={class:"text-sm text-gray-500 dark:text-dark-text-secondary"},qe={key:0},Je={key:0,class:"text-green-600 dark:text-green-400"},Ke={key:1,class:"text-blue-600 dark:text-blue-400"},Xe={key:1},Ye={class:"flex space-x-3"},re="title-generator",Ze=X({__name:"CreateTitleDialog",props:{modelValue:{type:Boolean}},emits:["update:modelValue","success"],setup(H,{emit:M}){const S=H,C=M,y=D({get:()=>S.modelValue,set:r=>C("update:modelValue",r)}),g=x(!1),V=x(!1),B=x(!1),$=x(!1),i=x([]),a=x([]),w=x(1),j=x(18),v=x(""),I=x([{id:"1",name:"亚马逊标题",description:"根据图片内容拟定标题；要求：商品款式+商品颜色+印花图案+使用场景，遵循亚马逊平台要求，输出英文，总字符量不超过120个字符",rules:"任务：根据图片内容拟定标题；要求：商品款式+商品颜色+印花图案+使用场景，遵循<亚马逊>平台要求，输出<英文>，总字符量不超过120个字符"},{id:"2",name:"Temu标题",description:"根据图片内容生成Temu平台标题；要求：突出产品特色和卖点，中英文混合，字符数80-100",rules:"任务：根据图片内容拟定标题；要求：突出产品特色和卖点，遵循<Temu>平台要求，输出<中英文混合>，总字符量80-100个字符"},{id:"3",name:"Shein标题",description:"根据图片内容生成Shein平台标题；要求：时尚感强，突出风格和场合，英文输出，60-80字符",rules:"任务：根据图片内容拟定标题；要求：时尚感强，突出风格和场合，遵循<Shein>平台要求，输出<英文>，总字符量60-80个字符"}]),b=D(()=>{const r=(w.value-1)*j.value,t=r+j.value;return a.value.slice(r,t)}),o=r=>{const t=new FileReader;t.onload=p=>{var U;const z=Date.now()+Math.random();a.value.push({id:z,name:r.name,url:(U=p.target)==null?void 0:U.result,file:r.raw})},t.readAsDataURL(r.raw)},T=r=>{},A=r=>{a.value.splice(r,1),b.value.length===0&&w.value>1&&w.value--},N=r=>{const t=r.map(p=>({id:p.id,name:p.name,url:p.url}));a.value.push(...t),g.value=!1,h.success(`已添加 ${r.length} 张图片`)},R=r=>{v.value=r.rules,V.value=!1,h.success(`已应用预设：${r.name}`)},W=r=>{I.value.push(r),h.success("预设创建成功！")},G=me(re),c=D(()=>a.value.length*3),P=D(()=>c.value===0?0:ge(re,c.value)),L=D(()=>xe(re,c.value)),q=D(()=>{if(a.value.length===0)return"提交任务";const r=P.value;return r===0?"提交任务（免费）":`提交任务（¥${r.toFixed(2)}）`}),Z=()=>{if(a.value.length===0){h.warning("请先选择图片");return}if(!v.value.trim()){h.warning("请设置生成规则");return}h.success(`正在创建标题生成任务，共 ${a.value.length} 张图片`),J(),C("success"),C("update:modelValue",!1)},J=()=>{a.value=[],i.value=[],w.value=1,v.value="",V.value=!1};return(r,t)=>{const p=f("el-button"),z=f("el-upload"),U=f("el-input"),ee=f("el-pagination"),te=f("el-dialog");return u(),k(O,null,[s(te,{modelValue:y.value,"onUpdate:modelValue":t[8]||(t[8]=m=>y.value=m),title:"新建标题生成任务",width:"900px","align-center":"",onClose:J},{footer:l(()=>[e("div",Oe,[e("div",Qe,[a.value.length>0?(u(),k("div",qe,[e("div",null,"将处理 "+n(a.value.length)+" 张图片，生成 "+n(c.value)+" 个标题",1),L.value.hasFreeQuota&&L.value.freeItems>0?(u(),k("div",Je," 免费额度："+n(L.value.freeItems)+" 个标题，付费："+n(L.value.chargeableItems)+" 个标题 ",1)):F("",!0),Q(G)?(u(),k("div",Ke," 单价：¥"+n(Q(G).unitPrice.toFixed(2))+"/个标题 ",1)):F("",!0)])):(u(),k("div",Xe,"请先选择图片和设置生成规则"))]),e("div",Ye,[s(p,{onClick:t[7]||(t[7]=m=>y.value=!1)},{default:l(()=>t[18]||(t[18]=[_("取消")])),_:1,__:[18]}),s(p,{type:"primary",onClick:Z,disabled:a.value.length===0||!v.value.trim()},{default:l(()=>[_(n(q.value),1)]),_:1},8,["disabled"])])])]),default:l(()=>[e("div",ye,[e("div",_e,[s(z,{ref:"uploadRef","file-list":i.value,"on-change":o,"on-remove":T,"auto-upload":!1,multiple:"",accept:"image/*","show-file-list":!1},{default:l(()=>[s(p,{type:"primary",size:"large"},{default:l(()=>t[11]||(t[11]=[e("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"})],-1),_(" 上传图片 ")])),_:1,__:[11]})]),_:1},8,["file-list"]),s(p,{size:"large",onClick:t[0]||(t[0]=m=>g.value=!0)},{default:l(()=>t[12]||(t[12]=[e("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1),_(" 从图库选择 ")])),_:1,__:[12]})]),e("div",Ce,[e("div",$e,[t[14]||(t[14]=e("h4",{class:"text-sm font-medium text-gray-900 dark:text-dark-text"},"生成规则设置",-1)),e("div",Ve,[e("button",{onMouseenter:t[1]||(t[1]=m=>B.value=!0),onMouseleave:t[2]||(t[2]=m=>B.value=!1),onClick:t[3]||(t[3]=m=>V.value=!V.value),class:"text-sm text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 transition-colors duration-200"}," 使用预设 ",32),V.value?(u(),k("div",je,[e("div",Te,[e("div",Me,[t[13]||(t[13]=e("h5",{class:"text-sm font-medium text-gray-900 dark:text-dark-text"},"预设列表",-1)),e("button",{onClick:t[4]||(t[4]=m=>$.value=!0),class:"text-xs text-blue-600 dark:text-blue-400 hover:text-blue-700"}," 新建预设 ")]),e("div",Se,[(u(!0),k(O,null,se(I.value,m=>(u(),k("div",{key:m.id,onClick:E=>R(m),class:"p-3 border border-gray-200 dark:border-dark-border rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-dark-card transition-colors duration-200"},[e("div",ze,n(m.name),1),e("div",De,n(m.description),1)],8,Be))),128))])])])):F("",!0)])]),s(U,{modelValue:v.value,"onUpdate:modelValue":t[5]||(t[5]=m=>v.value=m),type:"textarea",rows:6,placeholder:"请输入生成规则，例如：根据图片内容拟定标题；要求：商品款式+商品颜色+印花图案+使用场景，遵循亚马逊平台要求，输出英文，总字符量不超过120个字符",class:"modern-textarea"},null,8,["modelValue"])]),a.value.length>0?(u(),k("div",Le,[e("div",Pe,[e("h4",He," 已选择图片 ("+n(a.value.length)+") ",1),e("div",Ie," 预计生成 "+n(a.value.length*3)+" 个标题 ",1)]),e("div",Ge,[e("div",Ue,[(u(!0),k(O,null,se(b.value,(m,E)=>(u(),k("div",{key:m.id||E,class:"relative group"},[e("div",Fe,[e("img",{src:m.url,alt:m.name,class:"w-full h-20 object-cover rounded-lg border border-gray-200 dark:border-dark-border"},null,8,Ae),e("button",{onClick:d=>A(E+(w.value-1)*j.value),class:"absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200 hover:bg-red-600"},t[15]||(t[15]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]),8,Ne),t[16]||(t[16]=e("div",{class:"absolute bottom-1 right-1 bg-blue-500 text-white text-xs px-1.5 py-0.5 rounded"}," ×3 ",-1))]),e("p",Re,n(m.name),1)]))),128))]),a.value.length>j.value?(u(),k("div",We,[s(ee,{"current-page":w.value,"onUpdate:currentPage":t[6]||(t[6]=m=>w.value=m),"page-size":j.value,total:a.value.length,layout:"prev, pager, next",small:""},null,8,["current-page","page-size","total"])])):F("",!0)])])):(u(),k("div",Ee,t[17]||(t[17]=[e("svg",{class:"w-12 h-12 text-gray-400 dark:text-dark-text-secondary mx-auto mb-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1),e("p",{class:"text-gray-500 dark:text-dark-text-secondary"},"请上传图片或从图库选择",-1)])))])]),_:1},8,["modelValue"]),s(ce,{modelValue:g.value,"onUpdate:modelValue":t[9]||(t[9]=m=>g.value=m),"theme-color":"blue",onSelect:N},null,8,["modelValue"]),s(we,{modelValue:$.value,"onUpdate:modelValue":t[10]||(t[10]=m=>$.value=m),onSuccess:W},null,8,["modelValue"])],64)}}}),et=Y(Ze,[["__scopeId","data-v-d3810cc9"]]),tt={class:"flex items-center justify-between p-6 border-b border-gray-100 dark:border-dark-border"},rt={class:"flex items-center space-x-3"},st={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},ot={key:0,class:"p-6 grid grid-cols-2 md:grid-cols-4 gap-4"},at={class:"bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 p-4 rounded-xl border border-blue-200 dark:border-blue-800"},lt={class:"flex items-center space-x-2"},nt={class:"text-sm font-bold text-blue-900 dark:text-blue-100"},dt={class:"bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 p-4 rounded-xl border border-green-200 dark:border-green-800"},it={class:"flex items-center space-x-2"},ut={class:"text-sm font-bold text-green-900 dark:text-green-100"},ct={class:"bg-gradient-to-br from-indigo-50 to-indigo-100 dark:from-indigo-900/20 dark:to-indigo-800/20 p-4 rounded-xl border border-indigo-200 dark:border-indigo-800"},mt={class:"flex items-center space-x-2"},gt={class:"text-sm font-bold text-indigo-900 dark:text-indigo-100"},xt={class:"bg-gradient-to-br from-cyan-50 to-cyan-100 dark:from-cyan-900/20 dark:to-cyan-800/20 p-4 rounded-xl border border-cyan-200 dark:border-cyan-800"},pt={class:"flex items-center space-x-2"},kt={class:"text-sm font-bold text-cyan-900 dark:text-cyan-100"},vt={class:"px-6 pb-4"},bt={class:"bg-gray-50 dark:bg-dark-card/50 p-4 rounded-lg border border-gray-100 dark:border-dark-border"},ht={class:"text-sm text-gray-700 dark:text-dark-text-secondary whitespace-pre-line"},ft={class:"px-6 pb-6"},wt={class:"bg-white dark:bg-dark-surface rounded-xl border border-gray-200 dark:border-dark-border overflow-hidden"},yt={key:0,class:"flex justify-center"},_t={key:1,class:"flex justify-center"},Ct={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},$t={key:0,class:"space-y-2"},Vt={class:"flex items-center justify-between"},jt={class:"text-sm text-gray-900 dark:text-dark-text"},Tt=["onClick"],Mt={key:1,class:"text-sm text-gray-500 dark:text-dark-text-secondary"},St={key:0,class:"flex justify-center"},Bt=["onClick"],zt={key:1,class:"flex justify-center"},Dt={class:"flex items-center justify-between p-6 border-t border-gray-100 dark:border-dark-border bg-gray-50 dark:bg-dark-card/50"},Lt={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},Pt=X({__name:"ViewDetailsDialog",props:{modelValue:{type:Boolean},task:{}},emits:["update:modelValue"],setup(H,{emit:M}){const S=H,C=M,y=D({get:()=>S.modelValue,set:b=>C("update:modelValue",b)}),g=x(!1),V=x(0),B=x("任务：根据图片内容拟定标题；要求：商品款式+商品颜色+印花图案+使用场景，遵循<亚马逊>平台要求，输出<英文>，总字符量不超过120个字符"),$=x([{index:1,image:"https://picsum.photos/400/400?random=1",fileName:"product_001.jpg",titles:["Women's Floral Print Summer Dress - Blue Casual Maxi Dress with Pockets for Beach Vacation","Elegant Blue Floral Maxi Dress for Women - Casual Summer Beach Vacation Outfit with Side Pockets","Summer Beach Maxi Dress - Women's Blue Floral Print Casual Outfit with Pockets for Vacation"],status:"success"},{index:2,image:"https://picsum.photos/400/400?random=2",fileName:"product_002.jpg",titles:["Men's Casual Linen Shirt - White Button Down Short Sleeve Beach Shirt for Summer Vacation","Summer White Linen Shirt for Men - Casual Short Sleeve Button Down Beach Wear for Vacation","Men's White Short Sleeve Linen Shirt - Casual Button Down Beach Wear for Summer Vacation"],status:"success"},{index:3,image:"https://picsum.photos/400/400?random=3",fileName:"product_003.jpg",titles:[],status:"failed"}]);V.value=$.value.length;const i=b=>({completed:"已完成",processing:"处理中",failed:"失败",pending:"等待中"})[b]||"未知",a=b=>({success:"成功",failed:"失败"})[b]||"未知",w=()=>{y.value=!1},j=b=>{navigator.clipboard.writeText(b).then(()=>{h.success("标题已复制到剪贴板")}).catch(()=>{h.error("复制失败，请手动复制")})},v=b=>{h.success(`正在导出 ${b.fileName} 的标题`)},I=()=>{h.success("正在导出所有标题...")};return(b,o)=>{const T=f("el-table-column"),A=f("el-image"),N=f("el-tag"),R=f("el-table"),W=f("el-dialog"),G=le("loading");return u(),K(W,{modelValue:y.value,"onUpdate:modelValue":o[0]||(o[0]=c=>y.value=c),width:"1200px","before-close":w,"show-close":!1,class:"modern-dialog"},{header:l(()=>{var c;return[e("div",tt,[e("div",rt,[o[2]||(o[2]=e("div",{class:"w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})])],-1)),e("div",null,[o[1]||(o[1]=e("h3",{class:"text-xl font-bold text-gray-900 dark:text-dark-text"},"标题生成详情",-1)),e("p",st,"任务ID: "+n(((c=b.task)==null?void 0:c.id)||""),1)])]),e("button",{onClick:w,class:"p-2 text-gray-400 hover:text-gray-600 dark:text-dark-text-secondary dark:hover:text-dark-text rounded-lg hover:bg-gray-100 dark:hover:bg-dark-card transition-all duration-200"},o[3]||(o[3]=[e("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))])]}),footer:l(()=>[e("div",Dt,[e("div",Lt," 共 "+n(V.value)+" 条生成结果 ",1),e("div",{class:"flex items-center space-x-3"},[e("button",{onClick:w,class:"px-6 py-2.5 text-gray-700 dark:text-dark-text font-medium rounded-lg border border-gray-300 dark:border-dark-border hover:bg-gray-50 dark:hover:bg-dark-border transition-all duration-200"}," 关闭 "),e("button",{onClick:I,class:"inline-flex items-center px-6 py-2.5 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-medium rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"},o[17]||(o[17]=[e("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"})],-1),_(" 导出全部 ")]))])])]),default:l(()=>[b.task?(u(),k("div",ot,[e("div",at,[e("div",lt,[o[5]||(o[5]=e("div",{class:"w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1)),e("div",null,[o[4]||(o[4]=e("p",{class:"text-xs text-blue-600 dark:text-blue-400 font-medium"},"任务状态",-1)),e("p",nt,n(i(b.task.status)),1)])])]),e("div",dt,[e("div",it,[o[7]||(o[7]=e("div",{class:"w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})])],-1)),e("div",null,[o[6]||(o[6]=e("p",{class:"text-xs text-green-600 dark:text-green-400 font-medium"},"图片数量",-1)),e("p",ut,n(b.task.imageCount),1)])])]),e("div",ct,[e("div",mt,[o[9]||(o[9]=e("div",{class:"w-8 h-8 bg-indigo-500 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})])],-1)),e("div",null,[o[8]||(o[8]=e("p",{class:"text-xs text-indigo-600 dark:text-indigo-400 font-medium"},"生成数量",-1)),e("p",gt,n(b.task.generatedCount),1)])])]),e("div",xt,[e("div",pt,[o[11]||(o[11]=e("div",{class:"w-8 h-8 bg-cyan-500 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"})])],-1)),e("div",null,[o[10]||(o[10]=e("p",{class:"text-xs text-cyan-600 dark:text-cyan-400 font-medium"},"使用预设",-1)),e("p",kt,n(b.task.preset||"自定义规则"),1)])])])])):F("",!0),e("div",vt,[e("div",bt,[o[12]||(o[12]=e("h4",{class:"text-sm font-medium text-gray-900 dark:text-dark-text mb-2"},"生成规则",-1)),e("p",ht,n(B.value),1)])]),e("div",ft,[o[16]||(o[16]=e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-dark-text mb-4"},"标题生成结果",-1)),e("div",wt,[ae((u(),K(R,{data:$.value,style:{width:"100%"},"max-height":"400",class:"modern-table"},{default:l(()=>[s(T,{prop:"index",label:"序号",width:"80",align:"center"}),s(T,{label:"图片",width:"100",align:"center"},{default:l(c=>[c.row.status==="success"?(u(),k("div",yt,[s(A,{src:c.row.image,"preview-src-list":[c.row.image],fit:"cover",class:"w-16 h-16 rounded-lg border border-gray-200 dark:border-dark-border","preview-teleported":!0},null,8,["src","preview-src-list"])])):(u(),k("div",_t,o[13]||(o[13]=[e("div",{class:"w-16 h-16 bg-gray-100 dark:bg-dark-card rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})])],-1)])))]),_:1}),s(T,{prop:"fileName",label:"文件名",width:"150",align:"center"},{default:l(c=>[e("span",Ct,n(c.row.fileName),1)]),_:1}),s(T,{prop:"title",label:"生成标题","min-width":"300"},{default:l(c=>[c.row.status==="success"?(u(),k("div",$t,[(u(!0),k(O,null,se(c.row.titles,(P,L)=>(u(),k("div",{key:L,class:"p-2 bg-gray-50 dark:bg-dark-card rounded border border-gray-200 dark:border-dark-border"},[e("div",Vt,[e("span",jt,n(P),1),e("button",{onClick:q=>j(P),class:"text-blue-500 hover:text-blue-600 dark:text-blue-400 dark:hover:text-blue-300"},o[14]||(o[14]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-2M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3"})],-1)]),8,Tt)])]))),128))])):(u(),k("div",Mt,n(a(c.row.status)),1))]),_:1}),s(T,{prop:"status",label:"状态",width:"100",align:"center"},{default:l(c=>[s(N,{type:c.row.status==="success"?"success":"danger",size:"small"},{default:l(()=>[_(n(a(c.row.status)),1)]),_:2},1032,["type"])]),_:1}),s(T,{label:"操作",width:"120",align:"center"},{default:l(c=>[c.row.status==="success"?(u(),k("div",St,[e("button",{onClick:P=>v(c.row),class:"inline-flex items-center px-3 py-1.5 text-sm font-medium text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/30 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-900/50 transition-all duration-200"}," 导出 ",8,Bt)])):(u(),k("div",zt,o[15]||(o[15]=[e("span",{class:"text-sm text-gray-400"},"-",-1)])))]),_:1})]),_:1},8,["data"])),[[G,g.value]])])])]),_:1},8,["modelValue"])}}}),Ht=Y(Pt,[["__scopeId","data-v-752f1d1d"]]),It={class:"space-y-6"},Gt={class:"grid grid-cols-1 md:grid-cols-4 gap-6"},Ut={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},Ft={class:"flex items-center justify-between"},At={class:"text-2xl font-bold text-gray-900 dark:text-dark-text"},Nt={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},Rt={class:"flex items-center justify-between"},Wt={class:"text-2xl font-bold text-green-600 dark:text-green-400"},Et={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},Ot={class:"flex items-center justify-between"},Qt={class:"text-2xl font-bold text-blue-600 dark:text-blue-400"},qt={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},Jt={class:"flex items-center justify-between"},Kt={class:"text-2xl font-bold text-orange-600 dark:text-orange-400"},Xt={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border"},Yt={class:"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0"},Zt={class:"flex items-center space-x-3"},er={key:0,class:"flex items-center space-x-2 ml-4 pl-4 border-l border-gray-200 dark:border-dark-border"},tr={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},rr={class:"bg-white dark:bg-dark-surface rounded-xl shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border overflow-hidden"},sr={class:"overflow-x-auto"},or={class:"font-mono text-sm text-gray-600 dark:text-dark-text-secondary"},ar={class:"flex items-center space-x-2"},lr={class:"font-medium text-gray-900 dark:text-dark-text"},nr={class:"font-medium text-blue-600 dark:text-blue-400"},dr={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},ir={class:"flex items-center space-x-2"},ur={class:"w-6 h-6 bg-gradient-to-br from-blue-400 to-blue-500 rounded-full flex items-center justify-center"},cr={class:"text-white text-xs font-medium"},mr={class:"text-sm text-gray-900 dark:text-dark-text"},gr={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},xr={class:"flex items-center space-x-2"},pr=["onClick"],kr={class:"flex justify-between items-center px-6 py-4 border-t border-gray-100 dark:border-dark-border bg-gray-50 dark:bg-dark-card/50"},vr={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},br=X({__name:"index",setup(H){const M=x(!1),S=x(!1),C=x(!1),y=x(null),g=x([]),V=x(2856),B=x(96.8),$=x(234),i=x(12),a=x({currentPage:1,pageSize:20,total:0}),w=x([{id:"TG001",imageCount:25,generatedCount:75,status:"completed",preset:"亚马逊标题",operator:"张三",createTime:"2024-01-15 14:30:25"},{id:"TG002",imageCount:18,generatedCount:54,status:"processing",preset:"Temu标题",operator:"李四",createTime:"2024-01-15 13:45:12"},{id:"TG003",imageCount:32,generatedCount:96,status:"completed",preset:"Shein标题",operator:"王五",createTime:"2024-01-15 12:20:08"},{id:"TG004",imageCount:15,generatedCount:0,status:"failed",preset:"自定义规则",operator:"赵六",createTime:"2024-01-15 11:15:33"},{id:"TG005",imageCount:28,generatedCount:0,status:"pending",preset:"亚马逊标题",operator:"钱七",createTime:"2024-01-15 10:30:45"},{id:"TG006",imageCount:22,generatedCount:66,status:"completed",preset:"eBay标题",operator:"孙八",createTime:"2024-01-15 09:45:22"},{id:"TG007",imageCount:19,generatedCount:38,status:"processing",preset:"自定义规则",operator:"周九",createTime:"2024-01-15 08:20:15"},{id:"TG008",imageCount:35,generatedCount:105,status:"completed",preset:"亚马逊标题",operator:"吴十",createTime:"2024-01-14 16:30:40"},{id:"TG009",imageCount:12,generatedCount:36,status:"completed",preset:"Temu标题",operator:"郑一",createTime:"2024-01-14 15:15:28"},{id:"TG010",imageCount:26,generatedCount:52,status:"processing",preset:"Shein标题",operator:"王二",createTime:"2024-01-14 14:45:55"}]),j=D(()=>{const r=(a.value.currentPage-1)*a.value.pageSize,t=r+a.value.pageSize;return w.value.slice(r,t)});ne(()=>{v()});const v=()=>{M.value=!0,setTimeout(()=>{a.value.total=w.value.length,M.value=!1},500)},I=r=>{const t={completed:"bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300",processing:"bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300",failed:"bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300",pending:"bg-gray-100 dark:bg-gray-900/30 text-gray-800 dark:text-gray-300"};return t[r]||t.pending},b=r=>({completed:"已完成",processing:"处理中",failed:"失败",pending:"等待中"})[r]||"未知",o=r=>{g.value=r},T=r=>{y.value=r,C.value=!0},A=r=>{const{action:t,row:p}=r;switch(t){case"batchListing":G(p);break;case"smartCrop":c(p);break;case"oneClickCutout":P(p);break;case"superSplit":L(p);break;case"copyrightDetection":q(p);break}},N=()=>{h.success("导出表格功能开发中...")},R=()=>{h.success(`正在批量导出 ${g.value.length} 个任务...`)},W=()=>{h.success("标题生成任务创建成功！"),v()},G=r=>{h.success(`正在为标题生成任务 ${r.id} 创建批量刊登任务...`)},c=r=>{h.success(`正在为标题生成任务 ${r.id} 创建智能裁图任务...`)},P=r=>{h.success(`正在为标题生成任务 ${r.id} 创建一键抠图任务...`)},L=r=>{h.success(`正在为标题生成任务 ${r.id} 创建超级裂变任务...`)},q=r=>{h.success(`正在为标题生成任务 ${r.id} 创建侵权检测任务...`)},Z=r=>{a.value.pageSize=r,a.value.currentPage=1,v()},J=r=>{a.value.currentPage=r,v()};return(r,t)=>{const p=f("el-table-column"),z=f("el-dropdown-item"),U=f("el-dropdown-menu"),ee=f("el-dropdown"),te=f("el-table"),m=f("el-pagination"),E=le("loading");return u(),k(O,null,[e("div",It,[t[26]||(t[26]=de('<div class="bg-gradient-to-r from-blue-50 to-cyan-50 dark:from-blue-900/20 dark:to-cyan-900/20 rounded-2xl p-6 border border-blue-100 dark:border-blue-800" data-v-76e7460c><div class="flex items-center space-x-3" data-v-76e7460c><div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center" data-v-76e7460c><svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-76e7460c><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" data-v-76e7460c></path></svg></div><div data-v-76e7460c><h1 class="text-2xl font-bold text-gray-900 dark:text-dark-text" data-v-76e7460c>标题生成</h1><p class="mt-1 text-sm text-gray-600 dark:text-dark-text-secondary" data-v-76e7460c>AI智能生成吸引人的商品标题</p></div></div></div>',1)),e("div",Gt,[e("div",Ut,[e("div",Ft,[e("div",null,[t[5]||(t[5]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"总生成数",-1)),e("p",At,n(V.value),1)]),t[6]||(t[6]=e("div",{class:"w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-blue-600 dark:text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})])],-1))])]),e("div",Nt,[e("div",Rt,[e("div",null,[t[7]||(t[7]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"成功率",-1)),e("p",Wt,n(B.value)+"%",1)]),t[8]||(t[8]=e("div",{class:"w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-green-600 dark:text-green-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1))])]),e("div",Et,[e("div",Ot,[e("div",null,[t[9]||(t[9]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"今日生成",-1)),e("p",Qt,n($.value),1)]),t[10]||(t[10]=e("div",{class:"w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-blue-600 dark:text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})])],-1))])]),e("div",qt,[e("div",Jt,[e("div",null,[t[11]||(t[11]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"处理中",-1)),e("p",Kt,n(i.value),1)]),t[12]||(t[12]=e("div",{class:"w-12 h-12 bg-orange-100 dark:bg-orange-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-orange-600 dark:text-orange-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1))])])]),e("div",Xt,[e("div",Yt,[e("div",Zt,[e("button",{onClick:t[0]||(t[0]=d=>S.value=!0),class:"inline-flex items-center px-4 py-2.5 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-medium rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"},[s(Q(ie),{class:"w-5 h-5 mr-2"}),t[13]||(t[13]=_(" 新建生成 "))]),e("button",{onClick:N,class:"inline-flex items-center px-4 py-2.5 bg-white dark:bg-dark-card text-gray-700 dark:text-dark-text font-medium rounded-lg border border-gray-300 dark:border-dark-border hover:bg-gray-50 dark:hover:bg-dark-border shadow-sm hover:shadow-md transition-all duration-200"},[s(Q(oe),{class:"w-5 h-5 mr-2"}),t[14]||(t[14]=_(" 导出表格 "))]),g.value.length>0?(u(),k("div",er,[e("span",tr," 已选择 "+n(g.value.length)+" 项 ",1),e("button",{onClick:R,class:"inline-flex items-center px-3 py-1.5 bg-blue-500 hover:bg-blue-600 text-white text-sm font-medium rounded-lg transition-all duration-200"},[s(Q(oe),{class:"w-4 h-4 mr-1"}),t[15]||(t[15]=_(" 批量导出 "))])])):F("",!0)])])]),e("div",rr,[t[25]||(t[25]=e("div",{class:"px-6 py-4 border-b border-gray-100 dark:border-dark-border"},[e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-dark-text"},"标题生成任务列表"),e("p",{class:"text-sm text-gray-600 dark:text-dark-text-secondary mt-1"},"管理和监控您的所有标题生成任务")],-1)),e("div",sr,[ae((u(),K(te,{data:j.value,style:{width:"100%"},onSelectionChange:o,class:"modern-table","header-cell-style":{backgroundColor:"var(--el-bg-color-page)",color:"var(--el-text-color-primary)",fontWeight:"600",borderBottom:"1px solid var(--el-border-color-light)"},"row-style":{backgroundColor:"transparent"}},{default:l(()=>[s(p,{type:"selection",width:"55"}),s(p,{prop:"id",label:"标题ID",width:"120"},{default:l(d=>[e("span",or,n(d.row.id),1)]),_:1}),s(p,{label:"生成数量",width:"150"},{default:l(d=>[e("div",ar,[t[16]||(t[16]=e("span",{class:"text-sm text-gray-600 dark:text-dark-text-secondary"},"图片:",-1)),e("span",lr,n(d.row.imageCount),1),t[17]||(t[17]=e("span",{class:"text-gray-400"},"|",-1)),t[18]||(t[18]=e("span",{class:"text-sm text-gray-600 dark:text-dark-text-secondary"},"标题:",-1)),e("span",nr,n(d.row.generatedCount),1)])]),_:1}),s(p,{prop:"status",label:"生成状态",width:"120"},{default:l(d=>[e("span",{class:ue([I(d.row.status),"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"])},n(b(d.row.status)),3)]),_:1}),s(p,{prop:"preset",label:"使用预设",width:"150"},{default:l(d=>[e("span",dr,n(d.row.preset||"自定义规则"),1)]),_:1}),s(p,{prop:"operator",label:"操作人",width:"100"},{default:l(d=>[e("div",ir,[e("div",ur,[e("span",cr,n(d.row.operator.charAt(0)),1)]),e("span",mr,n(d.row.operator),1)])]),_:1}),s(p,{prop:"createTime",label:"创建时间",width:"180"},{default:l(d=>[e("div",gr,n(d.row.createTime),1)]),_:1}),s(p,{label:"操作",width:"180"},{default:l(d=>[e("div",xr,[e("button",{onClick:hr=>T(d.row),class:"inline-flex items-center px-3 py-1.5 text-sm font-medium text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 bg-blue-50 dark:bg-blue-900/20 hover:bg-blue-100 dark:hover:bg-blue-900/30 rounded-lg transition-all duration-200"}," 查看详情 ",8,pr),s(ee,{onCommand:A,trigger:"click"},{dropdown:l(()=>[s(U,null,{default:l(()=>[s(z,{command:{action:"batchListing",row:d.row}},{default:l(()=>t[19]||(t[19]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-green-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"})]),e("span",null,"批量刊登")],-1)])),_:2,__:[19]},1032,["command"]),s(z,{command:{action:"smartCrop",row:d.row}},{default:l(()=>t[20]||(t[20]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-yellow-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})]),e("span",null,"智能裁图")],-1)])),_:2,__:[20]},1032,["command"]),s(z,{command:{action:"oneClickCutout",row:d.row}},{default:l(()=>t[21]||(t[21]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-pink-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m-9 0h10m-10 0a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V6a2 2 0 00-2-2"})]),e("span",null,"一键抠图")],-1)])),_:2,__:[21]},1032,["command"]),s(z,{command:{action:"superSplit",row:d.row}},{default:l(()=>t[22]||(t[22]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-purple-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 10V3L4 14h7v7l9-11h-7z"})]),e("span",null,"超级裂变")],-1)])),_:2,__:[22]},1032,["command"]),s(z,{command:{action:"copyrightDetection",row:d.row}},{default:l(()=>t[23]||(t[23]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-blue-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"})]),e("span",null,"侵权检测")],-1)])),_:2,__:[23]},1032,["command"])]),_:2},1024)]),default:l(()=>[t[24]||(t[24]=e("button",{class:"inline-flex items-center px-2 py-1.5 text-sm font-medium text-gray-600 dark:text-dark-text-secondary hover:text-gray-700 dark:hover:text-dark-text bg-gray-50 dark:bg-dark-card hover:bg-gray-100 dark:hover:bg-dark-border rounded-lg transition-all duration-200"},[_(" 更多 "),e("svg",{class:"w-4 h-4 ml-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 9l-7 7-7-7"})])],-1))]),_:2,__:[24]},1024)])]),_:1})]),_:1},8,["data"])),[[E,M.value]])]),e("div",kr,[e("div",vr," 共 "+n(a.value.total)+" 条记录 ",1),s(m,{"current-page":a.value.currentPage,"onUpdate:currentPage":t[1]||(t[1]=d=>a.value.currentPage=d),"page-size":a.value.pageSize,"onUpdate:pageSize":t[2]||(t[2]=d=>a.value.pageSize=d),"page-sizes":[10,20,50,100],total:a.value.total,layout:"sizes, prev, pager, next, jumper",onSizeChange:Z,onCurrentChange:J,class:"modern-pagination"},null,8,["current-page","page-size","total"])])])]),s(et,{modelValue:S.value,"onUpdate:modelValue":t[3]||(t[3]=d=>S.value=d),onSuccess:W},null,8,["modelValue"]),s(Ht,{modelValue:C.value,"onUpdate:modelValue":t[4]||(t[4]=d=>C.value=d),task:y.value},null,8,["modelValue","task"])],64)}}}),$r=Y(br,[["__scopeId","data-v-76e7460c"]]);export{$r as default};
