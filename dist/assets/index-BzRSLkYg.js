import{K as Ce,d as de,r as j,e as D,L as ce,q as G,j as s,h as f,a as e,g as o,c as g,M as $,k as oe,n as P,b as Y,N as ue,s as pe,t as m,D as me,O as ge,y as ne,F as le,A as k,C as U,P as X,E as h,o as c,_ as ie,Q as xe,f as $e,p as Ve,R as Me,l as Te}from"./index-BTucjZQh.js";import{g as Se,c as je,a as ze}from"./billing-DsHNwwW1.js";import{r as ke}from"./MagnifyingGlassIcon-TSA1g2un.js";import"./billing-Ciq-WFQK.js";var v=(p=>(p.PRODUCT="product",p.STORE="store",p.SEARCH="search",p.OTHER="other",p))(v||{}),I=(p=>(p.AMAZON="amazon",p.TEMU="temu",p.SHEIN="shein",p))(I||{});const B=Ce({tasks:[{id:"COL001",type:"商品",platform:"亚马逊",targetCount:100,successCount:95,status:"已完成",collector:"admin",createTime:"2024-01-15 10:30:00",links:`https://amazon.com/product1
https://amazon.com/product2`},{id:"COL002",type:"店铺",platform:"Temu",targetCount:50,successCount:45,status:"部分失败",collector:"admin",createTime:"2024-01-14 14:20:00",links:"https://temu.com/store1",failureReason:"部分商品链接失效"},{id:"COL003",type:"搜索",platform:"Shein",targetCount:200,successCount:150,status:"进行中",collector:"admin",createTime:"2024-01-13 09:15:00",keyword:"women dress"},{id:"COL004",type:"商品",platform:"亚马逊",targetCount:30,successCount:0,status:"失败",collector:"admin",createTime:"2024-01-12 16:45:00",links:"https://amazon.com/invalid-product",failureReason:"所有商品链接均无效"}],pagination:{currentPage:1,pageSize:20,total:0},loading:!1}),De=(p=1,_=20)=>(B.loading=!0,new Promise(S=>{setTimeout(()=>{const V=(p-1)*_,b=V+_,y=B.tasks.slice(V,b),r=B.tasks.length;B.pagination.currentPage=p,B.pagination.pageSize=_,B.pagination.total=r,B.loading=!1,S({data:y,total:r})},500)})),Ue=p=>new Promise(_=>{setTimeout(()=>{var V;const S={id:`COL${String(B.tasks.length+1).padStart(3,"0")}`,type:Be(p.type),platform:Re(p.platform),targetCount:p.type==="search"?100:((V=p.links)==null?void 0:V.split(`
`).filter(b=>b.trim()).length)||0,successCount:0,status:"待开始",collector:"admin",createTime:new Date().toLocaleString("zh-CN"),links:p.links,keyword:p.keyword};B.tasks.unshift(S),_(S)},1e3)}),Be=p=>({product:"商品",store:"店铺",search:"搜索",other:"其他"})[p]||"未知",Re=p=>({amazon:"亚马逊",temu:"Temu",shein:"Shein"})[p]||"未知",He=(p="excel")=>new Promise(_=>{setTimeout(()=>{console.log(`导出${p}格式的采集数据...`),_()},1e3)}),Oe=()=>new Promise(p=>{setTimeout(()=>{console.log("下载采集插件..."),p()},500)}),Pe={class:"p-6 space-y-6"},Ae={class:"space-y-3"},Ee={class:"grid grid-cols-2 gap-3 w-full"},Le=["value"],Ie={class:"flex items-center space-x-3"},Ne={class:"font-medium text-gray-900 dark:text-dark-text"},Fe={key:0,class:"absolute top-2 right-2 w-5 h-5 bg-primary-500 rounded-full flex items-center justify-center"},Ge={key:0,class:"space-y-3"},qe={class:"block text-sm font-semibold text-gray-900 dark:text-dark-text"},Ze={key:1,class:"space-y-3"},Qe={key:2,class:"space-y-3"},Ke={class:"grid grid-cols-3 gap-3 w-full"},We=["value","onUpdate:modelValue"],Je={class:"flex flex-col items-center space-y-1 w-full"},Xe={class:"text-lg"},Ye={class:"text-sm font-medium text-gray-900 dark:text-dark-text"},et={key:0,class:"absolute top-1 right-1 w-4 h-4 bg-primary-500 rounded-full flex items-center justify-center"},tt={key:3,class:"space-y-3"},rt={class:"flex items-center space-x-3"},at={key:4,class:"space-y-3"},ot={key:5,class:"space-y-3"},lt={key:6,class:"space-y-3"},st={class:"p-4 bg-gray-50 dark:bg-dark-card rounded-lg border border-gray-200 dark:border-dark-border"},nt={class:"flex items-center justify-between p-6 border-t border-gray-100 dark:border-dark-border bg-gray-50 dark:bg-dark-card/50"},dt={class:"text-sm text-gray-500 dark:text-dark-text-secondary"},it={key:0},ut={key:0,class:"text-green-600 dark:text-green-400"},ct={key:1,class:"text-blue-600 dark:text-blue-400"},pt={class:"flex items-center space-x-3"},mt=["disabled"],gt={key:0,class:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",fill:"none",viewBox:"0 0 24 24"},se="product-collection",xt=de({__name:"CreateCollectionDialog",props:{modelValue:{type:Boolean}},emits:["update:modelValue","success"],setup(p,{emit:_}){const S=p,V=_,b=j(),y=j(!1),r=j({type:v.PRODUCT,links:"",keyword:"",platform:I.AMAZON,priceMin:"",priceMax:"",pageCount:5,productType:""}),C=[{value:v.PRODUCT,label:"商品链接",icon:me},{value:v.STORE,label:"店铺链接",icon:ge},{value:v.SEARCH,label:"搜索采集",icon:ke},{value:v.OTHER,label:"其他方式",icon:ne}],N=[{value:I.AMAZON,label:"亚马逊",icon:"🛒"},{value:I.TEMU,label:"Temu",icon:"🛍️"},{value:I.SHEIN,label:"Shein",icon:"👗"}],A=[{value:"",label:"全部类型"},{value:"new",label:"新品"},{value:"hot",label:"热卖"},{value:"discount",label:"折扣商品"},{value:"bestseller",label:"畅销商品"},{value:"featured",label:"精选商品"}],E=D({get:()=>S.modelValue,set:T=>V("update:modelValue",T)}),q=D(()=>r.value.type===v.PRODUCT||r.value.type===v.STORE?r.value.links.trim()!=="":r.value.type===v.SEARCH?r.value.keyword.trim()!==""&&r.value.platform:r.value.type===v.OTHER),Z={type:[{required:!0,message:"请选择采集类型",trigger:"change"}],links:[{required:!0,message:"请输入链接地址",trigger:"blur"}],keyword:[{required:!0,message:"请输入搜索关键词",trigger:"blur"}],platform:[{required:!0,message:"请选择平台",trigger:"change"}]},Q=()=>{r.value.links="",r.value.keyword="",r.value.platform=I.AMAZON,r.value.priceMin="",r.value.priceMax="",r.value.pageCount=5,r.value.productType=""},L=()=>{h.success("采集器下载功能开发中...")},O=Se(se),x=D(()=>r.value.type===v.PRODUCT?r.value.links.split(`
`).filter(a=>a.trim()).length:(r.value.type===v.SEARCH,r.value.pageCount*10)),i=D(()=>x.value===0?0:je(se,x.value)),M=D(()=>ze(se,x.value)),K=D(()=>{if(x.value===0)return"开始采集";const T=i.value;return T===0?"开始采集（免费）":`开始采集（¥${T.toFixed(2)}）`}),W=async()=>{if(b.value)try{await b.value.validate(),y.value=!0,await Ue(r.value),h.success("采集任务创建成功！"),V("success"),R()}catch(T){console.error("创建采集任务失败:",T),h.error("创建失败，请重试")}finally{y.value=!1}},R=()=>{b.value&&b.value.resetFields(),r.value={type:v.PRODUCT,links:"",keyword:"",platform:I.AMAZON,priceMin:"",priceMax:"",pageCount:5,productType:""},V("update:modelValue",!1)};return ce(E,T=>{T&&(R(),V("update:modelValue",!0))}),(T,a)=>{const z=f("el-form-item"),u=f("el-input"),F=f("el-input-number"),ee=f("el-option"),te=f("el-select"),re=f("el-form"),ae=f("el-dialog");return c(),G(ae,{modelValue:E.value,"onUpdate:modelValue":a[7]||(a[7]=d=>E.value=d),width:"700px","before-close":R,"show-close":!1,class:"modern-dialog"},{header:s(()=>[e("div",{class:"flex items-center justify-between p-6 border-b border-gray-100 dark:border-dark-border"},[a[9]||(a[9]=e("div",{class:"flex items-center space-x-3"},[e("div",{class:"w-10 h-10 bg-gradient-to-br from-primary-500 to-primary-600 rounded-xl flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})])]),e("div",null,[e("h3",{class:"text-xl font-bold text-gray-900 dark:text-dark-text"},"新建采集任务"),e("p",{class:"text-sm text-gray-600 dark:text-dark-text-secondary"},"配置您的商品采集参数")])],-1)),e("button",{onClick:R,class:"p-2 text-gray-400 hover:text-gray-600 dark:text-dark-text-secondary dark:hover:text-dark-text rounded-lg hover:bg-gray-100 dark:hover:bg-dark-card transition-all duration-200"},a[8]||(a[8]=[e("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))])]),footer:s(()=>[e("div",nt,[e("div",dt,[x.value>0?(c(),g("div",it,[e("div",null,"预计采集 "+m(x.value)+" 个商品",1),M.value.hasFreeQuota&&M.value.freeItems>0?(c(),g("div",ut," 免费额度："+m(M.value.freeItems)+" 个，付费："+m(M.value.chargeableItems)+" 个 ",1)):$("",!0),k(O)?(c(),g("div",ct," 单价：¥"+m(k(O).unitPrice.toFixed(2))+"/个商品 ",1)):$("",!0)])):$("",!0)]),e("div",pt,[e("button",{onClick:R,class:"px-6 py-2.5 text-gray-700 dark:text-dark-text font-medium rounded-lg border border-gray-300 dark:border-dark-border hover:bg-gray-50 dark:hover:bg-dark-border transition-all duration-200"}," 取消 "),e("button",{onClick:W,disabled:!q.value||y.value,class:"inline-flex items-center px-6 py-2.5 bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 disabled:from-gray-400 disabled:to-gray-500 text-white font-medium rounded-lg shadow-lg hover:shadow-xl disabled:shadow-none transition-all duration-200 transform hover:scale-105 disabled:scale-100 disabled:cursor-not-allowed"},[y.value?(c(),g("svg",gt,a[26]||(a[26]=[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]))):$("",!0),U(" "+m(y.value?"创建中...":K.value),1)],8,mt)])])]),default:s(()=>[e("div",Pe,[o(re,{ref_key:"formRef",ref:b,model:r.value,rules:Z,"label-width":"0px",class:"space-y-6"},{default:s(()=>[e("div",Ae,[a[11]||(a[11]=e("label",{class:"block text-sm font-semibold text-gray-900 dark:text-dark-text"}," 采集类型 ",-1)),o(z,{prop:"type",class:"mb-0"},{default:s(()=>[e("div",Ee,[(c(),g(le,null,oe(C,d=>e("label",{key:d.value,class:P(["relative flex items-center p-4 border-2 rounded-xl cursor-pointer transition-all duration-200 hover:shadow-md",r.value.type===d.value?"border-primary-500 bg-primary-50 dark:bg-primary-900/20":"border-gray-200 dark:border-dark-border hover:border-gray-300 dark:hover:border-gray-600"])},[Y(e("input",{type:"radio",value:d.value,"onUpdate:modelValue":a[0]||(a[0]=J=>r.value.type=J),onChange:Q,class:"sr-only"},null,40,Le),[[ue,r.value.type]]),e("div",Ie,[e("div",{class:P(["w-8 h-8 rounded-lg flex items-center justify-center",r.value.type===d.value?"bg-primary-500 text-white":"bg-gray-100 dark:bg-dark-card text-gray-600 dark:text-dark-text-secondary"])},[(c(),G(pe(d.icon),{class:"w-4 h-4"}))],2),e("span",Ne,m(d.label),1)]),r.value.type===d.value?(c(),g("div",Fe,a[10]||(a[10]=[e("svg",{class:"w-3 h-3 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 13l4 4L19 7"})],-1)]))):$("",!0)],2)),64))])]),_:1})]),r.value.type===k(v).PRODUCT||r.value.type===k(v).STORE?(c(),g("div",Ge,[e("label",qe,m(r.value.type===k(v).PRODUCT?"商品链接":"店铺链接"),1),o(z,{prop:"links",class:"mb-0"},{default:s(()=>[o(u,{modelValue:r.value.links,"onUpdate:modelValue":a[1]||(a[1]=d=>r.value.links=d),type:"textarea",rows:6,placeholder:r.value.type===k(v).PRODUCT?"请输入商品链接地址，一行一条":"请输入店铺链接地址，一行一条",class:"modern-textarea"},null,8,["modelValue","placeholder"]),a[12]||(a[12]=e("div",{class:"flex items-start space-x-2 mt-2 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800"},[e("svg",{class:"w-4 h-4 text-blue-500 mt-0.5 flex-shrink-0",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})]),e("div",{class:"text-xs text-blue-700 dark:text-blue-300"},[e("p",{class:"font-medium"},"支持平台："),e("p",null,"亚马逊、Temu、Shein - 每行输入一个链接地址")])],-1))]),_:1,__:[12]})])):$("",!0),r.value.type===k(v).SEARCH?(c(),g("div",Ze,[a[13]||(a[13]=e("label",{class:"block text-sm font-semibold text-gray-900 dark:text-dark-text"}," 搜索关键词 ",-1)),o(z,{prop:"keyword",class:"mb-0"},{default:s(()=>[o(u,{modelValue:r.value.keyword,"onUpdate:modelValue":a[2]||(a[2]=d=>r.value.keyword=d),placeholder:"请输入搜索关键词，如：iPhone 15",class:"modern-input"},null,8,["modelValue"])]),_:1})])):$("",!0),r.value.type===k(v).SEARCH?(c(),g("div",Qe,[a[15]||(a[15]=e("label",{class:"block text-sm font-semibold text-gray-900 dark:text-dark-text"}," 选择平台 ",-1)),o(z,{prop:"platform",class:"mb-0"},{default:s(()=>[e("div",Ke,[(c(),g(le,null,oe(N,d=>e("label",{key:d.value,class:P(["relative flex items-center p-3 border-2 rounded-lg cursor-pointer transition-all duration-200",r.value.platform===d.value?"border-primary-500 bg-primary-50 dark:bg-primary-900/20":"border-gray-200 dark:border-dark-border hover:border-gray-300 dark:hover:border-gray-600"])},[Y(e("input",{type:"radio",value:d.value,"onUpdate:modelValue":J=>r.value.platform=J,class:"sr-only"},null,8,We),[[ue,r.value.platform]]),e("div",Je,[e("span",Xe,m(d.icon),1),e("span",Ye,m(d.label),1)]),r.value.platform===d.value?(c(),g("div",et,a[14]||(a[14]=[e("svg",{class:"w-2.5 h-2.5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 13l4 4L19 7"})],-1)]))):$("",!0)],2)),64))])]),_:1})])):$("",!0),r.value.type===k(v).SEARCH?(c(),g("div",tt,[a[19]||(a[19]=e("label",{class:"block text-sm font-semibold text-gray-900 dark:text-dark-text"}," 价格区间 ",-1)),o(z,{class:"mb-0"},{default:s(()=>[e("div",rt,[o(u,{modelValue:r.value.priceMin,"onUpdate:modelValue":a[3]||(a[3]=d=>r.value.priceMin=d),placeholder:"最低价格",type:"number",class:"modern-input",style:{width:"150px"}},{prefix:s(()=>a[16]||(a[16]=[U("¥")])),_:1},8,["modelValue"]),a[18]||(a[18]=e("span",{class:"text-gray-500 dark:text-dark-text-secondary"},"至",-1)),o(u,{modelValue:r.value.priceMax,"onUpdate:modelValue":a[4]||(a[4]=d=>r.value.priceMax=d),placeholder:"最高价格",type:"number",class:"modern-input",style:{width:"150px"}},{prefix:s(()=>a[17]||(a[17]=[U("¥")])),_:1},8,["modelValue"])])]),_:1})])):$("",!0),r.value.type===k(v).SEARCH?(c(),g("div",at,[a[21]||(a[21]=e("label",{class:"block text-sm font-semibold text-gray-900 dark:text-dark-text"}," 翻页数 ",-1)),o(z,{prop:"pageCount",class:"mb-0"},{default:s(()=>[o(F,{modelValue:r.value.pageCount,"onUpdate:modelValue":a[5]||(a[5]=d=>r.value.pageCount=d),min:1,max:50,placeholder:"请输入翻页数",class:"w-full"},null,8,["modelValue"]),a[20]||(a[20]=e("div",{class:"mt-1 text-xs text-gray-500 dark:text-dark-text-secondary"}," 建议设置1-10页，页数过多可能影响采集效率 ",-1))]),_:1,__:[20]})])):$("",!0),r.value.type===k(v).SEARCH?(c(),g("div",ot,[a[22]||(a[22]=e("label",{class:"block text-sm font-semibold text-gray-900 dark:text-dark-text"}," 商品类型 ",-1)),o(z,{prop:"productType",class:"mb-0"},{default:s(()=>[o(te,{modelValue:r.value.productType,"onUpdate:modelValue":a[6]||(a[6]=d=>r.value.productType=d),placeholder:"请选择商品类型",class:"w-full"},{default:s(()=>[(c(),g(le,null,oe(A,d=>o(ee,{key:d.value,label:d.label,value:d.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1})])):$("",!0),r.value.type===k(v).OTHER?(c(),g("div",lt,[a[25]||(a[25]=e("label",{class:"block text-sm font-semibold text-gray-900 dark:text-dark-text"}," 采集器工具 ",-1)),e("div",st,[e("button",{onClick:L,class:"inline-flex items-center px-4 py-2.5 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-medium rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"},[o(k(X),{class:"w-5 h-5 mr-2"}),a[23]||(a[23]=U(" 下载采集器 "))]),a[24]||(a[24]=e("div",{class:"mt-3 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-800"},[e("div",{class:"flex items-start space-x-2"},[e("svg",{class:"w-4 h-4 text-yellow-500 mt-0.5 flex-shrink-0",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})]),e("div",{class:"text-xs text-yellow-700 dark:text-yellow-300"},[e("p",{class:"font-medium"},"使用说明："),e("p",null,"1. 下载并安装采集器工具"),e("p",null,"2. 按照安装向导完成配置"),e("p",null,"3. 启动采集器开始自定义采集")])])],-1))])])):$("",!0)]),_:1},8,["model"])])]),_:1},8,["modelValue"])}}}),kt=ie(xt,[["__scopeId","data-v-1b0804ef"]]),vt={class:"flex items-center justify-between p-6 border-b border-gray-100 dark:border-dark-border"},bt={class:"flex items-center space-x-3"},ht={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},ft={key:0,class:"p-6 grid grid-cols-2 md:grid-cols-4 gap-4"},yt={class:"bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 p-4 rounded-xl border border-blue-200 dark:border-blue-800"},wt={class:"flex items-center space-x-2"},_t={class:"text-sm font-bold text-blue-900 dark:text-blue-100"},Ct={class:"bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 p-4 rounded-xl border border-green-200 dark:border-green-800"},$t={class:"flex items-center space-x-2"},Vt={class:"text-sm font-bold text-green-900 dark:text-green-100"},Mt={class:"bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 p-4 rounded-xl border border-purple-200 dark:border-purple-800"},Tt={class:"flex items-center space-x-2"},St={class:"text-sm font-bold text-purple-900 dark:text-purple-100"},jt={class:"bg-gradient-to-br from-emerald-50 to-emerald-100 dark:from-emerald-900/20 dark:to-emerald-800/20 p-4 rounded-xl border border-emerald-200 dark:border-emerald-800"},zt={class:"flex items-center space-x-2"},Dt={class:"text-sm font-bold text-emerald-900 dark:text-emerald-100"},Ut={class:"px-6 pb-6"},Bt={class:"bg-white dark:bg-dark-surface rounded-xl border border-gray-200 dark:border-dark-border overflow-hidden"},Rt={key:0,class:"flex justify-center"},Ht={key:1,class:"text-gray-400 text-xs"},Ot={key:0,class:"text-sm font-medium text-gray-900 dark:text-dark-text"},Pt={key:1,class:"text-gray-400 text-xs"},At={key:0,class:"text-red-600 dark:text-red-400 font-medium"},Et={key:1,class:"text-gray-400 text-xs"},Lt={key:0,class:"flex items-center justify-center"},It={key:1,class:"text-gray-400 text-xs"},Nt=["onClick"],Ft={class:"flex justify-center p-4 border-t border-gray-200 dark:border-dark-border"},Gt={class:"flex items-center justify-between p-6 border-t border-gray-100 dark:border-dark-border bg-gray-50 dark:bg-dark-card/50"},qt={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},Zt={class:"flex items-center space-x-3"},Qt=de({__name:"ViewDetailsDialog",props:{modelValue:{type:Boolean},collectionData:{}},emits:["update:modelValue"],setup(p,{emit:_}){const S=p,V=_,b=j(!1),y=j(1),r=j(20),C=j(0),N=j([{index:1,title:"Apple iPhone 15 Pro Max 256GB Natural Titanium",mainImage:"https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=200&h=200&fit=crop",price:"$1,199.00",rating:4.5,status:"成功",originalUrl:"https://amazon.com/example1"},{index:2,title:"Samsung Galaxy S24 Ultra 512GB Titanium Black",mainImage:"https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=200&h=200&fit=crop",price:"$1,299.99",rating:4.3,status:"成功",originalUrl:"https://amazon.com/example2"},{index:3,title:"",mainImage:"",price:"",rating:0,status:"失败",originalUrl:"https://amazon.com/example3"}]),A=D({get:()=>S.modelValue,set:x=>V("update:modelValue",x)}),E=x=>{window.open(x,"_blank")},q=()=>{h.success("导出详情功能开发中...")},Z=x=>{r.value=x,L()},Q=x=>{y.value=x,L()},L=()=>{b.value=!0,setTimeout(()=>{C.value=N.value.length,b.value=!1},300)},O=()=>{V("update:modelValue",!1)};return ce(A,x=>{x&&S.collectionData&&(y.value=1,L())}),(x,i)=>{const M=f("el-table-column"),K=f("el-image"),W=f("el-rate"),R=f("el-table"),T=f("el-pagination"),a=f("el-dialog"),z=xe("loading");return c(),G(a,{modelValue:A.value,"onUpdate:modelValue":i[2]||(i[2]=u=>A.value=u),width:"1200px","before-close":O,"show-close":!1,class:"modern-dialog"},{header:s(()=>{var u;return[e("div",vt,[e("div",bt,[i[4]||(i[4]=e("div",{class:"w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})])],-1)),e("div",null,[i[3]||(i[3]=e("h3",{class:"text-xl font-bold text-gray-900 dark:text-dark-text"},"采集详情",-1)),e("p",ht,"任务ID: "+m(((u=x.collectionData)==null?void 0:u.id)||""),1)])]),e("button",{onClick:O,class:"p-2 text-gray-400 hover:text-gray-600 dark:text-dark-text-secondary dark:hover:text-dark-text rounded-lg hover:bg-gray-100 dark:hover:bg-dark-card transition-all duration-200"},i[5]||(i[5]=[e("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))])]}),footer:s(()=>[e("div",Gt,[e("div",qt," 共 "+m(C.value)+" 条采集结果 ",1),e("div",Zt,[e("button",{onClick:O,class:"px-6 py-2.5 text-gray-700 dark:text-dark-text font-medium rounded-lg border border-gray-300 dark:border-dark-border hover:bg-gray-50 dark:hover:bg-dark-border transition-all duration-200"}," 关闭 "),e("button",{onClick:q,class:"inline-flex items-center px-6 py-2.5 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-medium rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"},[o(k(X),{class:"w-5 h-5 mr-2"}),i[15]||(i[15]=U(" 导出详情 "))])])])]),default:s(()=>[x.collectionData?(c(),g("div",ft,[e("div",yt,[e("div",wt,[i[7]||(i[7]=e("div",{class:"w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a1.994 1.994 0 01-1.414.586H7a4 4 0 01-4-4V7a4 4 0 014-4z"})])],-1)),e("div",null,[i[6]||(i[6]=e("p",{class:"text-xs text-blue-600 dark:text-blue-400 font-medium"},"采集类型",-1)),e("p",_t,m(x.collectionData.type),1)])])]),e("div",Ct,[e("div",$t,[i[9]||(i[9]=e("div",{class:"w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"})])],-1)),e("div",null,[i[8]||(i[8]=e("p",{class:"text-xs text-green-600 dark:text-green-400 font-medium"},"平台",-1)),e("p",Vt,m(x.collectionData.platform),1)])])]),e("div",Mt,[e("div",Tt,[i[11]||(i[11]=e("div",{class:"w-8 h-8 bg-purple-500 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})])],-1)),e("div",null,[i[10]||(i[10]=e("p",{class:"text-xs text-purple-600 dark:text-purple-400 font-medium"},"目标数量",-1)),e("p",St,m(x.collectionData.targetCount),1)])])]),e("div",jt,[e("div",zt,[i[13]||(i[13]=e("div",{class:"w-8 h-8 bg-emerald-500 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1)),e("div",null,[i[12]||(i[12]=e("p",{class:"text-xs text-emerald-600 dark:text-emerald-400 font-medium"},"成功数量",-1)),e("p",Dt,m(x.collectionData.successCount),1)])])])])):$("",!0),e("div",Ut,[i[14]||(i[14]=e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-dark-text mb-4"},"采集结果",-1)),e("div",Bt,[Y((c(),G(R,{data:N.value,style:{width:"100%"},"max-height":"400",class:"modern-table"},{default:s(()=>[o(M,{prop:"index",label:"序号",width:"80",align:"center"}),o(M,{label:"主图",width:"100",align:"center"},{default:s(u=>[u.row.mainImage?(c(),g("div",Rt,[o(K,{src:u.row.mainImage,"preview-src-list":[u.row.mainImage],fit:"cover",class:"w-16 h-16 rounded-lg border border-gray-200 dark:border-dark-border","preview-teleported":!0},null,8,["src","preview-src-list"])])):(c(),g("div",Ht,"无图片"))]),_:1}),o(M,{label:"标题","min-width":"200"},{default:s(u=>[u.row.title?(c(),g("div",Ot,m(u.row.title),1)):(c(),g("div",Pt,"无标题"))]),_:1}),o(M,{prop:"price",label:"价格",width:"100",align:"center"},{default:s(u=>[u.row.price?(c(),g("span",At,m(u.row.price),1)):(c(),g("span",Et,"-"))]),_:1}),o(M,{prop:"rating",label:"评分",width:"120",align:"center"},{default:s(u=>[u.row.rating?(c(),g("div",Lt,[o(W,{modelValue:u.row.rating,"onUpdate:modelValue":F=>u.row.rating=F,disabled:"","show-score":"","text-color":"#ff9900","score-template":"{value}",size:"small"},null,8,["modelValue","onUpdate:modelValue"])])):(c(),g("span",It,"-"))]),_:1}),o(M,{prop:"status",label:"状态",width:"100",align:"center"},{default:s(u=>[e("span",{class:P(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",u.row.status==="成功"?"bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300":"bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300"])},m(u.row.status),3)]),_:1}),o(M,{label:"操作",width:"120",align:"center"},{default:s(u=>[u.row.originalUrl?(c(),g("button",{key:0,onClick:F=>E(u.row.originalUrl),class:"text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 text-sm font-medium"}," 查看原链接 ",8,Nt)):$("",!0)]),_:1})]),_:1},8,["data"])),[[z,b.value]]),e("div",Ft,[o(T,{"current-page":y.value,"onUpdate:currentPage":i[0]||(i[0]=u=>y.value=u),"page-size":r.value,"onUpdate:pageSize":i[1]||(i[1]=u=>r.value=u),"page-sizes":[10,20,50],total:C.value,layout:"total, sizes, prev, pager, next",onSizeChange:Z,onCurrentChange:Q},null,8,["current-page","page-size","total"])])])])]),_:1},8,["modelValue"])}}}),Kt=ie(Qt,[["__scopeId","data-v-88a244da"]]),Wt={class:"space-y-6"},Jt={class:"grid grid-cols-1 md:grid-cols-4 gap-6"},Xt={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},Yt={class:"flex items-center justify-between"},er={class:"text-2xl font-bold text-gray-900 dark:text-dark-text"},tr={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},rr={class:"flex items-center justify-between"},ar={class:"text-2xl font-bold text-green-600 dark:text-green-400"},or={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},lr={class:"flex items-center justify-between"},sr={class:"text-2xl font-bold text-yellow-600 dark:text-yellow-400"},nr={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},dr={class:"flex items-center justify-between"},ir={class:"text-2xl font-bold text-purple-600 dark:text-purple-400"},ur={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border"},cr={class:"flex justify-between items-center"},pr={class:"flex items-center space-x-3"},mr={key:0,class:"flex items-center space-x-2 ml-4 pl-4 border-l border-gray-200 dark:border-dark-border"},gr={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},xr={class:"bg-white dark:bg-dark-surface rounded-xl shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border overflow-hidden"},kr={class:"overflow-x-auto"},vr={class:"font-mono text-sm font-medium text-primary-600 dark:text-primary-400"},br={class:"flex items-center space-x-3"},hr={class:"font-medium text-gray-900 dark:text-dark-text"},fr={class:"text-sm text-gray-500 dark:text-dark-text-secondary"},yr={class:"space-y-1"},wr={class:"flex items-center space-x-2"},_r={class:"font-medium text-gray-900 dark:text-dark-text"},Cr={class:"flex items-center space-x-2"},$r={class:"font-medium text-green-600 dark:text-green-400"},Vr={class:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5"},Mr={class:"space-y-2"},Tr={key:0},Sr=["onClick"],jr={class:"flex items-center space-x-2"},zr={class:"w-6 h-6 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center"},Dr={class:"text-white text-xs font-medium"},Ur={class:"text-sm font-medium text-gray-900 dark:text-dark-text"},Br={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},Rr={class:"flex items-center space-x-2"},Hr=["onClick"],Or={class:"flex justify-between items-center px-6 py-4 border-t border-gray-100 dark:border-dark-border bg-gray-50 dark:bg-dark-card/50"},Pr={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},Ar=de({__name:"index",setup(p){const _=j(!1),S=j(!1),V=j(null),b=j([]),y=j([]),{loading:r,pagination:C}=B,N=D(()=>b.value.length),A=D(()=>{if(b.value.length===0)return 0;const l=b.value.reduce((w,H)=>w+H.successCount,0),t=b.value.reduce((w,H)=>w+H.targetCount,0);return t>0?Math.round(l/t*100):0}),E=D(()=>b.value.filter(l=>l.status==="进行中").length),q=D(()=>{const l=new Date().toDateString();return b.value.filter(t=>new Date(t.createTime).toDateString()===l).length}),Z={商品:me,店铺:ge,搜索:ke,其他:ne},Q=l=>Z[l]||ne,L=l=>({商品:"bg-blue-100 dark:bg-blue-900/30",店铺:"bg-green-100 dark:bg-green-900/30",搜索:"bg-purple-100 dark:bg-purple-900/30",其他:"bg-gray-100 dark:bg-gray-900/30"})[l]||"bg-gray-100 dark:bg-gray-900/30",O=l=>({商品:"text-blue-600 dark:text-blue-400",店铺:"text-green-600 dark:text-green-400",搜索:"text-purple-600 dark:text-purple-400",其他:"text-gray-600 dark:text-gray-400"})[l]||"text-gray-600 dark:text-gray-400",x=l=>l==="已完成"?"bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300":l==="进行中"?"bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300":l.includes("失败")?"bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300":"bg-gray-100 dark:bg-gray-900/30 text-gray-800 dark:text-gray-300",i=l=>l==="已完成"?"bg-green-500":l==="进行中"?"bg-yellow-500":l.includes("失败")?"bg-red-500":"bg-gray-500",M=async()=>{try{await He("excel"),h.success("表格导出成功！")}catch{h.error("导出失败，请重试")}},K=async()=>{try{await Oe(),h.success("插件下载成功！")}catch{h.error("下载失败，请重试")}},W=l=>{const t=l.failureReason||"网络超时导致部分商品采集失败";h.info(`采集ID ${l.id} 的失败原因：${t}`)},R=l=>{V.value=l,S.value=!0},T=()=>{d(),h.success("采集任务创建成功！")},a=l=>{const{action:t,row:w}=l;switch(t){case"smartCrop":z(w);break;case"titleGenerate":u(w);break;case"batchListing":F(w);break;case"oneClickCutout":ee(w);break;case"superSplit":te(w);break;default:h.warning("未知操作")}},z=l=>{h.success(`正在为采集任务 ${l.id} 创建智能裁图任务...`)},u=l=>{h.success(`正在为采集任务 ${l.id} 创建标题生成任务...`)},F=l=>{h.success(`正在为采集任务 ${l.id} 创建批量刊登任务...`)},ee=l=>{h.success(`正在为采集任务 ${l.id} 创建一键抠图任务...`)},te=l=>{h.success(`正在为采集任务 ${l.id} 创建超级裂变任务...`)},re=l=>{C.pageSize=l,d()},ae=l=>{C.currentPage=l,d()},d=async()=>{try{const{data:l,total:t}=await De(C.currentPage,C.pageSize);b.value=l,C.total=t}catch{h.error("加载数据失败，请重试")}},J=l=>{y.value=l},ve=()=>{if(y.value.length===0){h.warning("请先选择要导出的任务");return}h.success(`正在导出 ${y.value.length} 个任务的数据...`)};return $e(()=>{d()}),(l,t)=>{const w=f("el-table-column"),H=f("el-dropdown-item"),be=f("el-dropdown-menu"),he=f("el-dropdown"),fe=f("el-table"),ye=f("el-pagination"),we=xe("loading");return c(),g("div",Wt,[t[26]||(t[26]=Ve('<div class="bg-gradient-to-r from-primary-50 to-blue-50 dark:from-primary-900/20 dark:to-blue-900/20 rounded-2xl p-6 border border-primary-100 dark:border-primary-800" data-v-698f84e1><div class="flex items-center space-x-3" data-v-698f84e1><div class="w-10 h-10 bg-gradient-to-br from-primary-500 to-primary-600 rounded-xl flex items-center justify-center" data-v-698f84e1><svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-698f84e1><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" data-v-698f84e1></path></svg></div><div data-v-698f84e1><h1 class="text-2xl font-bold text-gray-900 dark:text-dark-text" data-v-698f84e1>商品采集</h1><p class="mt-1 text-sm text-gray-600 dark:text-dark-text-secondary" data-v-698f84e1>智能采集全球电商平台商品信息</p></div></div></div>',1)),e("div",Jt,[e("div",Xt,[e("div",Yt,[e("div",null,[t[5]||(t[5]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"总采集数",-1)),e("p",er,m(N.value),1)]),t[6]||(t[6]=e("div",{class:"w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-blue-600 dark:text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})])],-1))])]),e("div",tr,[e("div",rr,[e("div",null,[t[7]||(t[7]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"成功率",-1)),e("p",ar,m(A.value)+"%",1)]),t[8]||(t[8]=e("div",{class:"w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-green-600 dark:text-green-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1))])]),e("div",or,[e("div",lr,[e("div",null,[t[9]||(t[9]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"进行中",-1)),e("p",sr,m(E.value),1)]),t[10]||(t[10]=e("div",{class:"w-12 h-12 bg-yellow-100 dark:bg-yellow-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-yellow-600 dark:text-yellow-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1))])]),e("div",nr,[e("div",dr,[e("div",null,[t[11]||(t[11]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"今日采集",-1)),e("p",ir,m(q.value),1)]),t[12]||(t[12]=e("div",{class:"w-12 h-12 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-purple-600 dark:text-purple-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})])],-1))])])]),e("div",ur,[e("div",cr,[e("div",pr,[e("button",{onClick:t[0]||(t[0]=n=>_.value=!0),class:"inline-flex items-center px-4 py-2.5 bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 text-white font-medium rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"},[o(k(Me),{class:"w-5 h-5 mr-2"}),t[13]||(t[13]=U(" 新建采集 "))]),e("button",{onClick:M,class:"inline-flex items-center px-4 py-2.5 bg-white dark:bg-dark-card text-gray-700 dark:text-dark-text font-medium rounded-lg border border-gray-300 dark:border-dark-border hover:bg-gray-50 dark:hover:bg-dark-border shadow-sm hover:shadow-md transition-all duration-200"},[o(k(X),{class:"w-5 h-5 mr-2"}),t[14]||(t[14]=U(" 导出表格 "))]),y.value.length>0?(c(),g("div",mr,[e("span",gr," 已选择 "+m(y.value.length)+" 项 ",1),e("button",{onClick:ve,class:"inline-flex items-center px-3 py-1.5 bg-blue-500 hover:bg-blue-600 text-white text-sm font-medium rounded-lg transition-all duration-200"},[o(k(X),{class:"w-4 h-4 mr-1"}),t[15]||(t[15]=U(" 批量导出 "))])])):$("",!0)]),e("div",null,[e("button",{onClick:K,class:"inline-flex items-center px-4 py-2.5 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-medium rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"},[o(k(X),{class:"w-5 h-5 mr-2"}),t[16]||(t[16]=U(" 下载采集插件 "))])])])]),e("div",xr,[t[25]||(t[25]=e("div",{class:"px-6 py-4 border-b border-gray-100 dark:border-dark-border"},[e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-dark-text"},"采集任务列表"),e("p",{class:"text-sm text-gray-600 dark:text-dark-text-secondary mt-1"},"管理和监控您的所有采集任务")],-1)),e("div",kr,[Y((c(),G(fe,{data:b.value,style:{width:"100%"},"header-cell-style":{backgroundColor:"var(--el-bg-color-page)",color:"var(--el-text-color-primary)",fontWeight:"600",borderBottom:"1px solid var(--el-border-color-light)"},"row-style":{backgroundColor:"transparent"},class:"modern-table",onSelectionChange:J},{default:s(()=>[o(w,{type:"selection",width:"55",align:"center"}),o(w,{prop:"id",label:"采集ID",width:"120"},{default:s(n=>[e("div",vr,m(n.row.id),1)]),_:1}),o(w,{label:"采集类型",width:"200"},{default:s(n=>[e("div",br,[e("div",{class:P(["w-8 h-8 rounded-lg flex items-center justify-center",L(n.row.type)])},[(c(),G(pe(Q(n.row.type)),{class:P(["w-4 h-4",O(n.row.type)])},null,8,["class"]))],2),e("div",null,[e("div",hr,m(n.row.type),1),e("div",fr,m(n.row.platform),1)])])]),_:1}),o(w,{label:"采集数量",width:"150"},{default:s(n=>[e("div",yr,[e("div",wr,[t[17]||(t[17]=e("span",{class:"text-xs text-gray-500 dark:text-dark-text-secondary"},"目标:",-1)),e("span",_r,m(n.row.targetCount),1)]),e("div",Cr,[t[18]||(t[18]=e("span",{class:"text-xs text-gray-500 dark:text-dark-text-secondary"},"成功:",-1)),e("span",$r,m(n.row.successCount),1)]),e("div",Vr,[e("div",{class:"bg-green-500 h-1.5 rounded-full transition-all duration-300",style:Te({width:`${n.row.successCount/n.row.targetCount*100}%`})},null,4)])])]),_:1}),o(w,{label:"采集状态",width:"150"},{default:s(n=>[e("div",Mr,[e("span",{class:P(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",x(n.row.status)])},[e("span",{class:P(["w-1.5 h-1.5 rounded-full mr-1.5",i(n.row.status)])},null,2),U(" "+m(n.row.status),1)],2),n.row.status.includes("失败")?(c(),g("div",Tr,[e("button",{onClick:_e=>W(n.row),class:"text-xs text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 underline"}," 查看原因 ",8,Sr)])):$("",!0)])]),_:1}),o(w,{prop:"collector",label:"创建人",width:"100"},{default:s(n=>[e("div",jr,[e("div",zr,[e("span",Dr,m(n.row.collector.charAt(0).toUpperCase()),1)]),e("span",Ur,m(n.row.collector),1)])]),_:1}),o(w,{prop:"createTime",label:"创建时间",width:"180"},{default:s(n=>[e("div",Br,m(n.row.createTime),1)]),_:1}),o(w,{label:"操作",width:"180"},{default:s(n=>[e("div",Rr,[e("button",{onClick:_e=>R(n.row),class:"inline-flex items-center px-3 py-1.5 text-sm font-medium text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 bg-primary-50 dark:bg-primary-900/20 hover:bg-primary-100 dark:hover:bg-primary-900/30 rounded-lg transition-all duration-200"}," 查看详情 ",8,Hr),o(he,{onCommand:a,trigger:"click"},{dropdown:s(()=>[o(be,null,{default:s(()=>[o(H,{command:{action:"smartCrop",row:n.row}},{default:s(()=>t[19]||(t[19]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-green-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m0 0V1a1 1 0 011-1h2a1 1 0 011 1v18a1 1 0 01-1 1H4a1 1 0 01-1-1V1a1 1 0 011-1h2a1 1 0 011 1v3m0 0h8m-8 0H4a1 1 0 00-1 1v3a1 1 0 001 1h3m0 0h10m0 0h3a1 1 0 001-1V5a1 1 0 00-1-1h-3m-3 0v8a1 1 0 01-1 1H8a1 1 0 01-1-1V4"})]),e("span",null,"智能裁图")],-1)])),_:2,__:[19]},1032,["command"]),o(H,{command:{action:"titleGenerate",row:n.row}},{default:s(()=>t[20]||(t[20]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-blue-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})]),e("span",null,"标题生成")],-1)])),_:2,__:[20]},1032,["command"]),o(H,{command:{action:"batchListing",row:n.row}},{default:s(()=>t[21]||(t[21]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-purple-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"})]),e("span",null,"批量刊登")],-1)])),_:2,__:[21]},1032,["command"]),o(H,{command:{action:"oneClickCutout",row:n.row}},{default:s(()=>t[22]||(t[22]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-pink-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})]),e("span",null,"一键抠图")],-1)])),_:2,__:[22]},1032,["command"]),o(H,{command:{action:"superSplit",row:n.row}},{default:s(()=>t[23]||(t[23]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-orange-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 10V3L4 14h7v7l9-11h-7z"})]),e("span",null,"超级裂变")],-1)])),_:2,__:[23]},1032,["command"])]),_:2},1024)]),default:s(()=>[t[24]||(t[24]=e("button",{class:"inline-flex items-center px-2 py-1.5 text-sm font-medium text-gray-600 dark:text-dark-text-secondary hover:text-gray-700 dark:hover:text-dark-text bg-gray-50 dark:bg-dark-card hover:bg-gray-100 dark:hover:bg-dark-border rounded-lg transition-all duration-200"},[U(" 更多 "),e("svg",{class:"w-4 h-4 ml-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 9l-7 7-7-7"})])],-1))]),_:2,__:[24]},1024)])]),_:1})]),_:1},8,["data"])),[[we,k(r)]])]),e("div",Or,[e("div",Pr," 共 "+m(k(C).total)+" 条记录 ",1),o(ye,{"current-page":k(C).currentPage,"onUpdate:currentPage":t[1]||(t[1]=n=>k(C).currentPage=n),"page-size":k(C).pageSize,"onUpdate:pageSize":t[2]||(t[2]=n=>k(C).pageSize=n),"page-sizes":[10,20,50,100],total:k(C).total,layout:"sizes, prev, pager, next, jumper",onSizeChange:re,onCurrentChange:ae,class:"modern-pagination"},null,8,["current-page","page-size","total"])])]),o(kt,{modelValue:_.value,"onUpdate:modelValue":t[3]||(t[3]=n=>_.value=n),onSuccess:T},null,8,["modelValue"]),o(Kt,{modelValue:S.value,"onUpdate:modelValue":t[4]||(t[4]=n=>S.value=n),"collection-data":V.value},null,8,["modelValue","collection-data"])])}}}),Fr=ie(Ar,[["__scopeId","data-v-698f84e1"]]);export{Fr as default};
