import{d as Q,e as G,r as g,c as k,F as E,g as r,h as b,j as a,E as w,a as e,C as V,t as n,M as W,k as K,o as x,q,b as X,Q as Y,A as F,_ as Z,f as le,p as ne,G as de,n as ie}from"./index-84KJ6UvQ.js";import{_ as ue}from"./GallerySelectDialog.vue_vue_type_script_setup_true_lang-B2wBZjlb.js";import{r as O}from"./ArrowDownTrayIcon-C6iGhkUM.js";const ce={class:"space-y-6"},pe={class:"flex justify-start space-x-3"},ge={class:"bg-gray-50 dark:bg-dark-card rounded-lg p-4"},xe={class:"flex items-center space-x-4"},me={class:"flex items-center space-x-2"},ke={class:"text-xs text-gray-500 dark:text-dark-text-secondary"},ve={key:0,class:"space-y-4"},be={class:"flex items-center justify-between"},he={class:"text-sm font-medium text-gray-900 dark:text-dark-text"},fe={class:"text-xs text-gray-500 dark:text-dark-text-secondary"},we={class:"border border-gray-200 dark:border-dark-border rounded-lg p-4 max-h-80 overflow-y-auto"},_e={class:"grid grid-cols-6 gap-4"},ye={class:"relative"},Ce=["src","alt"],$e=["onClick"],je={class:"absolute bottom-1 right-1 bg-purple-500 text-white text-xs px-1.5 py-0.5 rounded"},Ve={class:"text-xs text-gray-600 dark:text-dark-text-secondary mt-1 truncate"},Me={key:0,class:"mt-4 flex justify-center"},ze={key:1,class:"text-center py-12 border-2 border-dashed border-gray-200 dark:border-dark-border rounded-lg"},Se={class:"flex justify-between items-center"},Te={class:"text-sm text-gray-500 dark:text-dark-text-secondary"},Be={class:"flex space-x-3"},Le=Q({__name:"CreateSplitDialog",props:{modelValue:{type:Boolean}},emits:["update:modelValue","success"],setup(H,{emit:S}){const T=H,M=S,C=G({get:()=>T.modelValue,set:m=>M("update:modelValue",m)}),_=g(!1),B=g([]),c=g([]),y=g(1),$=g(18),u=g(3),L=G(()=>{const m=(y.value-1)*$.value,s=m+$.value;return c.value.slice(m,s)}),R=m=>{const s=new FileReader;s.onload=h=>{var z;const d=Date.now()+Math.random();c.value.push({id:d,name:m.name,url:(z=h.target)==null?void 0:z.result,file:m.raw})},s.readAsDataURL(m.raw)},p=m=>{},o=m=>{c.value.splice(m,1),L.value.length===0&&y.value>1&&y.value--},j=m=>{const s=m.map(h=>({id:h.id,name:h.name,url:h.url}));c.value.push(...s),_.value=!1,w.success(`已添加 ${m.length} 张图片`)},P=()=>{if(c.value.length===0){w.warning("请先选择图片");return}w.success(`正在创建裂变任务，共 ${c.value.length} 张图片，每张生成 ${u.value} 个裂变版本`),I(),M("success"),M("update:modelValue",!1)},I=()=>{c.value=[],B.value=[],y.value=1,u.value=3};return(m,s)=>{const h=b("el-button"),d=b("el-upload"),z=b("el-input-number"),D=b("el-pagination"),N=b("el-dialog");return x(),k(E,null,[r(N,{modelValue:C.value,"onUpdate:modelValue":s[4]||(s[4]=v=>C.value=v),title:"新建裂变任务",width:"900px","align-center":"",onClose:I},{footer:a(()=>[e("div",Se,[e("div",Te,n(c.value.length>0?`将处理 ${c.value.length} 张图片，生成 ${c.value.length*u.value} 张裂变图片`:"请先选择图片"),1),e("div",Be,[r(h,{onClick:s[3]||(s[3]=v=>C.value=!1)},{default:a(()=>s[12]||(s[12]=[V("取消")])),_:1,__:[12]}),r(h,{type:"primary",onClick:P,disabled:c.value.length===0},{default:a(()=>s[13]||(s[13]=[V(" 提交任务 ")])),_:1,__:[13]},8,["disabled"])])])]),default:a(()=>[e("div",ce,[e("div",pe,[r(d,{ref:"uploadRef","file-list":B.value,"on-change":R,"on-remove":p,"auto-upload":!1,multiple:"",accept:"image/*","show-file-list":!1},{default:a(()=>[r(h,{type:"primary",size:"large"},{default:a(()=>s[6]||(s[6]=[e("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"})],-1),V(" 上传图片 ")])),_:1,__:[6]})]),_:1},8,["file-list"]),r(h,{size:"large",onClick:s[0]||(s[0]=v=>_.value=!0)},{default:a(()=>s[7]||(s[7]=[e("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1),V(" 从图库选择 ")])),_:1,__:[7]})]),e("div",ge,[s[9]||(s[9]=e("h4",{class:"text-sm font-medium text-gray-900 dark:text-dark-text mb-3"},"裂变设置",-1)),e("div",xe,[e("div",me,[s[8]||(s[8]=e("label",{class:"text-sm text-gray-600 dark:text-dark-text-secondary"},"裂变数量:",-1)),r(z,{modelValue:u.value,"onUpdate:modelValue":s[1]||(s[1]=v=>u.value=v),min:1,max:5,size:"small",style:{width:"120px"}},null,8,["modelValue"])]),e("div",ke," 每张原图将生成 "+n(u.value)+" 个不同的裂变版本 ",1)])]),c.value.length>0?(x(),k("div",ve,[e("div",be,[e("h4",he," 已选择图片 ("+n(c.value.length)+") ",1),e("div",fe," 预计生成 "+n(c.value.length*u.value)+" 张裂变图片 ",1)]),e("div",we,[e("div",_e,[(x(!0),k(E,null,K(L.value,(v,A)=>(x(),k("div",{key:v.id||A,class:"relative group"},[e("div",ye,[e("img",{src:v.url,alt:v.name,class:"w-full h-20 object-cover rounded-lg border border-gray-200 dark:border-dark-border"},null,8,Ce),e("button",{onClick:J=>o(A+(y.value-1)*$.value),class:"absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200 hover:bg-red-600"},s[10]||(s[10]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]),8,$e),e("div",je," ×"+n(u.value),1)]),e("p",Ve,n(v.name),1)]))),128))]),c.value.length>$.value?(x(),k("div",Me,[r(D,{"current-page":y.value,"onUpdate:currentPage":s[2]||(s[2]=v=>y.value=v),"page-size":$.value,total:c.value.length,layout:"prev, pager, next",small:""},null,8,["current-page","page-size","total"])])):W("",!0)])])):(x(),k("div",ze,s[11]||(s[11]=[e("svg",{class:"w-12 h-12 text-gray-400 dark:text-dark-text-secondary mx-auto mb-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1),e("p",{class:"text-gray-500 dark:text-dark-text-secondary"},"请上传图片或从图库选择",-1)])))])]),_:1},8,["modelValue"]),r(ue,{modelValue:_.value,"onUpdate:modelValue":s[5]||(s[5]=v=>_.value=v),"theme-color":"purple",onSelect:j},null,8,["modelValue"])],64)}}}),De={class:"flex items-center justify-between p-6 border-b border-gray-100 dark:border-dark-border"},Pe={class:"flex items-center space-x-3"},Ie={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},Re={key:0,class:"p-6 grid grid-cols-2 md:grid-cols-4 gap-4"},Ne={class:"bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 p-4 rounded-xl border border-purple-200 dark:border-purple-800"},Ue={class:"flex items-center space-x-2"},He={class:"text-sm font-bold text-purple-900 dark:text-purple-100"},Ae={class:"bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 p-4 rounded-xl border border-blue-200 dark:border-blue-800"},Fe={class:"flex items-center space-x-2"},Ge={class:"text-sm font-bold text-blue-900 dark:text-blue-100"},Ee={class:"bg-gradient-to-br from-indigo-50 to-indigo-100 dark:from-indigo-900/20 dark:to-indigo-800/20 p-4 rounded-xl border border-indigo-200 dark:border-indigo-800"},qe={class:"flex items-center space-x-2"},Oe={class:"text-sm font-bold text-indigo-900 dark:text-indigo-100"},Qe={class:"bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 p-4 rounded-xl border border-green-200 dark:border-green-800"},We={class:"flex items-center space-x-2"},Je={class:"text-sm font-bold text-green-900 dark:text-green-100"},Ke={class:"px-6 pb-6"},Xe={class:"bg-white dark:bg-dark-surface rounded-xl border border-gray-200 dark:border-dark-border overflow-hidden"},Ye={key:0,class:"flex justify-center"},Ze={key:1,class:"flex justify-center"},et={key:0,class:"flex justify-center space-x-2"},tt={class:"absolute -top-1 -right-1 w-4 h-4 bg-purple-500 text-white text-xs rounded-full flex items-center justify-center"},rt={key:1,class:"flex justify-center"},st={class:"text-sm text-gray-500 dark:text-dark-text-secondary"},ot={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},at={key:0,class:"flex justify-center"},lt=["onClick"],nt={key:1,class:"flex justify-center"},dt={class:"flex items-center justify-between p-6 border-t border-gray-100 dark:border-dark-border bg-gray-50 dark:bg-dark-card/50"},it={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},ut={class:"flex items-center space-x-3"},ct=Q({__name:"ViewDetailsDialog",props:{modelValue:{type:Boolean},task:{}},emits:["update:modelValue"],setup(H,{emit:S}){const T=H,M=S,C=G({get:()=>T.modelValue,set:p=>M("update:modelValue",p)}),_=g(!1),B=g(0),c=g([{index:1,originalImage:"https://picsum.photos/400/400?random=1",splitResults:[{url:"https://picsum.photos/400/400?random=11",name:"split_1_v1.jpg"},{url:"https://picsum.photos/400/400?random=12",name:"split_1_v2.jpg"},{url:"https://picsum.photos/400/400?random=13",name:"split_1_v3.jpg"}],fileName:"product_001.jpg",status:"success"},{index:2,originalImage:"https://picsum.photos/400/400?random=2",splitResults:[{url:"https://picsum.photos/400/400?random=21",name:"split_2_v1.jpg"},{url:"https://picsum.photos/400/400?random=22",name:"split_2_v2.jpg"},{url:"https://picsum.photos/400/400?random=23",name:"split_2_v3.jpg"},{url:"https://picsum.photos/400/400?random=24",name:"split_2_v4.jpg"},{url:"https://picsum.photos/400/400?random=25",name:"split_2_v5.jpg"}],fileName:"product_002.jpg",status:"success"},{index:3,originalImage:"https://picsum.photos/400/400?random=3",splitResults:[],fileName:"product_003.jpg",status:"failed"}]);B.value=c.value.length;const y=p=>({completed:"已完成",processing:"处理中",failed:"失败",pending:"等待中"})[p]||"未知",$=p=>({success:"成功",failed:"失败"})[p]||"未知",u=()=>{C.value=!1},L=p=>{w.success(`正在下载 ${p.fileName} 的所有裂变结果`)},R=()=>{w.success("正在导出详情...")};return(p,o)=>{const j=b("el-table-column"),P=b("el-image"),I=b("el-tag"),m=b("el-table"),s=b("el-dialog"),h=Y("loading");return x(),q(s,{modelValue:C.value,"onUpdate:modelValue":o[0]||(o[0]=d=>C.value=d),width:"1200px","before-close":u,"show-close":!1,class:"modern-dialog"},{header:a(()=>{var d;return[e("div",De,[e("div",Pe,[o[2]||(o[2]=e("div",{class:"w-10 h-10 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 10V3L4 14h7v7l9-11h-7z"})])],-1)),e("div",null,[o[1]||(o[1]=e("h3",{class:"text-xl font-bold text-gray-900 dark:text-dark-text"},"裂变详情",-1)),e("p",Ie,"任务ID: "+n(((d=p.task)==null?void 0:d.id)||""),1)])]),e("button",{onClick:u,class:"p-2 text-gray-400 hover:text-gray-600 dark:text-dark-text-secondary dark:hover:text-dark-text rounded-lg hover:bg-gray-100 dark:hover:bg-dark-card transition-all duration-200"},o[3]||(o[3]=[e("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))])]}),footer:a(()=>[e("div",dt,[e("div",it," 共 "+n(B.value)+" 条裂变结果 ",1),e("div",ut,[e("button",{onClick:u,class:"px-6 py-2.5 text-gray-700 dark:text-dark-text font-medium rounded-lg border border-gray-300 dark:border-dark-border hover:bg-gray-50 dark:hover:bg-dark-border transition-all duration-200"}," 关闭 "),e("button",{onClick:R,class:"inline-flex items-center px-6 py-2.5 bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white font-medium rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"},[r(F(O),{class:"w-5 h-5 mr-2"}),o[15]||(o[15]=V(" 导出详情 "))])])])]),default:a(()=>[p.task?(x(),k("div",Re,[e("div",Ne,[e("div",Ue,[o[5]||(o[5]=e("div",{class:"w-8 h-8 bg-purple-500 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1)),e("div",null,[o[4]||(o[4]=e("p",{class:"text-xs text-purple-600 dark:text-purple-400 font-medium"},"任务状态",-1)),e("p",He,n(y(p.task.status)),1)])])]),e("div",Ae,[e("div",Fe,[o[7]||(o[7]=e("div",{class:"w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})])],-1)),e("div",null,[o[6]||(o[6]=e("p",{class:"text-xs text-blue-600 dark:text-blue-400 font-medium"},"原图数量",-1)),e("p",Ge,n(p.task.targetCount),1)])])]),e("div",Ee,[e("div",qe,[o[9]||(o[9]=e("div",{class:"w-8 h-8 bg-indigo-500 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 10V3L4 14h7v7l9-11h-7z"})])],-1)),e("div",null,[o[8]||(o[8]=e("p",{class:"text-xs text-indigo-600 dark:text-indigo-400 font-medium"},"裂变数量",-1)),e("p",Oe,n(p.task.splitCount),1)])])]),e("div",Qe,[e("div",We,[o[11]||(o[11]=e("div",{class:"w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 13l4 4L19 7"})])],-1)),e("div",null,[o[10]||(o[10]=e("p",{class:"text-xs text-green-600 dark:text-green-400 font-medium"},"成功生成",-1)),e("p",Je,n(p.task.successCount),1)])])])])):W("",!0),e("div",Ke,[o[14]||(o[14]=e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-dark-text mb-4"},"裂变结果",-1)),e("div",Xe,[X((x(),q(m,{data:c.value,style:{width:"100%"},"max-height":"400",class:"modern-table"},{default:a(()=>[r(j,{prop:"index",label:"序号",width:"80",align:"center"}),r(j,{label:"原图",width:"100",align:"center"},{default:a(d=>[d.row.status==="success"?(x(),k("div",Ye,[r(P,{src:d.row.originalImage,"preview-src-list":[d.row.originalImage],fit:"cover",class:"w-16 h-16 rounded-lg border border-gray-200 dark:border-dark-border","preview-teleported":!0},null,8,["src","preview-src-list"])])):(x(),k("div",Ze,o[12]||(o[12]=[e("div",{class:"w-16 h-16 bg-gray-100 dark:bg-dark-card rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})])],-1)])))]),_:1}),r(j,{label:"裂变结果",width:"400",align:"center"},{default:a(d=>[d.row.status==="success"?(x(),k("div",et,[(x(!0),k(E,null,K(d.row.splitResults,(z,D)=>(x(),k("div",{key:D,class:"relative"},[r(P,{src:z.url,"preview-src-list":d.row.splitResults.map(N=>N.url),"initial-index":D,fit:"cover",class:"w-16 h-16 rounded-lg border border-gray-200 dark:border-dark-border","preview-teleported":!0},null,8,["src","preview-src-list","initial-index"]),e("div",tt,n(D+1),1)]))),128))])):(x(),k("div",rt,[e("span",st,n($(d.row.status)),1)]))]),_:1}),r(j,{prop:"fileName",label:"文件名",width:"200",align:"center"},{default:a(d=>[e("span",ot,n(d.row.fileName),1)]),_:1}),r(j,{prop:"status",label:"状态",width:"100",align:"center"},{default:a(d=>[r(I,{type:d.row.status==="success"?"success":"danger",size:"small"},{default:a(()=>[V(n($(d.row.status)),1)]),_:2},1032,["type"])]),_:1}),r(j,{label:"操作",width:"120",align:"center"},{default:a(d=>[d.row.status==="success"?(x(),k("div",at,[e("button",{onClick:z=>L(d.row),class:"inline-flex items-center px-3 py-1.5 text-sm font-medium text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/30 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-900/50 transition-all duration-200"}," 下载 ",8,lt)])):(x(),k("div",nt,o[13]||(o[13]=[e("span",{class:"text-sm text-gray-400"},"-",-1)])))]),_:1})]),_:1},8,["data"])),[[h,_.value]])])])]),_:1},8,["modelValue"])}}}),pt=Z(ct,[["__scopeId","data-v-3a9451d2"]]),gt={class:"space-y-6"},xt={class:"grid grid-cols-1 md:grid-cols-4 gap-6"},mt={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},kt={class:"flex items-center justify-between"},vt={class:"text-2xl font-bold text-gray-900 dark:text-dark-text"},bt={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},ht={class:"flex items-center justify-between"},ft={class:"text-2xl font-bold text-green-600 dark:text-green-400"},wt={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},_t={class:"flex items-center justify-between"},yt={class:"text-2xl font-bold text-blue-600 dark:text-blue-400"},Ct={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},$t={class:"flex items-center justify-between"},jt={class:"text-2xl font-bold text-orange-600 dark:text-orange-400"},Vt={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border"},Mt={class:"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0"},zt={class:"flex items-center space-x-3"},St={key:0,class:"flex items-center space-x-2 ml-4 pl-4 border-l border-gray-200 dark:border-dark-border"},Tt={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},Bt={class:"bg-white dark:bg-dark-surface rounded-xl shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border overflow-hidden"},Lt={class:"overflow-x-auto"},Dt={class:"font-mono text-sm text-gray-600 dark:text-dark-text-secondary"},Pt={class:"flex items-center space-x-2"},It={class:"font-medium text-gray-900 dark:text-dark-text"},Rt={class:"font-medium text-purple-600 dark:text-purple-400"},Nt={class:"text-sm font-medium text-green-600 dark:text-green-400"},Ut={class:"flex items-center space-x-2"},Ht={class:"w-6 h-6 bg-gradient-to-br from-purple-400 to-purple-500 rounded-full flex items-center justify-center"},At={class:"text-white text-xs font-medium"},Ft={class:"text-sm text-gray-900 dark:text-dark-text"},Gt={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},Et={class:"flex items-center space-x-2"},qt=["onClick"],Ot={class:"flex justify-between items-center px-6 py-4 border-t border-gray-100 dark:border-dark-border bg-gray-50 dark:bg-dark-card/50"},Qt={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},Wt=Q({__name:"index",setup(H){const S=g(!1),T=g(!1),M=g(!1),C=g(null),_=g([]),B=g(1248),c=g(94.2),y=g(156),$=g(8),u=g({currentPage:1,pageSize:20,total:0}),L=g([{id:"SP001",targetCount:50,splitCount:3,status:"completed",successCount:150,operator:"张三",createTime:"2024-01-15 14:30:25"},{id:"SP002",targetCount:30,splitCount:5,status:"processing",successCount:120,operator:"李四",createTime:"2024-01-15 13:45:12"},{id:"SP003",targetCount:80,splitCount:2,status:"completed",successCount:160,operator:"王五",createTime:"2024-01-15 12:20:08"},{id:"SP004",targetCount:25,splitCount:4,status:"failed",successCount:0,operator:"赵六",createTime:"2024-01-15 11:15:33"},{id:"SP005",targetCount:60,splitCount:3,status:"pending",successCount:0,operator:"钱七",createTime:"2024-01-15 10:30:45"},{id:"SP006",targetCount:40,splitCount:5,status:"completed",successCount:200,operator:"孙八",createTime:"2024-01-15 09:45:22"},{id:"SP007",targetCount:35,splitCount:2,status:"processing",successCount:45,operator:"周九",createTime:"2024-01-15 08:20:15"},{id:"SP008",targetCount:70,splitCount:4,status:"completed",successCount:280,operator:"吴十",createTime:"2024-01-14 16:30:40"},{id:"SP009",targetCount:20,splitCount:3,status:"completed",successCount:60,operator:"郑一",createTime:"2024-01-14 15:15:28"},{id:"SP010",targetCount:55,splitCount:5,status:"processing",successCount:180,operator:"王二",createTime:"2024-01-14 14:45:55"}]),R=G(()=>{const i=(u.value.currentPage-1)*u.value.pageSize,t=i+u.value.pageSize;return L.value.slice(i,t)});le(()=>{p()});const p=()=>{S.value=!0,setTimeout(()=>{u.value.total=L.value.length,S.value=!1},500)},o=i=>{const t={completed:"bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300",processing:"bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300",failed:"bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300",pending:"bg-gray-100 dark:bg-gray-900/30 text-gray-800 dark:text-gray-300"};return t[i]||t.pending},j=i=>({completed:"已完成",processing:"处理中",failed:"失败",pending:"等待中"})[i]||"未知",P=i=>{_.value=i},I=i=>{C.value=i,M.value=!0},m=i=>{const{action:t,row:f}=i;switch(t){case"titleGenerate":z(f);break;case"batchListing":D(f);break;case"smartCrop":N(f);break;case"oneClickCutout":v(f);break;case"copyrightDetection":A(f);break}},s=()=>{w.success("导出表格功能开发中...")},h=()=>{w.success(`正在批量导出 ${_.value.length} 个任务...`)},d=()=>{w.success("裂变任务创建成功！"),p()},z=i=>{w.success(`正在为裂变任务 ${i.id} 创建标题生成任务...`)},D=i=>{w.success(`正在为裂变任务 ${i.id} 创建批量刊登任务...`)},N=i=>{w.success(`正在为裂变任务 ${i.id} 创建智能裁图任务...`)},v=i=>{w.success(`正在为裂变任务 ${i.id} 创建一键抠图任务...`)},A=i=>{w.success(`正在为裂变任务 ${i.id} 创建侵权检测任务...`)},J=i=>{u.value.pageSize=i,u.value.currentPage=1,p()},ee=i=>{u.value.currentPage=i,p()};return(i,t)=>{const f=b("el-table-column"),U=b("el-dropdown-item"),te=b("el-dropdown-menu"),re=b("el-dropdown"),se=b("el-table"),oe=b("el-pagination"),ae=Y("loading");return x(),k(E,null,[e("div",gt,[t[26]||(t[26]=ne('<div class="bg-gradient-to-r from-purple-50 to-indigo-50 dark:from-purple-900/20 dark:to-indigo-900/20 rounded-2xl p-6 border border-purple-100 dark:border-purple-800" data-v-a408a1e4><div class="flex items-center space-x-3" data-v-a408a1e4><div class="w-10 h-10 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center" data-v-a408a1e4><svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-a408a1e4><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" data-v-a408a1e4></path></svg></div><div data-v-a408a1e4><h1 class="text-2xl font-bold text-gray-900 dark:text-dark-text" data-v-a408a1e4>超级裂变</h1><p class="mt-1 text-sm text-gray-600 dark:text-dark-text-secondary" data-v-a408a1e4>AI智能图片裂变和多样化生成工具</p></div></div></div>',1)),e("div",xt,[e("div",mt,[e("div",kt,[e("div",null,[t[5]||(t[5]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"总裂变数",-1)),e("p",vt,n(B.value),1)]),t[6]||(t[6]=e("div",{class:"w-12 h-12 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-purple-600 dark:text-purple-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 10V3L4 14h7v7l9-11h-7z"})])],-1))])]),e("div",bt,[e("div",ht,[e("div",null,[t[7]||(t[7]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"成功率",-1)),e("p",ft,n(c.value)+"%",1)]),t[8]||(t[8]=e("div",{class:"w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-green-600 dark:text-green-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1))])]),e("div",wt,[e("div",_t,[e("div",null,[t[9]||(t[9]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"今日裂变",-1)),e("p",yt,n(y.value),1)]),t[10]||(t[10]=e("div",{class:"w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-blue-600 dark:text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})])],-1))])]),e("div",Ct,[e("div",$t,[e("div",null,[t[11]||(t[11]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"处理中",-1)),e("p",jt,n($.value),1)]),t[12]||(t[12]=e("div",{class:"w-12 h-12 bg-orange-100 dark:bg-orange-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-orange-600 dark:text-orange-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1))])])]),e("div",Vt,[e("div",Mt,[e("div",zt,[e("button",{onClick:t[0]||(t[0]=l=>T.value=!0),class:"inline-flex items-center px-4 py-2.5 bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white font-medium rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"},[r(F(de),{class:"w-5 h-5 mr-2"}),t[13]||(t[13]=V(" 新建裂变 "))]),e("button",{onClick:s,class:"inline-flex items-center px-4 py-2.5 bg-white dark:bg-dark-card text-gray-700 dark:text-dark-text font-medium rounded-lg border border-gray-300 dark:border-dark-border hover:bg-gray-50 dark:hover:bg-dark-border shadow-sm hover:shadow-md transition-all duration-200"},[r(F(O),{class:"w-5 h-5 mr-2"}),t[14]||(t[14]=V(" 导出表格 "))]),_.value.length>0?(x(),k("div",St,[e("span",Tt," 已选择 "+n(_.value.length)+" 项 ",1),e("button",{onClick:h,class:"inline-flex items-center px-3 py-1.5 bg-purple-500 hover:bg-purple-600 text-white text-sm font-medium rounded-lg transition-all duration-200"},[r(F(O),{class:"w-4 h-4 mr-1"}),t[15]||(t[15]=V(" 批量导出 "))])])):W("",!0)])])]),e("div",Bt,[t[25]||(t[25]=e("div",{class:"px-6 py-4 border-b border-gray-100 dark:border-dark-border"},[e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-dark-text"},"裂变任务列表"),e("p",{class:"text-sm text-gray-600 dark:text-dark-text-secondary mt-1"},"管理和监控您的所有裂变任务")],-1)),e("div",Lt,[X((x(),q(se,{data:R.value,style:{width:"100%"},onSelectionChange:P,class:"modern-table","header-cell-style":{backgroundColor:"var(--el-bg-color-page)",color:"var(--el-text-color-primary)",fontWeight:"600",borderBottom:"1px solid var(--el-border-color-light)"},"row-style":{backgroundColor:"transparent"}},{default:a(()=>[r(f,{type:"selection",width:"55"}),r(f,{prop:"id",label:"任务ID",width:"120"},{default:a(l=>[e("span",Dt,n(l.row.id),1)]),_:1}),r(f,{label:"裂变数量",width:"150"},{default:a(l=>[e("div",Pt,[t[16]||(t[16]=e("span",{class:"text-sm text-gray-600 dark:text-dark-text-secondary"},"目标:",-1)),e("span",It,n(l.row.targetCount),1),t[17]||(t[17]=e("span",{class:"text-gray-400"},"|",-1)),t[18]||(t[18]=e("span",{class:"text-sm text-gray-600 dark:text-dark-text-secondary"},"裂变:",-1)),e("span",Rt,n(l.row.splitCount),1)])]),_:1}),r(f,{prop:"status",label:"裂变状态",width:"120"},{default:a(l=>[e("span",{class:ie([o(l.row.status),"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"])},n(j(l.row.status)),3)]),_:1}),r(f,{prop:"successCount",label:"成功数量",width:"100"},{default:a(l=>[e("span",Nt,n(l.row.successCount),1)]),_:1}),r(f,{prop:"operator",label:"操作人",width:"100"},{default:a(l=>[e("div",Ut,[e("div",Ht,[e("span",At,n(l.row.operator.charAt(0)),1)]),e("span",Ft,n(l.row.operator),1)])]),_:1}),r(f,{prop:"createTime",label:"创建时间",width:"180"},{default:a(l=>[e("div",Gt,n(l.row.createTime),1)]),_:1}),r(f,{label:"操作",width:"180"},{default:a(l=>[e("div",Et,[e("button",{onClick:Jt=>I(l.row),class:"inline-flex items-center px-3 py-1.5 text-sm font-medium text-purple-600 dark:text-purple-400 hover:text-purple-700 dark:hover:text-purple-300 bg-purple-50 dark:bg-purple-900/20 hover:bg-purple-100 dark:hover:bg-purple-900/30 rounded-lg transition-all duration-200"}," 查看详情 ",8,qt),r(re,{onCommand:m,trigger:"click"},{dropdown:a(()=>[r(te,null,{default:a(()=>[r(U,{command:{action:"titleGenerate",row:l.row}},{default:a(()=>t[19]||(t[19]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-blue-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})]),e("span",null,"标题生成")],-1)])),_:2,__:[19]},1032,["command"]),r(U,{command:{action:"batchListing",row:l.row}},{default:a(()=>t[20]||(t[20]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-green-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"})]),e("span",null,"批量刊登")],-1)])),_:2,__:[20]},1032,["command"]),r(U,{command:{action:"smartCrop",row:l.row}},{default:a(()=>t[21]||(t[21]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-yellow-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})]),e("span",null,"智能裁图")],-1)])),_:2,__:[21]},1032,["command"]),r(U,{command:{action:"oneClickCutout",row:l.row}},{default:a(()=>t[22]||(t[22]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-pink-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m-9 0h10m-10 0a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V6a2 2 0 00-2-2"})]),e("span",null,"一键抠图")],-1)])),_:2,__:[22]},1032,["command"]),r(U,{command:{action:"copyrightDetection",row:l.row}},{default:a(()=>t[23]||(t[23]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-blue-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"})]),e("span",null,"侵权检测")],-1)])),_:2,__:[23]},1032,["command"])]),_:2},1024)]),default:a(()=>[t[24]||(t[24]=e("button",{class:"inline-flex items-center px-2 py-1.5 text-sm font-medium text-gray-600 dark:text-dark-text-secondary hover:text-gray-700 dark:hover:text-dark-text bg-gray-50 dark:bg-dark-card hover:bg-gray-100 dark:hover:bg-dark-border rounded-lg transition-all duration-200"},[V(" 更多 "),e("svg",{class:"w-4 h-4 ml-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 9l-7 7-7-7"})])],-1))]),_:2,__:[24]},1024)])]),_:1})]),_:1},8,["data"])),[[ae,S.value]])]),e("div",Ot,[e("div",Qt," 共 "+n(u.value.total)+" 条记录 ",1),r(oe,{"current-page":u.value.currentPage,"onUpdate:currentPage":t[1]||(t[1]=l=>u.value.currentPage=l),"page-size":u.value.pageSize,"onUpdate:pageSize":t[2]||(t[2]=l=>u.value.pageSize=l),"page-sizes":[10,20,50,100],total:u.value.total,layout:"sizes, prev, pager, next, jumper",onSizeChange:J,onCurrentChange:ee,class:"modern-pagination"},null,8,["current-page","page-size","total"])])])]),r(Le,{modelValue:T.value,"onUpdate:modelValue":t[3]||(t[3]=l=>T.value=l),onSuccess:d},null,8,["modelValue"]),r(pt,{modelValue:M.value,"onUpdate:modelValue":t[4]||(t[4]=l=>M.value=l),task:C.value},null,8,["modelValue","task"])],64)}}}),Zt=Z(Wt,[["__scopeId","data-v-a408a1e4"]]);export{Zt as default};
