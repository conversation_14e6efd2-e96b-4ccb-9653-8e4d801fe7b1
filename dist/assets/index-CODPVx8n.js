import{d as oe,e as Z,r as f,K as ae,m as me,c as n,F as K,g as u,a as e,h as y,j as g,M as q,C as $,w as W,l as pe,E as ee,o as l,_ as se,f as ne,n as le,k as X,t as x,q as te,W as ge,X as ve,L as ke,p as be,b as de,v as xe,Q as fe}from"./index-BTucjZQh.js";const he={class:"space-y-6"},ye={class:"grid grid-cols-1 md:grid-cols-3 gap-6"},we={key:0,class:"relative"},_e=["src"],$e={key:1,class:"h-40 flex flex-col items-center justify-center"},Ve={key:0,class:"relative"},Ce=["src"],Ie={key:1,class:"h-40 flex flex-col items-center justify-center"},je={key:0,class:"relative"},Ue=["src"],ze={key:1,class:"h-40 flex flex-col items-center justify-center"},Me={key:0,class:"bg-gray-50 dark:bg-dark-card rounded-lg p-6"},Se={class:"relative bg-white dark:bg-dark-surface rounded-lg border border-gray-200 dark:border-dark-border overflow-hidden flex items-center justify-center",style:{height:"400px"}},Le={class:"relative",style:{width:"350px",height:"350px"}},Ke=["src"],Be=["src"],De=["src"],Re={class:"mt-4 grid grid-cols-2 md:grid-cols-4 gap-4"},Ae={class:"mt-4 flex items-center space-x-4"},Pe={class:"flex space-x-2"},Ee={class:"flex justify-end space-x-3"},He=oe({__name:"CreateEffectDialog",props:{modelValue:{type:Boolean}},emits:["update:modelValue","created"],setup(O,{emit:N}){const P=O,B=N,z=Z({get:()=>P.modelValue,set:v=>B("update:modelValue",v)}),C=f(!1),U=f(),M=f(),R=f("base"),i=ae({baseImage:"",printImage:"",maskImage:""}),s=ae({visible:!1,x:50,y:50,width:100,height:100,rotation:0}),r=ae({isDragging:!1,isResizing:!1,isRotating:!1,startX:0,startY:0,startWidth:0,startHeight:0,startRotation:0,resizeDirection:"",containerRect:null}),V=()=>document.querySelector('.relative[style*="width: 350px; height: 350px"]'),E=v=>{var t;R.value=v,(t=U.value)==null||t.click()},D=v=>{var p;const t=v.target,w=(p=t.files)==null?void 0:p[0];if(w){const I=new FileReader;I.onload=T=>{var G;const k=(G=T.target)==null?void 0:G.result;R.value==="base"?i.baseImage=k:R.value==="print"?(i.printImage=k,s.visible=!0):R.value==="mask"&&(i.maskImage=k)},I.readAsDataURL(w)}t.value=""},A=v=>{v==="base"?i.baseImage="":v==="print"?(i.printImage="",s.visible=!1):v==="mask"&&(i.maskImage="")},Q=v=>{v.preventDefault();const t=V();t&&(r.isDragging=!0,r.containerRect=t.getBoundingClientRect(),r.startX=v.clientX-r.containerRect.left-s.x,r.startY=v.clientY-r.containerRect.top-s.y,document.addEventListener("mousemove",_),document.addEventListener("mouseup",a))},_=v=>{if(!r.isDragging||!r.containerRect)return;const t=V();if(!t)return;const w=v.clientX-r.containerRect.left-r.startX,p=v.clientY-r.containerRect.top-r.startY,I=t.clientWidth-s.width,T=t.clientHeight-s.height;s.x=Math.max(0,Math.min(I,w)),s.y=Math.max(0,Math.min(T,p))},a=()=>{r.isDragging=!1,r.containerRect=null,document.removeEventListener("mousemove",_),document.removeEventListener("mouseup",a)},Y=(v,t)=>{t.preventDefault(),t.stopPropagation(),r.isResizing=!0,r.resizeDirection=v,r.startWidth=s.width,r.startHeight=s.height,r.startX=t.clientX,r.startY=t.clientY,document.addEventListener("mousemove",H),document.addEventListener("mouseup",J)},H=v=>{if(!r.isResizing)return;const t=v.clientX-r.startX,w=v.clientY-r.startY,p=V();if(!p)return;const I=p.clientWidth-s.x,T=p.clientHeight-s.y;if(r.resizeDirection.includes("e")&&(s.width=Math.max(10,Math.min(I,r.startWidth+t))),r.resizeDirection.includes("w")){const k=Math.max(10,r.startWidth-t),G=s.width-k;s.x+G>=0&&(s.width=k,s.x+=G)}if(r.resizeDirection.includes("s")&&(s.height=Math.max(10,Math.min(T,r.startHeight+w))),r.resizeDirection.includes("n")){const k=Math.max(10,r.startHeight-w),G=s.height-k;s.y+G>=0&&(s.height=k,s.y+=G)}},J=()=>{r.isResizing=!1,document.removeEventListener("mousemove",H),document.removeEventListener("mouseup",J)},b=v=>{v.preventDefault(),v.stopPropagation(),r.isRotating=!0,r.startRotation=s.rotation,r.startX=v.clientX,r.startY=v.clientY,document.addEventListener("mousemove",c),document.addEventListener("mouseup",L)},c=v=>{if(!r.isRotating)return;const t=V();if(!t)return;const w=t.getBoundingClientRect(),p=w.left+s.x+s.width/2,I=w.top+s.y+s.height/2,T=Math.atan2(r.startY-I,r.startX-p),G=(Math.atan2(v.clientY-I,v.clientX-p)-T)*(180/Math.PI);s.rotation=Math.max(-180,Math.min(180,r.startRotation+G))},L=()=>{r.isRotating=!1,document.removeEventListener("mousemove",c),document.removeEventListener("mouseup",L)},j=()=>{s.x=50,s.y=50,s.width=100,s.height=100,s.rotation=0},d=()=>{s.x=(350-s.width)/2,s.y=(350-s.height)/2},m=()=>{C.value=!0,setTimeout(()=>{C.value=!1;const v="https://picsum.photos/400/400?random="+Date.now();B("created",v),ee.success("效果图生成成功！"),o()},2e3)},o=()=>{i.baseImage="",i.printImage="",i.maskImage="",s.visible=!1,j()};return me(()=>{document.removeEventListener("mousemove",_),document.removeEventListener("mouseup",a),document.removeEventListener("mousemove",H),document.removeEventListener("mouseup",J),document.removeEventListener("mousemove",c),document.removeEventListener("mouseup",L)}),(v,t)=>{const w=y("el-input-number"),p=y("el-slider"),I=y("el-button"),T=y("el-dialog");return l(),n(K,null,[u(T,{modelValue:z.value,"onUpdate:modelValue":t[17]||(t[17]=k=>z.value=k),title:"创建效果图",width:"1200px","align-center":"",onClose:o},{footer:g(()=>[e("div",Ee,[u(I,{onClick:t[16]||(t[16]=k=>z.value=!1)},{default:g(()=>t[36]||(t[36]=[$("取消")])),_:1,__:[36]}),u(I,{type:"primary",onClick:m,loading:C.value,disabled:!i.baseImage||!i.printImage},{default:g(()=>t[37]||(t[37]=[$(" 生成效果图 ")])),_:1,__:[37]},8,["loading","disabled"])])]),default:g(()=>[e("div",he,[e("div",ye,[e("div",null,[t[20]||(t[20]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text-secondary mb-2"},[$(" 底图 "),e("span",{class:"text-red-500"},"*")],-1)),e("div",{class:"border-2 border-dashed border-gray-300 dark:border-dark-border rounded-lg p-4 text-center cursor-pointer hover:border-amber-500 dark:hover:border-amber-500 transition-colors",onClick:t[1]||(t[1]=k=>E("base"))},[i.baseImage?(l(),n("div",we,[e("img",{src:i.baseImage,alt:"底图",class:"w-full h-40 object-cover rounded-lg"},null,8,_e),e("button",{onClick:t[0]||(t[0]=W(k=>A("base"),["stop"])),class:"absolute top-2 right-2 w-8 h-8 bg-red-500 text-white rounded-full flex items-center justify-center hover:bg-red-600"},t[18]||(t[18]=[e("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))])):(l(),n("div",$e,t[19]||(t[19]=[e("svg",{class:"w-12 h-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1),e("p",{class:"mt-2 text-sm text-gray-500 dark:text-dark-text-secondary"},"点击上传底图",-1)])))])]),e("div",null,[t[23]||(t[23]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text-secondary mb-2"},[$(" 印刷图 "),e("span",{class:"text-red-500"},"*")],-1)),e("div",{class:"border-2 border-dashed border-gray-300 dark:border-dark-border rounded-lg p-4 text-center cursor-pointer hover:border-amber-500 dark:hover:border-amber-500 transition-colors",onClick:t[3]||(t[3]=k=>E("print"))},[i.printImage?(l(),n("div",Ve,[e("img",{src:i.printImage,alt:"印刷图",class:"w-full h-40 object-cover rounded-lg"},null,8,Ce),e("button",{onClick:t[2]||(t[2]=W(k=>A("print"),["stop"])),class:"absolute top-2 right-2 w-8 h-8 bg-red-500 text-white rounded-full flex items-center justify-center hover:bg-red-600"},t[21]||(t[21]=[e("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))])):(l(),n("div",Ie,t[22]||(t[22]=[e("svg",{class:"w-12 h-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1),e("p",{class:"mt-2 text-sm text-gray-500 dark:text-dark-text-secondary"},"点击上传印刷图",-1)])))])]),e("div",null,[t[26]||(t[26]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text-secondary mb-2"}," 遮罩图 ",-1)),e("div",{class:"border-2 border-dashed border-gray-300 dark:border-dark-border rounded-lg p-4 text-center cursor-pointer hover:border-amber-500 dark:hover:border-amber-500 transition-colors",onClick:t[5]||(t[5]=k=>E("mask"))},[i.maskImage?(l(),n("div",je,[e("img",{src:i.maskImage,alt:"遮罩图",class:"w-full h-40 object-cover rounded-lg"},null,8,Ue),e("button",{onClick:t[4]||(t[4]=W(k=>A("mask"),["stop"])),class:"absolute top-2 right-2 w-8 h-8 bg-red-500 text-white rounded-full flex items-center justify-center hover:bg-red-600"},t[24]||(t[24]=[e("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))])):(l(),n("div",ze,t[25]||(t[25]=[e("svg",{class:"w-12 h-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1),e("p",{class:"mt-2 text-sm text-gray-500 dark:text-dark-text-secondary"},"点击上传遮罩图",-1)])))])])]),i.baseImage&&i.printImage?(l(),n("div",Me,[t[34]||(t[34]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-dark-text mb-4"},"设置印刷区域",-1)),e("div",Se,[e("div",Le,[e("img",{src:i.baseImage,alt:"底图",class:"absolute inset-0 w-full h-full object-contain"},null,8,Ke),s.visible?(l(),n("div",{key:0,ref_key:"printAreaRef",ref:M,class:"absolute border-2 border-amber-500 bg-amber-500 bg-opacity-20 cursor-move flex items-center justify-center",style:pe({left:s.x+"px",top:s.y+"px",width:s.width+"px",height:s.height+"px",transform:`rotate(${s.rotation}deg)`}),onMousedown:Q},[e("img",{src:i.printImage,alt:"印刷图",class:"max-w-full max-h-full object-contain opacity-80",style:{"object-position":"center"}},null,8,Be),e("div",{class:"absolute -top-2 -left-2 w-4 h-4 bg-amber-500 rounded-full cursor-nw-resize",onMousedown:t[6]||(t[6]=W(k=>Y("nw",k),["stop"]))},null,32),e("div",{class:"absolute -top-2 -right-2 w-4 h-4 bg-amber-500 rounded-full cursor-ne-resize",onMousedown:t[7]||(t[7]=W(k=>Y("ne",k),["stop"]))},null,32),e("div",{class:"absolute -bottom-2 -left-2 w-4 h-4 bg-amber-500 rounded-full cursor-sw-resize",onMousedown:t[8]||(t[8]=W(k=>Y("sw",k),["stop"]))},null,32),e("div",{class:"absolute -bottom-2 -right-2 w-4 h-4 bg-amber-500 rounded-full cursor-se-resize",onMousedown:t[9]||(t[9]=W(k=>Y("se",k),["stop"]))},null,32),e("div",{class:"absolute -top-8 left-1/2 transform -translate-x-1/2 w-4 h-4 bg-blue-500 rounded-full cursor-pointer",onMousedown:t[10]||(t[10]=W(k=>b(k),["stop"]))},null,32)],36)):q("",!0),i.maskImage?(l(),n("img",{key:1,src:i.maskImage,alt:"遮罩图",class:"absolute inset-0 w-full h-full object-contain pointer-events-none"},null,8,De)):q("",!0)])]),e("div",Re,[e("div",null,[t[27]||(t[27]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text-secondary mb-1"},"X位置",-1)),u(w,{modelValue:s.x,"onUpdate:modelValue":t[11]||(t[11]=k=>s.x=k),min:0,max:350,size:"small"},null,8,["modelValue"])]),e("div",null,[t[28]||(t[28]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text-secondary mb-1"},"Y位置",-1)),u(w,{modelValue:s.y,"onUpdate:modelValue":t[12]||(t[12]=k=>s.y=k),min:0,max:350,size:"small"},null,8,["modelValue"])]),e("div",null,[t[29]||(t[29]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text-secondary mb-1"},"宽度",-1)),u(w,{modelValue:s.width,"onUpdate:modelValue":t[13]||(t[13]=k=>s.width=k),min:10,max:350,size:"small"},null,8,["modelValue"])]),e("div",null,[t[30]||(t[30]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text-secondary mb-1"},"高度",-1)),u(w,{modelValue:s.height,"onUpdate:modelValue":t[14]||(t[14]=k=>s.height=k),min:10,max:350,size:"small"},null,8,["modelValue"])])]),e("div",Ae,[e("div",null,[t[31]||(t[31]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text-secondary mb-1"},"旋转角度",-1)),u(p,{modelValue:s.rotation,"onUpdate:modelValue":t[15]||(t[15]=k=>s.rotation=k),min:-180,max:180,step:1,style:{width:"200px"}},null,8,["modelValue"])]),e("div",Pe,[u(I,{onClick:j,size:"small"},{default:g(()=>t[32]||(t[32]=[$("重置")])),_:1,__:[32]}),u(I,{onClick:d,size:"small"},{default:g(()=>t[33]||(t[33]=[$("居中")])),_:1,__:[33]})])])])):q("",!0),t[35]||(t[35]=e("div",{class:"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4"},[e("h4",{class:"text-sm font-medium text-blue-900 dark:text-blue-100 mb-2"},"图层说明："),e("ul",{class:"text-sm text-blue-800 dark:text-blue-200 space-y-1"},[e("li",null,[$("• "),e("strong",null,"底图"),$("：作为最底层的背景图片")]),e("li",null,[$("• "),e("strong",null,"印刷区域"),$("：可调整位置、大小和旋转角度的印刷图案")]),e("li",null,[$("• "),e("strong",null,"遮罩图"),$("：覆盖在最上层的遮罩效果")])])],-1))])]),_:1},8,["modelValue"]),e("input",{ref_key:"fileInputRef",ref:U,type:"file",accept:"image/*",style:{display:"none"},onChange:D},null,544)],64)}}}),ie=se(He,[["__scopeId","data-v-b6c1410a"]]),Te={class:"space-y-6"},Xe={class:"flex space-x-3"},Ye={class:"border border-gray-200 dark:border-dark-border rounded-lg overflow-hidden"},Fe={class:"flex border-b border-gray-200 dark:border-dark-border"},We={key:0,class:"p-4"},Ne={key:0,class:"grid grid-cols-5 gap-4"},qe=["onClick"],Ge={class:"relative"},Qe=["src","alt"],Je={key:0,class:"absolute inset-0 bg-amber-500 bg-opacity-20 rounded-lg flex items-center justify-center"},Oe={class:"text-xs text-gray-600 dark:text-dark-text-secondary mt-1 truncate"},Ze={key:1,class:"text-center py-12"},et={key:1,class:"p-4"},tt={class:"flex justify-between items-center mb-4"},rt={class:"flex space-x-4"},ot={key:0,class:"grid grid-cols-5 gap-4"},st=["onClick"],lt={class:"relative"},at=["src","alt"],dt={key:0,class:"absolute inset-0 bg-amber-500 bg-opacity-20 rounded-lg flex items-center justify-center"},nt={class:"text-xs text-gray-600 dark:text-dark-text-secondary mt-1 truncate"},it={key:1,class:"text-center py-12"},ut={class:"flex justify-between"},ct={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},mt={class:"flex space-x-3"},ue=oe({__name:"ImageSelectorDialog",props:{modelValue:{type:Boolean},title:{},multiple:{type:Boolean}},emits:["update:modelValue","select"],setup(O,{emit:N}){const P=O,B=N,z=Z({get:()=>P.modelValue,set:b=>B("update:modelValue",b)}),C=f(!1),U=f(!1),M=f([]),R=f([]),i=f([]),s=f(""),r=f(""),V=f([{id:1,name:"商品图片_001.jpg",url:"https://picsum.photos/400/400?random=1",category:"product"},{id:2,name:"商品图片_002.jpg",url:"https://picsum.photos/400/400?random=2",category:"product"},{id:3,name:"背景图片_001.jpg",url:"https://picsum.photos/400/400?random=3",category:"background"},{id:4,name:"素材图片_001.jpg",url:"https://picsum.photos/400/400?random=4",category:"material"},{id:5,name:"商品图片_003.jpg",url:"https://picsum.photos/400/400?random=5",category:"product"},{id:6,name:"商品图片_004.jpg",url:"https://picsum.photos/400/400?random=6",category:"product"},{id:7,name:"背景图片_002.jpg",url:"https://picsum.photos/400/400?random=7",category:"background"},{id:8,name:"素材图片_002.jpg",url:"https://picsum.photos/400/400?random=8",category:"material"}]),E=Z(()=>{let b=V.value;return r.value&&(b=b.filter(c=>c.category===r.value)),s.value&&(b=b.filter(c=>c.name.toLowerCase().includes(s.value.toLowerCase()))),b}),D=b=>i.value.some(c=>typeof b.id=="number"&&typeof c.id=="number"?c.id===b.id:c.url===b.url),A=b=>{if(!P.multiple){i.value=[b];return}const c=i.value.findIndex(L=>typeof b.id=="number"&&typeof L.id=="number"?L.id===b.id:L.url===b.url);c>-1?i.value.splice(c,1):i.value.push(b)},Q=b=>{const c=new FileReader;c.onload=L=>{var m;const d={id:Date.now()+Math.random(),name:b.name,url:(m=L.target)==null?void 0:m.result,file:b.raw};R.value.push(d),P.multiple?i.value.push(d):i.value=[d]},c.readAsDataURL(b.raw)},_=b=>{},a=()=>{},Y=()=>{},H=()=>{B("select",i.value.map(b=>b.url)),B("update:modelValue",!1),i.value=[]},J=b=>{const c={id:Date.now(),name:`效果图_${Date.now()}.jpg`,url:b};R.value.push(c),U.value=!1,P.multiple?i.value.push(c):i.value=[c],ee.success("效果图创建成功")};return ne(()=>{}),(b,c)=>{const L=y("el-button"),j=y("el-upload"),d=y("el-input"),m=y("el-option"),o=y("el-select"),v=y("el-dialog");return l(),n(K,null,[u(v,{modelValue:z.value,"onUpdate:modelValue":c[7]||(c[7]=t=>z.value=t),title:b.title||"选择图片",width:"900px","align-center":""},{footer:g(()=>[e("div",ut,[e("div",ct," 已选择 "+x(i.value.length)+" 张图片 ",1),e("div",mt,[u(L,{onClick:c[6]||(c[6]=t=>z.value=!1)},{default:g(()=>c[19]||(c[19]=[$("取消")])),_:1,__:[19]}),u(L,{type:"primary",onClick:H,disabled:i.value.length===0},{default:g(()=>c[20]||(c[20]=[$(" 确认选择 ")])),_:1,__:[20]},8,["disabled"])])])]),default:g(()=>[e("div",Te,[e("div",Xe,[u(j,{ref:"uploadRef","file-list":M.value,"on-change":Q,"on-remove":_,"auto-upload":!1,multiple:"",accept:"image/*","show-file-list":!1},{default:g(()=>[u(L,{type:"primary"},{default:g(()=>c[9]||(c[9]=[e("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"})],-1),$(" 上传图片 ")])),_:1,__:[9]})]),_:1},8,["file-list"]),u(L,{onClick:c[0]||(c[0]=t=>C.value=!0)},{default:g(()=>c[10]||(c[10]=[e("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1),$(" 从图库选择 ")])),_:1,__:[10]}),u(L,{onClick:c[1]||(c[1]=t=>U.value=!0)},{default:g(()=>c[11]||(c[11]=[e("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"})],-1),$(" 创建效果图 ")])),_:1,__:[11]})]),e("div",Ye,[e("div",Fe,[e("button",{onClick:c[2]||(c[2]=t=>C.value=!1),class:le(["px-4 py-2 text-sm font-medium",C.value?"text-gray-500 dark:text-dark-text-secondary hover:text-gray-700 dark:hover:text-dark-text":"text-amber-600 dark:text-amber-400 border-b-2 border-amber-500"])}," 已上传图片 ",2),e("button",{onClick:c[3]||(c[3]=t=>C.value=!0),class:le(["px-4 py-2 text-sm font-medium",C.value?"text-amber-600 dark:text-amber-400 border-b-2 border-amber-500":"text-gray-500 dark:text-dark-text-secondary hover:text-gray-700 dark:hover:text-dark-text"])}," 图库 ",2)]),C.value?(l(),n("div",et,[e("div",tt,[e("div",rt,[u(d,{modelValue:s.value,"onUpdate:modelValue":c[4]||(c[4]=t=>s.value=t),placeholder:"搜索图片...",style:{width:"300px"},onInput:a},{prefix:g(()=>c[15]||(c[15]=[e("svg",{class:"w-4 h-4 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})],-1)])),_:1},8,["modelValue"]),u(o,{modelValue:r.value,"onUpdate:modelValue":c[5]||(c[5]=t=>r.value=t),placeholder:"选择分类",style:{width:"150px"},onChange:Y},{default:g(()=>[u(m,{label:"全部分类",value:""}),u(m,{label:"商品图片",value:"product"}),u(m,{label:"背景图片",value:"background"}),u(m,{label:"素材图片",value:"material"})]),_:1},8,["modelValue"])])]),E.value.length>0?(l(),n("div",ot,[(l(!0),n(K,null,X(E.value,t=>(l(),n("div",{key:t.id,class:"relative group cursor-pointer",onClick:w=>A(t)},[e("div",lt,[e("img",{src:t.url,alt:t.name,class:le(["w-full h-32 object-cover rounded-lg border-2 transition-all duration-200",D(t)?"border-amber-500":"border-gray-200 dark:border-dark-border hover:border-amber-300"])},null,10,at),D(t)?(l(),n("div",dt,c[16]||(c[16]=[e("div",{class:"w-6 h-6 bg-amber-500 rounded-full flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 13l4 4L19 7"})])],-1)]))):q("",!0),c[17]||(c[17]=e("div",{class:"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-10 rounded-lg transition-all duration-200"},null,-1))]),e("p",nt,x(t.name),1)],8,st))),128))])):(l(),n("div",it,c[18]||(c[18]=[e("svg",{class:"w-12 h-12 text-gray-400 dark:text-dark-text-secondary mx-auto mb-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1),e("p",{class:"text-gray-500 dark:text-dark-text-secondary"},"没有找到匹配的图片",-1)])))])):(l(),n("div",We,[R.value.length>0?(l(),n("div",Ne,[(l(!0),n(K,null,X(R.value,(t,w)=>(l(),n("div",{key:w,class:"relative group",onClick:p=>A(t)},[e("div",Ge,[e("img",{src:t.url,alt:t.name,class:le(["w-full h-32 object-cover rounded-lg border-2 transition-all duration-200",D(t)?"border-amber-500":"border-gray-200 dark:border-dark-border hover:border-amber-300"])},null,10,Qe),D(t)?(l(),n("div",Je,c[12]||(c[12]=[e("div",{class:"w-6 h-6 bg-amber-500 rounded-full flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 13l4 4L19 7"})])],-1)]))):q("",!0),c[13]||(c[13]=e("div",{class:"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-10 rounded-lg transition-all duration-200"},null,-1))]),e("p",Oe,x(t.name),1)],8,qe))),128))])):(l(),n("div",Ze,c[14]||(c[14]=[e("svg",{class:"w-12 h-12 text-gray-400 dark:text-dark-text-secondary mx-auto mb-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1),e("p",{class:"text-gray-500 dark:text-dark-text-secondary"},"暂无上传图片，请点击上方按钮上传",-1)])))]))])])]),_:1},8,["modelValue","title"]),u(ie,{modelValue:U.value,"onUpdate:modelValue":c[8]||(c[8]=t=>U.value=t),onCreated:J},null,8,["modelValue"])],64)}}}),pt={class:"bg-gray-50 dark:bg-dark-card rounded-lg p-6"},gt={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},vt={class:"bg-gray-50 dark:bg-dark-card rounded-lg p-6"},kt={class:"grid grid-cols-1 md:grid-cols-3 gap-6"},bt={key:0,class:"relative"},xt=["src"],ft={class:"absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-30 flex items-center justify-center transition-all rounded-lg"},ht={key:1,class:"h-40 flex flex-col items-center justify-center"},yt={key:0,class:"relative"},wt={class:"grid grid-cols-2 gap-2 h-40"},_t=["src","alt"],$t=["onClick"],Vt={key:0,class:"absolute bottom-2 right-2 bg-gray-800 bg-opacity-70 text-white text-xs px-2 py-1 rounded-full"},Ct={key:1,class:"h-40 flex flex-col items-center justify-center"},It={key:0,class:"relative"},jt={class:"grid grid-cols-2 gap-2 h-40"},Ut=["src","alt"],zt=["onClick"],Mt={key:0,class:"absolute bottom-2 right-2 bg-gray-800 bg-opacity-70 text-white text-xs px-2 py-1 rounded-full"},St={key:1,class:"h-40 flex flex-col items-center justify-center"},Lt={class:"bg-gray-50 dark:bg-dark-card rounded-lg p-6"},Kt={class:"flex items-center justify-between mb-4"},Bt={key:0,class:"space-y-4 mb-6"},Dt={class:"flex-1"},Rt={key:1,class:"border border-gray-200 dark:border-dark-border rounded-lg overflow-hidden"},At={class:"min-w-full divide-y divide-gray-200 dark:divide-dark-border"},Pt={class:"bg-gray-100 dark:bg-dark-border"},Et={class:"bg-white dark:bg-dark-surface divide-y divide-gray-200 dark:divide-dark-border"},Ht={class:"px-4 py-3 text-sm"},Tt={class:"px-4 py-3 text-sm"},Xt={class:"px-4 py-3 text-sm"},Yt={class:"flex items-center"},Ft=["src"],Wt={key:2,class:"text-center py-8 text-gray-500 dark:text-dark-text-secondary"},Nt={key:3,class:"text-center py-8 text-gray-500 dark:text-dark-text-secondary"},qt={class:"flex justify-end space-x-3"},Gt=oe({__name:"CreateProductDialog",props:{modelValue:{type:Boolean}},emits:["update:modelValue","success"],setup(O,{emit:N}){const P=O,B=N,z=Z({get:()=>P.modelValue,set:m=>B("update:modelValue",m)}),C=f(),U=f(!1),M=f(!1),R=f(!1),i=f("cover"),s=Z(()=>({cover:"选择封面图",additional:"选择附图",sku:"选择SKU图"})[i.value]),r=f({name:"",price:0,coverImage:"",additionalImages:[],skuImages:[],skuAttributes:[]}),V=f({name:[{required:!0,message:"请输入商品标题",trigger:"blur"},{min:3,max:100,message:"长度在 3 到 100 个字符",trigger:"blur"}],price:[{required:!0,message:"请输入商品价格",trigger:"blur"}]}),E=Z(()=>{if(r.value.skuAttributes.length===0)return[];if(!r.value.skuAttributes.every(w=>w.values.length>0))return[];const o=(w,p=[],I=0,T=[])=>{if(I===w.length){T.push([...p]);return}for(let k=0;k<w[I].length;k++)p[I]=w[I][k],o(w,p,I+1,T);return T},v=r.value.skuAttributes.map(w=>w.values),t=o(v);return(t==null?void 0:t.map(w=>{const p={};return r.value.skuAttributes.forEach((I,T)=>{p[I.name]=w[T]}),{attributes:p,price:r.value.price,stock:100,image:""}}))||[]}),D=m=>{i.value=m,M.value=!0},A=m=>{i.value==="cover"?r.value.coverImage=m[0]:i.value==="additional"?r.value.additionalImages=[...r.value.additionalImages,...m]:i.value==="sku"&&(r.value.skuImages=[...r.value.skuImages,...m])},Q=m=>{r.value.coverImage=""},_=m=>{r.value.additionalImages.splice(m,1)},a=m=>{r.value.skuImages.splice(m,1)},Y=()=>{r.value.skuAttributes.push({name:"",values:[],inputVisible:!1,inputValue:""})},H=m=>{r.value.skuAttributes.splice(m,1)},J=m=>{r.value.skuAttributes[m].inputVisible=!0,ve(()=>{var o;(o=document.querySelector(".el-input__inner"))==null||o.focus()})},b=m=>{const o=r.value.skuAttributes[m].inputValue;o&&r.value.skuAttributes[m].values.push(o),r.value.skuAttributes[m].inputVisible=!1,r.value.skuAttributes[m].inputValue=""},c=(m,o)=>{r.value.skuAttributes[m].values.splice(o,1)},L=async()=>{C.value&&await C.value.validate((m,o)=>{m&&(U.value=!0,setTimeout(()=>{U.value=!1,ee.success("商品创建成功！"),j(),B("success"),B("update:modelValue",!1)},1e3))})},j=()=>{C.value&&C.value.resetFields(),r.value={name:"",price:0,coverImage:"",additionalImages:[],skuImages:[],skuAttributes:[]}},d=()=>{ee.success("特效创建成功！")};return(m,o)=>{const v=y("el-input"),t=y("el-form-item"),w=y("el-input-number"),p=y("el-button"),I=y("el-tag"),T=y("el-option"),k=y("el-select"),G=y("el-form"),ce=y("el-dialog");return l(),n(K,null,[u(ce,{modelValue:z.value,"onUpdate:modelValue":o[7]||(o[7]=h=>z.value=h),title:"创建商品",width:"1000px","align-center":"",onClose:j,class:"create-product-dialog"},{footer:g(()=>[e("div",qt,[u(p,{onClick:o[6]||(o[6]=h=>z.value=!1)},{default:g(()=>o[28]||(o[28]=[$("取消")])),_:1,__:[28]}),u(p,{type:"primary",onClick:L,loading:U.value},{default:g(()=>o[29]||(o[29]=[$("创建商品")])),_:1,__:[29]},8,["loading"])])]),default:g(()=>[u(G,{ref_key:"formRef",ref:C,model:r.value,rules:V.value,"label-position":"top",class:"space-y-6"},{default:g(()=>[e("div",pt,[o[10]||(o[10]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-dark-text mb-4"},"基本信息",-1)),e("div",gt,[u(t,{label:"商品标题",prop:"name"},{default:g(()=>[u(v,{modelValue:r.value.name,"onUpdate:modelValue":o[0]||(o[0]=h=>r.value.name=h),placeholder:"请输入商品标题",maxlength:"100","show-word-limit":""},null,8,["modelValue"])]),_:1}),u(t,{label:"商品价格",prop:"price"},{default:g(()=>[u(w,{modelValue:r.value.price,"onUpdate:modelValue":o[1]||(o[1]=h=>r.value.price=h),min:0,precision:2,step:.1,style:{width:"100%"}},null,8,["modelValue"])]),_:1})])]),e("div",vt,[o[20]||(o[20]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-dark-text mb-4"},"商品图片",-1)),e("div",kt,[e("div",null,[o[13]||(o[13]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text-secondary mb-2"},"封面图",-1)),e("div",{class:"border-2 border-dashed border-gray-300 dark:border-dark-border rounded-lg p-4 text-center cursor-pointer hover:border-amber-500 dark:hover:border-amber-500 transition-colors",onClick:o[3]||(o[3]=h=>D("cover"))},[r.value.coverImage?(l(),n("div",bt,[e("img",{src:r.value.coverImage,alt:"封面图",class:"w-full h-40 object-cover rounded-lg"},null,8,xt),e("div",ft,[e("button",{onClick:o[2]||(o[2]=W(h=>Q("cover"),["stop"])),class:"absolute top-2 right-2 w-8 h-8 bg-red-500 text-white rounded-full opacity-0 hover:opacity-100 flex items-center justify-center"},o[11]||(o[11]=[e("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))])])):(l(),n("div",ht,o[12]||(o[12]=[e("svg",{class:"w-12 h-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1),e("p",{class:"mt-2 text-sm text-gray-500 dark:text-dark-text-secondary"},"点击上传封面图",-1)])))])]),e("div",null,[o[16]||(o[16]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text-secondary mb-2"},"附图（多张）",-1)),e("div",{class:"border-2 border-dashed border-gray-300 dark:border-dark-border rounded-lg p-4 text-center cursor-pointer hover:border-amber-500 dark:hover:border-amber-500 transition-colors",onClick:o[4]||(o[4]=h=>D("additional"))},[r.value.additionalImages.length>0?(l(),n("div",yt,[e("div",wt,[(l(!0),n(K,null,X(r.value.additionalImages.slice(0,4),(h,F)=>(l(),n("div",{key:F,class:"relative"},[e("img",{src:h,alt:`附图${F+1}`,class:"w-full h-full object-cover rounded-lg"},null,8,_t),e("button",{onClick:W(S=>_(F),["stop"]),class:"absolute top-1 right-1 w-6 h-6 bg-red-500 text-white rounded-full opacity-0 hover:opacity-100 flex items-center justify-center"},o[14]||(o[14]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]),8,$t)]))),128))]),r.value.additionalImages.length>4?(l(),n("div",Vt," +"+x(r.value.additionalImages.length-4),1)):q("",!0)])):(l(),n("div",Ct,o[15]||(o[15]=[e("svg",{class:"w-12 h-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1),e("p",{class:"mt-2 text-sm text-gray-500 dark:text-dark-text-secondary"},"点击上传附图",-1)])))])]),e("div",null,[o[19]||(o[19]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text-secondary mb-2"},"SKU图",-1)),e("div",{class:"border-2 border-dashed border-gray-300 dark:border-dark-border rounded-lg p-4 text-center cursor-pointer hover:border-amber-500 dark:hover:border-amber-500 transition-colors",onClick:o[5]||(o[5]=h=>D("sku"))},[r.value.skuImages.length>0?(l(),n("div",It,[e("div",jt,[(l(!0),n(K,null,X(r.value.skuImages.slice(0,4),(h,F)=>(l(),n("div",{key:F,class:"relative"},[e("img",{src:h,alt:`SKU图${F+1}`,class:"w-full h-full object-cover rounded-lg"},null,8,Ut),e("button",{onClick:W(S=>a(F),["stop"]),class:"absolute top-1 right-1 w-6 h-6 bg-red-500 text-white rounded-full opacity-0 hover:opacity-100 flex items-center justify-center"},o[17]||(o[17]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]),8,zt)]))),128))]),r.value.skuImages.length>4?(l(),n("div",Mt," +"+x(r.value.skuImages.length-4),1)):q("",!0)])):(l(),n("div",St,o[18]||(o[18]=[e("svg",{class:"w-12 h-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1),e("p",{class:"mt-2 text-sm text-gray-500 dark:text-dark-text-secondary"},"点击上传SKU图",-1)])))])])])]),e("div",Lt,[e("div",Kt,[o[22]||(o[22]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-dark-text"},"SKU设置",-1)),u(p,{type:"primary",onClick:Y,size:"small"},{default:g(()=>o[21]||(o[21]=[$("添加属性")])),_:1,__:[21]})]),r.value.skuAttributes.length>0?(l(),n("div",Bt,[(l(!0),n(K,null,X(r.value.skuAttributes,(h,F)=>(l(),n("div",{key:F,class:"flex items-start space-x-4"},[u(v,{modelValue:h.name,"onUpdate:modelValue":S=>h.name=S,placeholder:"属性名称，如：颜色、尺码",style:{width:"200px"}},null,8,["modelValue","onUpdate:modelValue"]),e("div",Dt,[(l(!0),n(K,null,X(h.values,(S,re)=>(l(),te(I,{key:re,closable:"",onClose:Wo=>c(F,re),class:"mr-2 mb-2"},{default:g(()=>[$(x(S),1)]),_:2},1032,["onClose"]))),128)),h.inputVisible?(l(),te(v,{key:0,ref_for:!0,ref:"inputRef",modelValue:h.inputValue,"onUpdate:modelValue":S=>h.inputValue=S,size:"small",style:{width:"100px"},onKeyup:ge(S=>b(F),["enter"]),onBlur:S=>b(F)},null,8,["modelValue","onUpdate:modelValue","onKeyup","onBlur"])):(l(),te(p,{key:1,size:"small",onClick:S=>J(F)},{default:g(()=>o[23]||(o[23]=[$("+ 添加值")])),_:2,__:[23]},1032,["onClick"]))]),u(p,{type:"danger",onClick:S=>H(F),size:"small",circle:""},{default:g(()=>o[24]||(o[24]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)])),_:2,__:[24]},1032,["onClick"])]))),128))])):q("",!0),E.value.length>0?(l(),n("div",Rt,[e("table",At,[e("thead",Pt,[e("tr",null,[(l(!0),n(K,null,X(r.value.skuAttributes,h=>(l(),n("th",{key:h.name,class:"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-dark-text-secondary uppercase tracking-wider"},x(h.name),1))),128)),o[25]||(o[25]=e("th",{class:"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-dark-text-secondary uppercase tracking-wider"}," 价格 ",-1)),o[26]||(o[26]=e("th",{class:"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-dark-text-secondary uppercase tracking-wider"}," 库存 ",-1)),o[27]||(o[27]=e("th",{class:"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-dark-text-secondary uppercase tracking-wider"}," SKU图 ",-1))])]),e("tbody",Et,[(l(!0),n(K,null,X(E.value,(h,F)=>(l(),n("tr",{key:F},[(l(!0),n(K,null,X(r.value.skuAttributes,S=>(l(),n("td",{key:S.name,class:"px-4 py-3 text-sm text-gray-900 dark:text-dark-text"},x(h.attributes[S.name]),1))),128)),e("td",Ht,[u(w,{modelValue:h.price,"onUpdate:modelValue":S=>h.price=S,min:0,precision:2,step:.1,size:"small",style:{width:"120px"}},null,8,["modelValue","onUpdate:modelValue"])]),e("td",Tt,[u(w,{modelValue:h.stock,"onUpdate:modelValue":S=>h.stock=S,min:0,precision:0,step:1,size:"small",style:{width:"120px"}},null,8,["modelValue","onUpdate:modelValue"])]),e("td",Xt,[u(k,{modelValue:h.image,"onUpdate:modelValue":S=>h.image=S,placeholder:"选择SKU图",size:"small",style:{width:"120px"}},{default:g(()=>[(l(!0),n(K,null,X(r.value.skuImages,(S,re)=>(l(),te(T,{key:re,label:`图片${re+1}`,value:S},{default:g(()=>[e("div",Yt,[e("img",{src:S,class:"w-8 h-8 object-cover rounded mr-2"},null,8,Ft),e("span",null,"图片"+x(re+1),1)])]),_:2},1032,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])])]))),128))])])])):r.value.skuAttributes.length>0?(l(),n("div",Wt," 请为每个属性添加值，系统将自动生成SKU组合 ")):(l(),n("div",Nt," 请添加SKU属性，如颜色、尺码等 "))])]),_:1},8,["model","rules"])]),_:1},8,["modelValue"]),u(ue,{modelValue:M.value,"onUpdate:modelValue":o[8]||(o[8]=h=>M.value=h),title:s.value,multiple:i.value!=="cover",onSelect:A},null,8,["modelValue","title","multiple"]),u(ie,{modelValue:R.value,"onUpdate:modelValue":o[9]||(o[9]=h=>R.value=h),onSuccess:d},null,8,["modelValue"])],64)}}}),Qt=se(Gt,[["__scopeId","data-v-8a9611a0"]]),Jt={class:"flex items-center justify-between p-6 border-b border-gray-100 dark:border-dark-border"},Ot={class:"flex items-center space-x-3"},Zt={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},er={key:0,class:"p-6 space-y-8"},tr={class:"grid grid-cols-1 md:grid-cols-2 gap-8"},rr={class:"mb-4"},or={class:"grid grid-cols-5 gap-2"},sr=["src"],lr=["onClick"],ar=["src"],dr={class:"space-y-6"},nr={class:"text-2xl font-bold text-gray-900 dark:text-dark-text"},ir={class:"mt-2 flex items-center space-x-4"},ur={class:"text-xl font-bold text-amber-600 dark:text-amber-400"},cr={class:"text-sm text-gray-500 dark:text-dark-text-secondary"},mr={class:"border-t border-gray-100 dark:border-dark-border pt-4"},pr={class:"grid grid-cols-2 gap-4"},gr={class:"text-sm font-medium text-gray-900 dark:text-dark-text"},vr={class:"text-sm font-medium text-gray-900 dark:text-dark-text"},kr={class:"border-t border-gray-100 dark:border-dark-border pt-4"},br={class:"flex space-x-3"},xr={class:"border-t border-gray-100 dark:border-dark-border pt-6"},fr={class:"font-mono text-sm text-gray-600 dark:text-dark-text-secondary"},hr={class:"flex flex-wrap gap-2"},yr={class:"font-medium text-green-600 dark:text-green-400"},wr={class:"font-medium text-gray-900 dark:text-dark-text"},_r={key:0,class:"flex justify-center"},$r={key:1,class:"text-gray-400"},Vr=oe({__name:"ViewProductDialog",props:{modelValue:{type:Boolean},product:{}},emits:["update:modelValue"],setup(O,{emit:N}){const P=O,B=N,z=Z({get:()=>P.modelValue,set:s=>B("update:modelValue",s)}),C=f(""),U=()=>{z.value=!1},M=s=>{C.value=s},R=()=>{ee.success("编辑商品功能开发中...")},i=()=>{ee.success("创建刊登功能开发中...")};return(s,r)=>{const V=y("el-image"),E=y("el-button"),D=y("el-table-column"),A=y("el-tag"),Q=y("el-table"),_=y("el-dialog");return l(),te(_,{modelValue:z.value,"onUpdate:modelValue":r[1]||(r[1]=a=>z.value=a),width:"1200px","before-close":U,"show-close":!1,class:"modern-dialog"},{header:g(()=>{var a;return[e("div",Jt,[e("div",Ot,[r[3]||(r[3]=e("div",{class:"w-10 h-10 bg-gradient-to-br from-amber-500 to-amber-600 rounded-xl flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"})])],-1)),e("div",null,[r[2]||(r[2]=e("h3",{class:"text-xl font-bold text-gray-900 dark:text-dark-text"},"商品详情",-1)),e("p",Zt,"ID: "+x(((a=s.product)==null?void 0:a.id)||""),1)])]),e("button",{onClick:U,class:"p-2 text-gray-400 hover:text-gray-600 dark:text-dark-text-secondary dark:hover:text-dark-text rounded-lg hover:bg-gray-100 dark:hover:bg-dark-card transition-all duration-200"},r[4]||(r[4]=[e("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))])]}),footer:g(()=>[e("div",{class:"flex items-center justify-end p-6 border-t border-gray-100 dark:border-dark-border bg-gray-50 dark:bg-dark-card/50"},[e("button",{onClick:U,class:"px-6 py-2.5 text-gray-700 dark:text-dark-text font-medium rounded-lg border border-gray-300 dark:border-dark-border hover:bg-gray-50 dark:hover:bg-dark-border transition-all duration-200"}," 关闭 ")])]),default:g(()=>[s.product?(l(),n("div",er,[e("div",tr,[e("div",null,[e("div",rr,[u(V,{src:s.product.coverImage,"preview-src-list":[s.product.coverImage,...s.product.additionalImages],fit:"cover",class:"w-full h-80 object-cover rounded-lg border border-gray-200 dark:border-dark-border","preview-teleported":!0},null,8,["src","preview-src-list"])]),e("div",or,[e("div",{class:"cursor-pointer border-2 rounded-lg overflow-hidden transition-all duration-200 border-amber-500",onClick:r[0]||(r[0]=a=>M(s.product.coverImage))},[e("img",{src:s.product.coverImage,class:"w-full h-16 object-cover"},null,8,sr)]),(l(!0),n(K,null,X(s.product.additionalImages,(a,Y)=>(l(),n("div",{key:Y,class:"cursor-pointer border-2 border-gray-200 dark:border-dark-border hover:border-amber-500 rounded-lg overflow-hidden transition-all duration-200",onClick:H=>M(a)},[e("img",{src:a,class:"w-full h-16 object-cover"},null,8,ar)],8,lr))),128))])]),e("div",dr,[e("div",null,[e("h2",nr,x(s.product.name),1),e("div",ir,[e("div",ur," ¥"+x(s.product.minPrice.toFixed(2)),1),e("div",cr," SKU数量: "+x(s.product.skuCount),1)])]),e("div",mr,[r[7]||(r[7]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-dark-text mb-3"},"创建信息",-1)),e("div",pr,[e("div",null,[r[5]||(r[5]=e("p",{class:"text-sm text-gray-500 dark:text-dark-text-secondary"},"创建人",-1)),e("p",gr,x(s.product.creator),1)]),e("div",null,[r[6]||(r[6]=e("p",{class:"text-sm text-gray-500 dark:text-dark-text-secondary"},"创建时间",-1)),e("p",vr,x(s.product.createTime),1)])])]),e("div",kr,[r[10]||(r[10]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-dark-text mb-3"},"操作",-1)),e("div",br,[u(E,{type:"primary",onClick:R},{default:g(()=>r[8]||(r[8]=[e("svg",{class:"w-5 h-5 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})],-1),$(" 编辑商品 ")])),_:1,__:[8]}),u(E,{onClick:i},{default:g(()=>r[9]||(r[9]=[e("svg",{class:"w-5 h-5 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"})],-1),$(" 创建刊登 ")])),_:1,__:[9]})])])])]),e("div",xr,[r[11]||(r[11]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-dark-text mb-4"},"SKU信息",-1)),u(Q,{data:s.product.skus,style:{width:"100%"},class:"modern-table","header-cell-style":{backgroundColor:"var(--el-bg-color-page)",color:"var(--el-text-color-primary)",fontWeight:"600",borderBottom:"1px solid var(--el-border-color-light)"},"row-style":{backgroundColor:"transparent"}},{default:g(()=>[u(D,{prop:"id",label:"SKU ID",width:"120"},{default:g(a=>[e("span",fr,x(a.row.id),1)]),_:1}),u(D,{label:"属性","min-width":"200"},{default:g(a=>[e("div",hr,[(l(!0),n(K,null,X(a.row.attributes,(Y,H)=>(l(),te(A,{key:H,size:"small",class:"mr-1"},{default:g(()=>[$(x(H)+": "+x(Y),1)]),_:2},1024))),128))])]),_:1}),u(D,{prop:"price",label:"价格",width:"120"},{default:g(a=>[e("span",yr,"¥"+x(a.row.price.toFixed(2)),1)]),_:1}),u(D,{prop:"stock",label:"库存",width:"100"},{default:g(a=>[e("span",wr,x(a.row.stock),1)]),_:1}),u(D,{label:"SKU图",width:"100"},{default:g(a=>[a.row.image?(l(),n("div",_r,[u(V,{src:a.row.image,"preview-src-list":[a.row.image],fit:"cover",class:"w-12 h-12 rounded-lg border border-gray-200 dark:border-dark-border","preview-teleported":!0},null,8,["src","preview-src-list"])])):(l(),n("span",$r,"-"))]),_:1})]),_:1},8,["data"])])])):q("",!0)]),_:1},8,["modelValue"])}}}),Cr=se(Vr,[["__scopeId","data-v-166d4f86"]]),Ir={key:0,class:"space-y-6"},jr={class:"bg-gray-50 dark:bg-dark-card rounded-lg p-6"},Ur={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},zr={class:"bg-gray-50 dark:bg-dark-card rounded-lg p-6"},Mr={class:"grid grid-cols-1 md:grid-cols-3 gap-6"},Sr={key:0,class:"relative"},Lr=["src"],Kr={key:1,class:"h-40 flex flex-col items-center justify-center"},Br={key:0,class:"relative"},Dr={class:"grid grid-cols-2 gap-2 h-40"},Rr=["src","alt"],Ar=["onClick"],Pr={key:0,class:"absolute bottom-2 right-2 bg-gray-800 bg-opacity-70 text-white text-xs px-2 py-1 rounded-full"},Er={key:1,class:"h-40 flex flex-col items-center justify-center"},Hr={key:0,class:"relative"},Tr={class:"grid grid-cols-2 gap-2 h-40"},Xr=["src","alt"],Yr=["onClick"],Fr={key:0,class:"absolute bottom-2 right-2 bg-gray-800 bg-opacity-70 text-white text-xs px-2 py-1 rounded-full"},Wr={key:1,class:"h-40 flex flex-col items-center justify-center"},Nr={class:"bg-gray-50 dark:bg-dark-card rounded-lg p-6"},qr={class:"border border-gray-200 dark:border-dark-border rounded-lg overflow-hidden"},Gr={class:"min-w-full divide-y divide-gray-200 dark:divide-dark-border"},Qr={class:"bg-white dark:bg-dark-surface divide-y divide-gray-200 dark:divide-dark-border"},Jr={class:"px-4 py-3 text-sm text-gray-900 dark:text-dark-text"},Or={class:"px-4 py-3 text-sm"},Zr={class:"flex flex-wrap gap-1"},eo={class:"px-4 py-3 text-sm"},to={class:"px-4 py-3 text-sm"},ro={class:"px-4 py-3 text-sm"},oo={class:"flex items-center"},so=["src"],lo={class:"flex justify-end space-x-3"},ao=oe({__name:"EditProductDialog",props:{modelValue:{type:Boolean},product:{}},emits:["update:modelValue","success"],setup(O,{emit:N}){const P=O,B=N,z=Z({get:()=>P.modelValue,set:_=>B("update:modelValue",_)}),C=f(!1),U=f(!1),M=f("cover"),R=Z(()=>({cover:"选择封面图",additional:"选择附图",sku:"选择SKU图"})[M.value]),i=f({name:"",price:0,coverImage:"",additionalImages:[],skuImages:[],skus:[]});ke(()=>P.product,_=>{_&&(i.value={name:_.name,price:_.minPrice,coverImage:_.coverImage,additionalImages:[..._.additionalImages],skuImages:[],skus:[..._.skus]})},{immediate:!0});const s=_=>{M.value=_,U.value=!0},r=_=>{M.value==="cover"?i.value.coverImage=_[0]:M.value==="additional"?i.value.additionalImages=[...i.value.additionalImages,..._]:M.value==="sku"&&(i.value.skuImages=[...i.value.skuImages,..._])},V=_=>{i.value.coverImage=""},E=_=>{i.value.additionalImages.splice(_,1)},D=_=>{i.value.skuImages.splice(_,1)},A=()=>{C.value=!0,setTimeout(()=>{C.value=!1,ee.success("商品修改成功！"),B("success"),B("update:modelValue",!1)},1e3)},Q=()=>{};return(_,a)=>{const Y=y("el-input"),H=y("el-input-number"),J=y("el-tag"),b=y("el-option"),c=y("el-select"),L=y("el-button"),j=y("el-dialog");return l(),n(K,null,[u(j,{modelValue:z.value,"onUpdate:modelValue":a[7]||(a[7]=d=>z.value=d),title:"编辑商品",width:"1000px","align-center":"",onClose:Q,class:"edit-product-dialog"},{footer:g(()=>[e("div",lo,[u(L,{onClick:a[6]||(a[6]=d=>z.value=!1)},{default:g(()=>a[24]||(a[24]=[$("取消")])),_:1,__:[24]}),u(L,{type:"primary",onClick:A,loading:C.value},{default:g(()=>a[25]||(a[25]=[$("保存修改")])),_:1,__:[25]},8,["loading"])])]),default:g(()=>[_.product?(l(),n("div",Ir,[e("div",jr,[a[11]||(a[11]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-dark-text mb-4"},"基本信息",-1)),e("div",Ur,[e("div",null,[a[9]||(a[9]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text-secondary mb-2"},"商品标题",-1)),u(Y,{modelValue:i.value.name,"onUpdate:modelValue":a[0]||(a[0]=d=>i.value.name=d),placeholder:"请输入商品标题",maxlength:"100","show-word-limit":""},null,8,["modelValue"])]),e("div",null,[a[10]||(a[10]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text-secondary mb-2"},"商品价格",-1)),u(H,{modelValue:i.value.price,"onUpdate:modelValue":a[1]||(a[1]=d=>i.value.price=d),min:0,precision:2,step:.1,style:{width:"100%"}},null,8,["modelValue"])])])]),e("div",zr,[a[21]||(a[21]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-dark-text mb-4"},"商品图片",-1)),e("div",Mr,[e("div",null,[a[14]||(a[14]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text-secondary mb-2"},"封面图",-1)),e("div",{class:"border-2 border-dashed border-gray-300 dark:border-dark-border rounded-lg p-4 text-center cursor-pointer hover:border-amber-500 dark:hover:border-amber-500 transition-colors",onClick:a[3]||(a[3]=d=>s("cover"))},[i.value.coverImage?(l(),n("div",Sr,[e("img",{src:i.value.coverImage,alt:"封面图",class:"w-full h-40 object-cover rounded-lg"},null,8,Lr),e("button",{onClick:a[2]||(a[2]=W(d=>V("cover"),["stop"])),class:"absolute top-2 right-2 w-8 h-8 bg-red-500 text-white rounded-full flex items-center justify-center hover:bg-red-600"},a[12]||(a[12]=[e("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))])):(l(),n("div",Kr,a[13]||(a[13]=[e("svg",{class:"w-12 h-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1),e("p",{class:"mt-2 text-sm text-gray-500 dark:text-dark-text-secondary"},"点击更换封面图",-1)])))])]),e("div",null,[a[17]||(a[17]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text-secondary mb-2"},"附图（多张）",-1)),e("div",{class:"border-2 border-dashed border-gray-300 dark:border-dark-border rounded-lg p-4 text-center cursor-pointer hover:border-amber-500 dark:hover:border-amber-500 transition-colors",onClick:a[4]||(a[4]=d=>s("additional"))},[i.value.additionalImages.length>0?(l(),n("div",Br,[e("div",Dr,[(l(!0),n(K,null,X(i.value.additionalImages.slice(0,4),(d,m)=>(l(),n("div",{key:m,class:"relative"},[e("img",{src:d,alt:`附图${m+1}`,class:"w-full h-full object-cover rounded-lg"},null,8,Rr),e("button",{onClick:W(o=>E(m),["stop"]),class:"absolute top-1 right-1 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center hover:bg-red-600"},a[15]||(a[15]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]),8,Ar)]))),128))]),i.value.additionalImages.length>4?(l(),n("div",Pr," +"+x(i.value.additionalImages.length-4),1)):q("",!0)])):(l(),n("div",Er,a[16]||(a[16]=[e("svg",{class:"w-12 h-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1),e("p",{class:"mt-2 text-sm text-gray-500 dark:text-dark-text-secondary"},"点击添加附图",-1)])))])]),e("div",null,[a[20]||(a[20]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text-secondary mb-2"},"SKU图",-1)),e("div",{class:"border-2 border-dashed border-gray-300 dark:border-dark-border rounded-lg p-4 text-center cursor-pointer hover:border-amber-500 dark:hover:border-amber-500 transition-colors",onClick:a[5]||(a[5]=d=>s("sku"))},[i.value.skuImages.length>0?(l(),n("div",Hr,[e("div",Tr,[(l(!0),n(K,null,X(i.value.skuImages.slice(0,4),(d,m)=>(l(),n("div",{key:m,class:"relative"},[e("img",{src:d,alt:`SKU图${m+1}`,class:"w-full h-full object-cover rounded-lg"},null,8,Xr),e("button",{onClick:W(o=>D(m),["stop"]),class:"absolute top-1 right-1 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center hover:bg-red-600"},a[18]||(a[18]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]),8,Yr)]))),128))]),i.value.skuImages.length>4?(l(),n("div",Fr," +"+x(i.value.skuImages.length-4),1)):q("",!0)])):(l(),n("div",Wr,a[19]||(a[19]=[e("svg",{class:"w-12 h-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1),e("p",{class:"mt-2 text-sm text-gray-500 dark:text-dark-text-secondary"},"点击添加SKU图",-1)])))])])])]),e("div",Nr,[a[23]||(a[23]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-dark-text mb-4"},"SKU信息",-1)),e("div",qr,[e("table",Gr,[a[22]||(a[22]=e("thead",{class:"bg-gray-100 dark:bg-dark-border"},[e("tr",null,[e("th",{class:"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-dark-text-secondary uppercase tracking-wider"}," SKU ID "),e("th",{class:"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-dark-text-secondary uppercase tracking-wider"}," 属性 "),e("th",{class:"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-dark-text-secondary uppercase tracking-wider"}," 价格 "),e("th",{class:"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-dark-text-secondary uppercase tracking-wider"}," 库存 "),e("th",{class:"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-dark-text-secondary uppercase tracking-wider"}," SKU图 ")])],-1)),e("tbody",Qr,[(l(!0),n(K,null,X(i.value.skus,(d,m)=>(l(),n("tr",{key:m},[e("td",Jr,x(d.id),1),e("td",Or,[e("div",Zr,[(l(!0),n(K,null,X(d.attributes,(o,v)=>(l(),te(J,{key:v,size:"small"},{default:g(()=>[$(x(v)+": "+x(o),1)]),_:2},1024))),128))])]),e("td",eo,[u(H,{modelValue:d.price,"onUpdate:modelValue":o=>d.price=o,min:0,precision:2,step:.1,size:"small",style:{width:"120px"}},null,8,["modelValue","onUpdate:modelValue"])]),e("td",to,[u(H,{modelValue:d.stock,"onUpdate:modelValue":o=>d.stock=o,min:0,precision:0,step:1,size:"small",style:{width:"120px"}},null,8,["modelValue","onUpdate:modelValue"])]),e("td",ro,[u(c,{modelValue:d.image,"onUpdate:modelValue":o=>d.image=o,placeholder:"选择SKU图",size:"small",style:{width:"120px"}},{default:g(()=>[(l(!0),n(K,null,X(i.value.skuImages,(o,v)=>(l(),te(b,{key:v,label:`图片${v+1}`,value:o},{default:g(()=>[e("div",oo,[e("img",{src:o,class:"w-8 h-8 object-cover rounded mr-2"},null,8,so),e("span",null,"图片"+x(v+1),1)])]),_:2},1032,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])])]))),128))])])])])])):q("",!0)]),_:1},8,["modelValue"]),u(ue,{modelValue:U.value,"onUpdate:modelValue":a[8]||(a[8]=d=>U.value=d),title:R.value,multiple:M.value!=="cover",onSelect:r},null,8,["modelValue","title","multiple"])],64)}}}),no=se(ao,[["__scopeId","data-v-7d49c9bd"]]),io={class:"space-y-6"},uo={class:"grid grid-cols-1 md:grid-cols-4 gap-6"},co={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},mo={class:"flex items-center justify-between"},po={class:"text-2xl font-bold text-gray-900 dark:text-dark-text"},go={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},vo={class:"flex items-center justify-between"},ko={class:"text-2xl font-bold text-green-600 dark:text-green-400"},bo={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},xo={class:"flex items-center justify-between"},fo={class:"text-2xl font-bold text-blue-600 dark:text-blue-400"},ho={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},yo={class:"flex items-center justify-between"},wo={class:"text-2xl font-bold text-orange-600 dark:text-orange-400"},_o={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border"},$o={class:"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0"},Vo={class:"flex items-center space-x-3"},Co={key:0,class:"flex items-center space-x-2 ml-4 pl-4 border-l border-gray-200 dark:border-dark-border"},Io={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},jo={class:"relative"},Uo={class:"bg-white dark:bg-dark-surface rounded-xl shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border overflow-hidden"},zo={class:"overflow-x-auto"},Mo={class:"flex justify-center"},So={class:"font-medium text-gray-900 dark:text-dark-text"},Lo={class:"font-medium text-green-600 dark:text-green-400"},Ko={class:"text-sm font-medium text-blue-600 dark:text-blue-400"},Bo={class:"flex items-center space-x-2"},Do={class:"w-6 h-6 bg-gradient-to-br from-amber-400 to-amber-500 rounded-full flex items-center justify-center"},Ro={class:"text-white text-xs font-medium"},Ao={class:"text-sm text-gray-900 dark:text-dark-text"},Po={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},Eo={class:"flex items-center space-x-2"},Ho=["onClick"],To=["onClick"],Xo={class:"flex justify-between items-center px-6 py-4 border-t border-gray-100 dark:border-dark-border bg-gray-50 dark:bg-dark-card/50"},Yo={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},Fo=oe({__name:"index",setup(O){const N=f(!1),P=f(!1),B=f(!1),z=f(!1),C=f(null),U=f([]),M=f(""),R=f(128),i=f(456),s=f(12),r=f(199.99),V=f({currentPage:1,pageSize:10,total:0}),E=f([{id:"P001",name:"时尚休闲T恤 纯棉圆领短袖",minPrice:69.9,skuCount:6,coverImage:"https://picsum.photos/400/400?random=1",additionalImages:["https://picsum.photos/400/400?random=11","https://picsum.photos/400/400?random=12"],skus:[{id:"SKU001",attributes:{color:"白色",size:"M"},price:69.9,stock:100},{id:"SKU002",attributes:{color:"白色",size:"L"},price:69.9,stock:80},{id:"SKU003",attributes:{color:"黑色",size:"M"},price:69.9,stock:90},{id:"SKU004",attributes:{color:"黑色",size:"L"},price:69.9,stock:70},{id:"SKU005",attributes:{color:"蓝色",size:"M"},price:69.9,stock:85},{id:"SKU006",attributes:{color:"蓝色",size:"L"},price:69.9,stock:65}],creator:"张三",createTime:"2024-01-15 14:30:25"},{id:"P002",name:"夏季薄款牛仔裤 直筒宽松",minPrice:129.9,skuCount:4,coverImage:"https://picsum.photos/400/400?random=2",additionalImages:["https://picsum.photos/400/400?random=21","https://picsum.photos/400/400?random=22"],skus:[{id:"SKU007",attributes:{color:"浅蓝",size:"30"},price:129.9,stock:50},{id:"SKU008",attributes:{color:"浅蓝",size:"32"},price:129.9,stock:45},{id:"SKU009",attributes:{color:"深蓝",size:"30"},price:129.9,stock:55},{id:"SKU010",attributes:{color:"深蓝",size:"32"},price:129.9,stock:40}],creator:"李四",createTime:"2024-01-15 13:45:12"},{id:"P003",name:"运动休闲鞋 轻便透气",minPrice:199.9,skuCount:8,coverImage:"https://picsum.photos/400/400?random=3",additionalImages:["https://picsum.photos/400/400?random=31","https://picsum.photos/400/400?random=32"],skus:[{id:"SKU011",attributes:{color:"白色",size:"39"},price:199.9,stock:30},{id:"SKU012",attributes:{color:"白色",size:"40"},price:199.9,stock:35},{id:"SKU013",attributes:{color:"白色",size:"41"},price:199.9,stock:40},{id:"SKU014",attributes:{color:"白色",size:"42"},price:199.9,stock:38},{id:"SKU015",attributes:{color:"黑色",size:"39"},price:199.9,stock:32},{id:"SKU016",attributes:{color:"黑色",size:"40"},price:199.9,stock:36},{id:"SKU017",attributes:{color:"黑色",size:"41"},price:199.9,stock:42},{id:"SKU018",attributes:{color:"黑色",size:"42"},price:199.9,stock:40}],creator:"王五",createTime:"2024-01-15 12:20:08"},{id:"P004",name:"女士连衣裙 碎花雪纺",minPrice:159.9,skuCount:6,coverImage:"https://picsum.photos/400/400?random=4",additionalImages:["https://picsum.photos/400/400?random=41","https://picsum.photos/400/400?random=42"],skus:[{id:"SKU019",attributes:{color:"蓝色",size:"S"},price:159.9,stock:25},{id:"SKU020",attributes:{color:"蓝色",size:"M"},price:159.9,stock:30},{id:"SKU021",attributes:{color:"蓝色",size:"L"},price:159.9,stock:20},{id:"SKU022",attributes:{color:"粉色",size:"S"},price:159.9,stock:28},{id:"SKU023",attributes:{color:"粉色",size:"M"},price:159.9,stock:32},{id:"SKU024",attributes:{color:"粉色",size:"L"},price:159.9,stock:22}],creator:"赵六",createTime:"2024-01-15 11:15:33"},{id:"P005",name:"男士商务西装 修身款",minPrice:599.9,skuCount:9,coverImage:"https://picsum.photos/400/400?random=5",additionalImages:["https://picsum.photos/400/400?random=51","https://picsum.photos/400/400?random=52"],skus:[{id:"SKU025",attributes:{color:"黑色",size:"48"},price:599.9,stock:15},{id:"SKU026",attributes:{color:"黑色",size:"50"},price:599.9,stock:18},{id:"SKU027",attributes:{color:"黑色",size:"52"},price:599.9,stock:12},{id:"SKU028",attributes:{color:"藏青",size:"48"},price:599.9,stock:16},{id:"SKU029",attributes:{color:"藏青",size:"50"},price:599.9,stock:19},{id:"SKU030",attributes:{color:"藏青",size:"52"},price:599.9,stock:14},{id:"SKU031",attributes:{color:"灰色",size:"48"},price:599.9,stock:17},{id:"SKU032",attributes:{color:"灰色",size:"50"},price:599.9,stock:20},{id:"SKU033",attributes:{color:"灰色",size:"52"},price:599.9,stock:15}],creator:"钱七",createTime:"2024-01-15 10:30:45"}]),D=Z(()=>{let j=E.value;M.value&&(j=j.filter(o=>o.name.toLowerCase().includes(M.value.toLowerCase())||o.skus.some(v=>v.id.toLowerCase().includes(M.value.toLowerCase()))));const d=(V.value.currentPage-1)*V.value.pageSize,m=d+V.value.pageSize;return j.slice(d,m)});ne(()=>{A()});const A=()=>{N.value=!0,setTimeout(()=>{V.value.total=E.value.length,N.value=!1},500)},Q=j=>{U.value=j},_=()=>{V.value.currentPage=1,A()},a=j=>{C.value=j,B.value=!0},Y=j=>{C.value=j,z.value=!0},H=()=>{ee.success("导出商品功能开发中...")},J=()=>{ee.success(`正在批量导出 ${U.value.length} 个商品...`)},b=()=>{ee.success("操作成功！"),A()},c=j=>{V.value.pageSize=j,V.value.currentPage=1,A()},L=j=>{V.value.currentPage=j,A()};return(j,d)=>{const m=y("el-table-column"),o=y("el-image"),v=y("el-table"),t=y("el-pagination"),w=fe("loading");return l(),n(K,null,[e("div",io,[d[20]||(d[20]=be('<div class="bg-gradient-to-r from-amber-50 to-yellow-50 dark:from-amber-900/20 dark:to-yellow-900/20 rounded-2xl p-6 border border-amber-100 dark:border-amber-800" data-v-5c32293d><div class="flex items-center space-x-3" data-v-5c32293d><div class="w-10 h-10 bg-gradient-to-br from-amber-500 to-amber-600 rounded-xl flex items-center justify-center" data-v-5c32293d><svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-5c32293d><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" data-v-5c32293d></path></svg></div><div data-v-5c32293d><h1 class="text-2xl font-bold text-gray-900 dark:text-dark-text" data-v-5c32293d>白品管理</h1><p class="mt-1 text-sm text-gray-600 dark:text-dark-text-secondary" data-v-5c32293d>管理您的基础商品信息和SKU</p></div></div></div>',1)),e("div",uo,[e("div",co,[e("div",mo,[e("div",null,[d[7]||(d[7]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"商品总数",-1)),e("p",po,x(R.value),1)]),d[8]||(d[8]=e("div",{class:"w-12 h-12 bg-amber-100 dark:bg-amber-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-amber-600 dark:text-amber-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"})])],-1))])]),e("div",go,[e("div",vo,[e("div",null,[d[9]||(d[9]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"SKU总数",-1)),e("p",ko,x(i.value),1)]),d[10]||(d[10]=e("div",{class:"w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-green-600 dark:text-green-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"})])],-1))])]),e("div",bo,[e("div",xo,[e("div",null,[d[11]||(d[11]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"今日新增",-1)),e("p",fo,x(s.value),1)]),d[12]||(d[12]=e("div",{class:"w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-blue-600 dark:text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})])],-1))])]),e("div",ho,[e("div",yo,[e("div",null,[d[13]||(d[13]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"平均价格",-1)),e("p",wo,"¥"+x(r.value),1)]),d[14]||(d[14]=e("div",{class:"w-12 h-12 bg-orange-100 dark:bg-orange-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-orange-600 dark:text-orange-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1))])])]),e("div",_o,[e("div",$o,[e("div",Vo,[e("button",{onClick:d[0]||(d[0]=p=>P.value=!0),class:"inline-flex items-center px-4 py-2.5 bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700 text-white font-medium rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"},d[15]||(d[15]=[e("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),$(" 创建商品 ")])),e("button",{onClick:H,class:"inline-flex items-center px-4 py-2.5 bg-white dark:bg-dark-card text-gray-700 dark:text-dark-text font-medium rounded-lg border border-gray-300 dark:border-dark-border hover:bg-gray-50 dark:hover:bg-dark-border shadow-sm hover:shadow-md transition-all duration-200"},d[16]||(d[16]=[e("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"})],-1),$(" 导出商品 ")])),U.value.length>0?(l(),n("div",Co,[e("span",Io," 已选择 "+x(U.value.length)+" 项 ",1),e("button",{onClick:J,class:"inline-flex items-center px-3 py-1.5 bg-amber-500 hover:bg-amber-600 text-white text-sm font-medium rounded-lg transition-all duration-200"},d[17]||(d[17]=[e("svg",{class:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"})],-1),$(" 批量导出 ")]))])):q("",!0)]),e("div",jo,[de(e("input",{"onUpdate:modelValue":d[1]||(d[1]=p=>M.value=p),type:"text",placeholder:"搜索商品名称、SKU...",class:"w-full sm:w-64 pl-10 pr-4 py-2 border border-gray-300 dark:border-dark-border rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500 dark:bg-dark-card dark:text-dark-text",onInput:_},null,544),[[xe,M.value]]),d[18]||(d[18]=e("svg",{class:"w-5 h-5 text-gray-400 absolute left-3 top-2.5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})],-1))])])]),e("div",Uo,[d[19]||(d[19]=e("div",{class:"px-6 py-4 border-b border-gray-100 dark:border-dark-border"},[e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-dark-text"},"商品列表"),e("p",{class:"text-sm text-gray-600 dark:text-dark-text-secondary mt-1"},"管理您的所有商品信息")],-1)),e("div",zo,[de((l(),te(v,{data:D.value,style:{width:"100%"},onSelectionChange:Q,class:"modern-table","header-cell-style":{backgroundColor:"var(--el-bg-color-page)",color:"var(--el-text-color-primary)",fontWeight:"600",borderBottom:"1px solid var(--el-border-color-light)"},"row-style":{backgroundColor:"transparent"}},{default:g(()=>[u(m,{type:"selection",width:"55"}),u(m,{label:"商品封面图",width:"100"},{default:g(p=>[e("div",Mo,[u(o,{src:p.row.coverImage,"preview-src-list":[p.row.coverImage],fit:"cover",class:"w-16 h-16 rounded-lg border border-gray-200 dark:border-dark-border","preview-teleported":!0},null,8,["src","preview-src-list"])])]),_:1}),u(m,{prop:"name",label:"商品名称","min-width":"200"},{default:g(p=>[e("div",So,x(p.row.name),1)]),_:1}),u(m,{prop:"price",label:"价格（最低）",width:"120"},{default:g(p=>[e("span",Lo," ¥"+x(p.row.minPrice),1)]),_:1}),u(m,{prop:"skuCount",label:"SKU数量",width:"100"},{default:g(p=>[e("span",Ko,x(p.row.skuCount),1)]),_:1}),u(m,{prop:"creator",label:"创建人",width:"100"},{default:g(p=>[e("div",Bo,[e("div",Do,[e("span",Ro,x(p.row.creator.charAt(0)),1)]),e("span",Ao,x(p.row.creator),1)])]),_:1}),u(m,{prop:"createTime",label:"创建时间",width:"180"},{default:g(p=>[e("div",Po,x(p.row.createTime),1)]),_:1}),u(m,{label:"操作",width:"180"},{default:g(p=>[e("div",Eo,[e("button",{onClick:I=>a(p.row),class:"inline-flex items-center px-3 py-1.5 text-sm font-medium text-amber-600 dark:text-amber-400 hover:text-amber-700 dark:hover:text-amber-300 bg-amber-50 dark:bg-amber-900/20 hover:bg-amber-100 dark:hover:bg-amber-900/30 rounded-lg transition-all duration-200"}," 查看详情 ",8,Ho),e("button",{onClick:I=>Y(p.row),class:"inline-flex items-center px-3 py-1.5 text-sm font-medium text-gray-600 dark:text-dark-text-secondary hover:text-gray-700 dark:hover:text-dark-text bg-gray-50 dark:bg-dark-card hover:bg-gray-100 dark:hover:bg-dark-border rounded-lg transition-all duration-200"}," 编辑 ",8,To)])]),_:1})]),_:1},8,["data"])),[[w,N.value]])]),e("div",Xo,[e("div",Yo," 共 "+x(V.value.total)+" 条记录 ",1),u(t,{"current-page":V.value.currentPage,"onUpdate:currentPage":d[2]||(d[2]=p=>V.value.currentPage=p),"page-size":V.value.pageSize,"onUpdate:pageSize":d[3]||(d[3]=p=>V.value.pageSize=p),"page-sizes":[10,20,50,100],total:V.value.total,layout:"sizes, prev, pager, next, jumper",onSizeChange:c,onCurrentChange:L,class:"modern-pagination"},null,8,["current-page","page-size","total"])])])]),u(Qt,{modelValue:P.value,"onUpdate:modelValue":d[4]||(d[4]=p=>P.value=p),onSuccess:b},null,8,["modelValue"]),u(Cr,{modelValue:B.value,"onUpdate:modelValue":d[5]||(d[5]=p=>B.value=p),product:C.value},null,8,["modelValue","product"]),u(no,{modelValue:z.value,"onUpdate:modelValue":d[6]||(d[6]=p=>z.value=p),product:C.value,onSuccess:b},null,8,["modelValue","product"])],64)}}}),qo=se(Fo,[["__scopeId","data-v-5c32293d"]]);export{qo as default};
