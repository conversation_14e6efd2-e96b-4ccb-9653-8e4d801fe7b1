import{c as o,a as e,o as t,d as u,e as p,F as h,k as g,g as k,h as c,q as d,j as x,C as _,s as v,t as b,n as f}from"./index-BTucjZQh.js";import{r as w}from"./CreditCardIcon-4VBkbhbU.js";import{r as y}from"./ClipboardDocumentListIcon-DTeQBIam.js";import{r as C}from"./UsersIcon-CeHGUE6D.js";import{r as B}from"./ShieldCheckIcon-DpAOpIr5.js";function $(n,a){return t(),o("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M15.75 9V5.25A2.25 2.25 0 0 0 13.5 3h-6a2.25 2.25 0 0 0-2.25 2.25v13.5A2.25 2.25 0 0 0 7.5 21h6a2.25 2.25 0 0 0 2.25-2.25V15m3 0 3-3m0 0-3-3m3 3H9"})])}function V(n,a){return t(),o("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z"})])}const j={class:"flex h-full bg-gray-50 dark:bg-dark-bg"},A={class:"w-64 bg-white dark:bg-dark-card shadow-sm border-r border-gray-200 dark:border-dark-border"},M={class:"p-6"},N={class:"space-y-2"},D={class:"flex-1 overflow-auto"},H=u({__name:"index",setup(n){const a=p(()=>[{name:"基本信息",path:"/account-settings/profile",icon:V},{name:"账号充值",path:"/account-settings/recharge",icon:w},{name:"交易记录",path:"/account-settings/transactions",icon:y},{name:"子账号管理",path:"/account-settings/sub-accounts",icon:C},{name:"权限设置",path:"/account-settings/permissions",icon:B},{name:"退出登录",path:"/account-settings/logout",icon:$}]);return(i,s)=>{const l=c("router-link"),m=c("router-view");return t(),o("div",j,[e("div",A,[e("div",M,[s[0]||(s[0]=e("h1",{class:"text-xl font-semibold text-gray-900 dark:text-dark-text mb-6"},"账号设置",-1)),e("nav",N,[(t(!0),o(h,null,g(a.value,r=>(t(),d(l,{key:r.path,to:r.path,class:f(["flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors",[i.$route.path===r.path?"bg-amber-50 dark:bg-amber-900/20 text-amber-700 dark:text-amber-400":"text-gray-600 dark:text-dark-text-secondary hover:text-gray-900 dark:hover:text-dark-text hover:bg-gray-50 dark:hover:bg-dark-border"]])},{default:x(()=>[(t(),d(v(r.icon),{class:"w-5 h-5 mr-3"})),_(" "+b(r.name),1)]),_:2},1032,["to","class"]))),128))])])]),e("div",D,[k(m)])])}}});export{H as default};
