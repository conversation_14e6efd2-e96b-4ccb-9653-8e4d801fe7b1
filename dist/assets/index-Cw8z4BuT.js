import{d as be,r as u,K as A,f as ye,c as n,a,g as l,C as x,A as Q,R as _e,F as _,k,t as b,j as r,h as g,E as V,n as ke,M as B,w as W,q as O,Y as he,o as d}from"./index-BTucjZQh.js";import{r as we}from"./ShieldCheckIcon-DpAOpIr5.js";const Ve={class:"p-8"},Ce={class:"flex items-center justify-between mb-8"},Re={class:"grid grid-cols-1 lg:grid-cols-3 gap-8"},Ue={class:"lg:col-span-1"},Pe={class:"bg-white dark:bg-dark-card rounded-lg p-6 shadow-sm"},Ee={class:"space-y-3"},Me=["onClick"],Se={class:"flex items-center justify-between"},Fe={class:"font-medium text-gray-900 dark:text-dark-text"},je={class:"text-sm text-gray-500 dark:text-dark-text-secondary"},Te={class:"text-xs text-gray-400 mt-1"},$e={class:"flex space-x-2"},Be=["onClick"],De=["onClick"],qe={class:"lg:col-span-2"},Ie={key:0,class:"bg-white dark:bg-dark-card rounded-lg p-6 shadow-sm"},Ne={class:"flex items-center justify-between mb-6"},Ae={class:"text-lg font-medium text-gray-900 dark:text-dark-text"},Oe=["loading"],Le={class:"space-y-6"},ze={class:"flex items-center justify-between mb-4"},Ke={class:"flex items-center"},Ye={class:"ml-3 font-medium text-gray-900 dark:text-dark-text"},Ge={class:"grid grid-cols-2 md:grid-cols-3 gap-3 ml-6"},He={class:"text-gray-700 dark:text-dark-text-secondary"},Je={class:"space-y-4"},Qe={class:"flex items-center justify-between mb-3"},We={class:"flex items-center"},Xe={class:"ml-3 font-medium text-gray-900 dark:text-dark-text"},Ze={key:0,class:"ml-6 space-y-2"},et={class:"text-gray-700 dark:text-dark-text-secondary"},tt={class:"space-y-6"},at={class:"border border-gray-200 dark:border-dark-border rounded-lg p-4"},ot={class:"flex flex-col space-y-3"},st={key:0,class:"border border-gray-200 dark:border-dark-border rounded-lg p-4"},lt={class:"grid grid-cols-2 gap-4"},rt={class:"border border-gray-200 dark:border-dark-border rounded-lg p-4"},dt={class:"grid grid-cols-2 md:grid-cols-4 gap-3"},nt={class:"text-gray-700 dark:text-dark-text-secondary"},it={key:1,class:"bg-white dark:bg-dark-card rounded-lg p-12 shadow-sm text-center"},ut={class:"flex justify-end space-x-3"},ct={class:"flex justify-end space-x-3"},vt=be({__name:"index",setup(mt){const D=u(!1),q=u(!1),I=u(!1),P=u(!1),E=u(!1),f=u(null),C=u(null),L=u("function"),S=u(),F=u(),h=A({name:"",description:""}),v=A({name:"",description:""}),R=u({}),y=u({}),c=A({scope:"department",departments:[],users:[],operations:{}}),w=u([{id:"admin",name:"超级管理员",description:"拥有所有权限",userCount:1,isSystem:!0},{id:"manager",name:"管理员",description:"管理权限",userCount:3,isSystem:!1},{id:"operator",name:"操作员",description:"基本操作权限",userCount:8,isSystem:!1},{id:"viewer",name:"查看员",description:"只读权限",userCount:12,isSystem:!1}]),z=u([{id:"product-collection",name:"商品采集",permissions:[{id:"product-collection:create",name:"创建采集任务",code:"product-collection:create"},{id:"product-collection:view",name:"查看采集任务",code:"product-collection:view"},{id:"product-collection:export",name:"导出采集数据",code:"product-collection:export"},{id:"product-collection:delete",name:"删除采集任务",code:"product-collection:delete"}]},{id:"smart-crop",name:"智能裁图",permissions:[{id:"smart-crop:create",name:"创建裁图任务",code:"smart-crop:create"},{id:"smart-crop:view",name:"查看裁图任务",code:"smart-crop:view"},{id:"smart-crop:download",name:"下载裁图结果",code:"smart-crop:download"},{id:"smart-crop:delete",name:"删除裁图任务",code:"smart-crop:delete"}]},{id:"gallery",name:"图库管理",permissions:[{id:"gallery:upload",name:"上传图片",code:"gallery:upload"},{id:"gallery:view",name:"查看图库",code:"gallery:view"},{id:"gallery:download",name:"下载图片",code:"gallery:download"},{id:"gallery:delete",name:"删除图片",code:"gallery:delete"},{id:"gallery:manage",name:"图库管理",code:"gallery:manage"}]},{id:"products",name:"商品管理",permissions:[{id:"products:create",name:"创建商品",code:"products:create"},{id:"products:view",name:"查看商品",code:"products:view"},{id:"products:edit",name:"编辑商品",code:"products:edit"},{id:"products:delete",name:"删除商品",code:"products:delete"},{id:"products:export",name:"导出商品",code:"products:export"}]},{id:"account",name:"账号管理",permissions:[{id:"account:sub-create",name:"创建子账号",code:"account:sub-create"},{id:"account:sub-view",name:"查看子账号",code:"account:sub-view"},{id:"account:sub-edit",name:"编辑子账号",code:"account:sub-edit"},{id:"account:sub-delete",name:"删除子账号",code:"account:sub-delete"},{id:"account:permission",name:"权限管理",code:"account:permission"}]}]),K=u([{id:"dashboard",name:"驾驶舱",path:"/dashboard"},{id:"my-apps",name:"我的应用",children:[{id:"product-collection-menu",name:"商品采集",path:"/my-apps/product-collection"},{id:"smart-crop-menu",name:"智能裁图",path:"/my-apps/smart-crop"},{id:"one-click-cutout-menu",name:"一键抠图",path:"/my-apps/one-click-cutout"},{id:"super-split-menu",name:"超级裂变",path:"/my-apps/super-split"},{id:"title-generator-menu",name:"标题生成",path:"/my-apps/title-generator"},{id:"batch-listing-menu",name:"批量刊登",path:"/my-apps/batch-listing"}]},{id:"workflows",name:"工作流",path:"/workflows"},{id:"gallery-menu",name:"图库管理",children:[{id:"gallery-overview-menu",name:"图库概览",path:"/gallery/overview"},{id:"product-gallery-menu",name:"商品图库",path:"/gallery/products"},{id:"material-gallery-menu",name:"素材图库",path:"/gallery/materials"},{id:"background-gallery-menu",name:"背景图库",path:"/gallery/backgrounds"},{id:"result-gallery-menu",name:"处理结果",path:"/gallery/results"}]},{id:"products-menu",name:"商品管理",children:[{id:"basic-products-menu",name:"白品管理",path:"/products/basic"},{id:"pod-products-menu",name:"POD商品",path:"/products/pod"}]},{id:"app-market-menu",name:"应用市场",path:"/app-market"},{id:"account-settings-menu",name:"账号设置",children:[{id:"account-profile-menu",name:"基本信息",path:"/account-settings/profile"},{id:"account-recharge-menu",name:"账号充值",path:"/account-settings/recharge"},{id:"account-transactions-menu",name:"交易记录",path:"/account-settings/transactions"},{id:"sub-accounts-menu",name:"子账号管理",path:"/account-settings/sub-accounts"},{id:"permissions-menu",name:"权限设置",path:"/account-settings/permissions"}]}]),X=u([{id:"tech",name:"技术部"},{id:"product",name:"产品部"},{id:"operation",name:"运营部"},{id:"sales",name:"销售部"}]),Z=u([{id:"user1",name:"张三"},{id:"user2",name:"李四"},{id:"user3",name:"王五"},{id:"user4",name:"赵六"}]),N=u([{id:"create",name:"创建",code:"data:create"},{id:"read",name:"查看",code:"data:read"},{id:"update",name:"编辑",code:"data:update"},{id:"delete",name:"删除",code:"data:delete"},{id:"export",name:"导出",code:"data:export"},{id:"import",name:"导入",code:"data:import"}]),ee={name:[{required:!0,message:"请输入权限组名称",trigger:"blur"},{min:2,max:20,message:"名称长度在 2 到 20 个字符",trigger:"blur"}],description:[{required:!0,message:"请输入权限组描述",trigger:"blur"}]},te={name:[{required:!0,message:"请输入权限组名称",trigger:"blur"},{min:2,max:20,message:"名称长度在 2 到 20 个字符",trigger:"blur"}],description:[{required:!0,message:"请输入权限组描述",trigger:"blur"}]},ae=o=>o.permissions.every(e=>R.value[e.id]),oe=o=>{const e=o.permissions.filter(s=>R.value[s.id]).length;return e>0&&e<o.permissions.length},se=o=>o.children?o.children.every(e=>y.value[e.id]):y.value[o.id],le=o=>{if(!o.children)return!1;const e=o.children.filter(s=>y.value[s.id]).length;return e>0&&e<o.children.length},re=o=>{f.value=o,de(o)},de=o=>{const e={},s={},m={};o.id==="admin"?(z.value.forEach(p=>{p.permissions.forEach(M=>{e[M.id]=!0})}),K.value.forEach(p=>{s[p.id]=!0,p.children&&p.children.forEach(M=>{s[M.id]=!0})}),N.value.forEach(p=>{m[p.id]=!0}),c.scope="all"):o.id==="operator"?(e["product-collection:create"]=!0,e["product-collection:view"]=!0,e["smart-crop:create"]=!0,e["smart-crop:view"]=!0,s.dashboard=!0,s["my-apps"]=!0,s["product-collection-menu"]=!0,s["smart-crop-menu"]=!0,m.create=!0,m.read=!0,c.scope="self"):o.id==="viewer"&&(e["product-collection:view"]=!0,e["smart-crop:view"]=!0,s.dashboard=!0,s["my-apps"]=!0,s["product-collection-menu"]=!0,s["smart-crop-menu"]=!0,m.read=!0,c.scope="self"),R.value=e,y.value=s,c.operations=m},ne=(o,e)=>{o.permissions.forEach(s=>{R.value[s.id]=e})},ie=(o,e)=>{y.value[o.id]=e,o.children&&o.children.forEach(s=>{y.value[s.id]=e})},ue=()=>{},ce=()=>{},me=async()=>{if(f.value){D.value=!0;try{await new Promise(o=>setTimeout(o,1e3)),V.success("权限保存成功")}catch{V.error("权限保存失败")}finally{D.value=!1}}},pe=o=>{C.value=o,v.name=o.name,v.description=o.description,E.value=!0},ge=async o=>{var e;try{await he.confirm(`确定要删除权限组 "${o.name}" 吗？此操作不可恢复。`,"确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"error"}),await new Promise(m=>setTimeout(m,500));const s=w.value.findIndex(m=>m.id===o.id);s>-1&&w.value.splice(s,1),((e=f.value)==null?void 0:e.id)===o.id&&(f.value=null),V.success("删除成功")}catch{}},ve=async()=>{if(S.value)try{await S.value.validate(),q.value=!0,await new Promise(e=>setTimeout(e,1e3));const o={id:`role_${Date.now()}`,name:h.name,description:h.description,userCount:0,isSystem:!1};w.value.push(o),S.value.resetFields(),P.value=!1,V.success("权限组创建成功")}catch{V.error("创建失败，请检查输入信息")}finally{q.value=!1}},fe=async()=>{var o;if(!(!F.value||!C.value))try{await F.value.validate(),I.value=!0,await new Promise(s=>setTimeout(s,1e3));const e=w.value.findIndex(s=>s.id===C.value.id);e>-1&&(w.value[e].name=v.name,w.value[e].description=v.description),((o=f.value)==null?void 0:o.id)===C.value.id&&(f.value.name=v.name,f.value.description=v.description),F.value.resetFields(),E.value=!1,C.value=null,V.success("权限组更新成功")}catch{V.error("更新失败，请检查输入信息")}finally{I.value=!1}};return ye(()=>{N.value.forEach(o=>{c.operations[o.id]=!1})}),(o,e)=>{const s=g("el-checkbox"),m=g("el-tab-pane"),p=g("el-radio"),M=g("el-radio-group"),Y=g("el-option"),G=g("el-select"),xe=g("el-tabs"),j=g("el-input"),T=g("el-form-item"),H=g("el-form"),$=g("el-button"),J=g("el-dialog");return d(),n("div",Ve,[a("div",Ce,[e[14]||(e[14]=a("div",null,[a("h2",{class:"text-2xl font-bold text-gray-900 dark:text-dark-text"},"权限设置"),a("p",{class:"mt-2 text-sm text-gray-600 dark:text-dark-text-secondary"},"管理权限组和权限分配")],-1)),a("button",{onClick:e[0]||(e[0]=t=>P.value=!0),class:"inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-amber-500 hover:bg-amber-600 rounded-lg transition-colors"},[l(Q(_e),{class:"w-4 h-4 mr-2"}),e[13]||(e[13]=x(" 创建权限组 "))])]),a("div",Re,[a("div",Ue,[a("div",Pe,[e[15]||(e[15]=a("h3",{class:"text-lg font-medium text-gray-900 dark:text-dark-text mb-4"},"权限组",-1)),a("div",Ee,[(d(!0),n(_,null,k(w.value,t=>{var i;return d(),n("div",{key:t.id,onClick:U=>re(t),class:ke(["p-4 border rounded-lg cursor-pointer transition-all duration-200",[((i=f.value)==null?void 0:i.id)===t.id?"border-amber-500 bg-amber-50 dark:bg-amber-900/20":"border-gray-200 dark:border-dark-border hover:border-amber-300 dark:hover:border-amber-600"]])},[a("div",Se,[a("div",null,[a("h4",Fe,b(t.name),1),a("p",je,b(t.description),1),a("p",Te,b(t.userCount)+" 个用户",1)]),a("div",$e,[a("button",{onClick:W(U=>pe(t),["stop"]),class:"text-sm text-amber-600 dark:text-amber-400 hover:text-amber-700 dark:hover:text-amber-300"}," 编辑 ",8,Be),t.isSystem?B("",!0):(d(),n("button",{key:0,onClick:W(U=>ge(t),["stop"]),class:"text-sm text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300"}," 删除 ",8,De))])])],10,Me)}),128))])])]),a("div",qe,[f.value?(d(),n("div",Ie,[a("div",Ne,[a("h3",Ae,b(f.value.name)+" - 权限配置 ",1),a("button",{onClick:me,loading:D.value,class:"px-4 py-2 text-sm font-medium text-white bg-amber-500 hover:bg-amber-600 rounded-lg transition-colors"}," 保存权限 ",8,Oe)]),l(xe,{modelValue:L.value,"onUpdate:modelValue":e[4]||(e[4]=t=>L.value=t),class:"permission-tabs"},{default:r(()=>[l(m,{label:"功能权限",name:"function"},{default:r(()=>[a("div",Le,[(d(!0),n(_,null,k(z.value,t=>(d(),n("div",{key:t.id,class:"border border-gray-200 dark:border-dark-border rounded-lg p-4"},[a("div",ze,[a("div",Ke,[l(s,{"model-value":ae(t),indeterminate:oe(t),onChange:i=>ne(t,i)},null,8,["model-value","indeterminate","onChange"]),a("h4",Ye,b(t.name),1)])]),a("div",Ge,[(d(!0),n(_,null,k(t.permissions,i=>(d(),n("label",{key:i.id,class:"flex items-center space-x-2 text-sm"},[l(s,{modelValue:R.value[i.id],"onUpdate:modelValue":U=>R.value[i.id]=U,onChange:ue},null,8,["modelValue","onUpdate:modelValue"]),a("span",He,b(i.name),1)]))),128))])]))),128))])]),_:1}),l(m,{label:"菜单权限",name:"menu"},{default:r(()=>[a("div",Je,[(d(!0),n(_,null,k(K.value,t=>(d(),n("div",{key:t.id,class:"border border-gray-200 dark:border-dark-border rounded-lg p-4"},[a("div",Qe,[a("div",We,[l(s,{"model-value":se(t),indeterminate:le(t),onChange:i=>ie(t,i)},null,8,["model-value","indeterminate","onChange"]),a("h4",Xe,b(t.name),1)])]),t.children?(d(),n("div",Ze,[(d(!0),n(_,null,k(t.children,i=>(d(),n("label",{key:i.id,class:"flex items-center space-x-2 text-sm"},[l(s,{modelValue:y.value[i.id],"onUpdate:modelValue":U=>y.value[i.id]=U,onChange:ce},null,8,["modelValue","onUpdate:modelValue"]),a("span",et,b(i.name),1)]))),128))])):B("",!0)]))),128))])]),_:1}),l(m,{label:"数据权限",name:"data"},{default:r(()=>[a("div",tt,[a("div",at,[e[20]||(e[20]=a("h4",{class:"font-medium text-gray-900 dark:text-dark-text mb-4"},"数据范围",-1)),l(M,{modelValue:c.scope,"onUpdate:modelValue":e[1]||(e[1]=t=>c.scope=t),class:"space-y-3"},{default:r(()=>[a("div",ot,[l(p,{label:"all"},{default:r(()=>e[16]||(e[16]=[x("全部数据")])),_:1,__:[16]}),l(p,{label:"department"},{default:r(()=>e[17]||(e[17]=[x("本部门数据")])),_:1,__:[17]}),l(p,{label:"self"},{default:r(()=>e[18]||(e[18]=[x("仅本人数据")])),_:1,__:[18]}),l(p,{label:"custom"},{default:r(()=>e[19]||(e[19]=[x("自定义数据范围")])),_:1,__:[19]})])]),_:1},8,["modelValue"])]),c.scope==="custom"?(d(),n("div",st,[e[23]||(e[23]=a("h4",{class:"font-medium text-gray-900 dark:text-dark-text mb-4"},"自定义范围",-1)),a("div",lt,[a("div",null,[e[21]||(e[21]=a("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text-secondary mb-2"}," 可访问部门 ",-1)),l(G,{modelValue:c.departments,"onUpdate:modelValue":e[2]||(e[2]=t=>c.departments=t),multiple:"",placeholder:"选择部门",class:"w-full"},{default:r(()=>[(d(!0),n(_,null,k(X.value,t=>(d(),O(Y,{key:t.id,label:t.name,value:t.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),a("div",null,[e[22]||(e[22]=a("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text-secondary mb-2"}," 可访问用户 ",-1)),l(G,{modelValue:c.users,"onUpdate:modelValue":e[3]||(e[3]=t=>c.users=t),multiple:"",placeholder:"选择用户",class:"w-full"},{default:r(()=>[(d(!0),n(_,null,k(Z.value,t=>(d(),O(Y,{key:t.id,label:t.name,value:t.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])])])])):B("",!0),a("div",rt,[e[24]||(e[24]=a("h4",{class:"font-medium text-gray-900 dark:text-dark-text mb-4"},"操作权限",-1)),a("div",dt,[(d(!0),n(_,null,k(N.value,t=>(d(),n("label",{key:t.id,class:"flex items-center space-x-2 text-sm"},[l(s,{modelValue:c.operations[t.id],"onUpdate:modelValue":i=>c.operations[t.id]=i},null,8,["modelValue","onUpdate:modelValue"]),a("span",nt,b(t.name),1)]))),128))])])])]),_:1})]),_:1},8,["modelValue"])])):(d(),n("div",it,[l(Q(we),{class:"w-16 h-16 text-gray-400 mx-auto mb-4"}),e[25]||(e[25]=a("h3",{class:"text-lg font-medium text-gray-900 dark:text-dark-text mb-2"},"选择权限组",-1)),e[26]||(e[26]=a("p",{class:"text-gray-500 dark:text-dark-text-secondary"},"请从左侧选择一个权限组来配置权限",-1))]))])]),l(J,{modelValue:P.value,"onUpdate:modelValue":e[8]||(e[8]=t=>P.value=t),title:"创建权限组",width:"500px"},{footer:r(()=>[a("div",ut,[l($,{onClick:e[7]||(e[7]=t=>P.value=!1)},{default:r(()=>e[27]||(e[27]=[x("取消")])),_:1,__:[27]}),l($,{type:"primary",onClick:ve,loading:q.value},{default:r(()=>e[28]||(e[28]=[x(" 创建 ")])),_:1,__:[28]},8,["loading"])])]),default:r(()=>[l(H,{ref_key:"createRoleFormRef",ref:S,model:h,rules:ee,"label-width":"100px"},{default:r(()=>[l(T,{label:"权限组名称",prop:"name"},{default:r(()=>[l(j,{modelValue:h.name,"onUpdate:modelValue":e[5]||(e[5]=t=>h.name=t),placeholder:"请输入权限组名称"},null,8,["modelValue"])]),_:1}),l(T,{label:"描述",prop:"description"},{default:r(()=>[l(j,{modelValue:h.description,"onUpdate:modelValue":e[6]||(e[6]=t=>h.description=t),type:"textarea",rows:3,placeholder:"请输入权限组描述"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),l(J,{modelValue:E.value,"onUpdate:modelValue":e[12]||(e[12]=t=>E.value=t),title:"编辑权限组",width:"500px"},{footer:r(()=>[a("div",ct,[l($,{onClick:e[11]||(e[11]=t=>E.value=!1)},{default:r(()=>e[29]||(e[29]=[x("取消")])),_:1,__:[29]}),l($,{type:"primary",onClick:fe,loading:I.value},{default:r(()=>e[30]||(e[30]=[x(" 保存 ")])),_:1,__:[30]},8,["loading"])])]),default:r(()=>[C.value?(d(),O(H,{key:0,ref_key:"editRoleFormRef",ref:F,model:v,rules:te,"label-width":"100px"},{default:r(()=>[l(T,{label:"权限组名称",prop:"name"},{default:r(()=>[l(j,{modelValue:v.name,"onUpdate:modelValue":e[9]||(e[9]=t=>v.name=t),placeholder:"请输入权限组名称"},null,8,["modelValue"])]),_:1}),l(T,{label:"描述",prop:"description"},{default:r(()=>[l(j,{modelValue:v.description,"onUpdate:modelValue":e[10]||(e[10]=t=>v.description=t),type:"textarea",rows:3,placeholder:"请输入权限组描述"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])):B("",!0)]),_:1},8,["modelValue"])])}}});export{vt as default};
