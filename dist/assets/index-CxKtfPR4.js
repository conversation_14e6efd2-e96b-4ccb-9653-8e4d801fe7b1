import{d as X,r as s,e as z,c as g,a as t,g as i,p as Y,t as d,M as _,j as y,h,C as V,F as I,k as Z,E as C,n as L,w as S,o as p,_ as ee}from"./index-BTucjZQh.js";import{I as te}from"./ImagePreviewDialog-BSZO2EZv.js";const oe={class:"space-y-6"},re={class:"grid grid-cols-1 md:grid-cols-4 gap-6"},se={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},ae={class:"flex items-center justify-between"},le={class:"text-2xl font-bold text-gray-900 dark:text-dark-text"},de={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},ne={class:"flex items-center justify-between"},ie={class:"text-2xl font-bold text-green-600 dark:text-green-400"},ue={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},ce={class:"flex items-center justify-between"},ge={class:"text-2xl font-bold text-purple-600 dark:text-purple-400"},pe={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},me={class:"flex items-center justify-between"},he={class:"text-2xl font-bold text-orange-600 dark:text-orange-400"},ve={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border"},xe={class:"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0"},ke={class:"flex items-center space-x-3"},be={key:0,class:"flex items-center space-x-2 ml-4 pl-4 border-l border-gray-200 dark:border-dark-border"},fe={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},we={class:"flex items-center space-x-3"},_e={class:"bg-white dark:bg-dark-surface rounded-xl shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border overflow-hidden"},ye={class:"px-6 py-4 border-b border-gray-100 dark:border-dark-border"},Ce={class:"text-sm text-gray-600 dark:text-dark-text-secondary mt-1"},je={class:"p-6"},Me={class:"grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4"},Be=["onClick"],ze={key:0,class:"absolute top-2 right-2 w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center z-10"},Ve={class:"relative"},Ie=["src","alt"],Le={class:"absolute top-2 left-2"},Se={class:"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 rounded-lg transition-all duration-200 flex items-center justify-center"},Te={class:"opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex space-x-2"},Ue=["onClick"],Fe=["onClick"],De={class:"mt-2"},Ke=["title"],Ne={class:"text-xs text-gray-500 dark:text-dark-text-secondary"},$e={key:0,class:"flex justify-center mt-6"},Ee=X({__name:"index",setup(He){const T=s(),U=s([]),l=s([]),v=s(""),x=s(""),k=s(1),m=s(24),w=s(!1),j=s(null),F=s(456),D=s(128),K=s(234),N=s(94),u=s([{id:"img_001",name:"商品主图_T恤_001.jpg",thumbnail:"https://picsum.photos/300/300?random=1",url:"https://picsum.photos/800/800?random=1",category:"main",size:"1.2MB",uploadTime:"2024-01-15 14:30",tags:["T恤","服装","主图"]},{id:"img_002",name:"商品详情图_T恤_001.jpg",thumbnail:"https://picsum.photos/300/300?random=2",url:"https://picsum.photos/800/800?random=2",category:"detail",size:"2.1MB",uploadTime:"2024-01-15 14:25",tags:["T恤","服装","详情图"]},{id:"img_003",name:"SKU图_T恤_红色_S.jpg",thumbnail:"https://picsum.photos/300/300?random=3",url:"https://picsum.photos/800/800?random=3",category:"sku",size:"0.8MB",uploadTime:"2024-01-15 14:20",tags:["T恤","红色","S码"]},{id:"img_004",name:"商品主图_马克杯_001.jpg",thumbnail:"https://picsum.photos/300/300?random=4",url:"https://picsum.photos/800/800?random=4",category:"main",size:"1.5MB",uploadTime:"2024-01-15 13:45",tags:["马克杯","家居","主图"]},{id:"img_005",name:"商品详情图_马克杯_001.jpg",thumbnail:"https://picsum.photos/300/300?random=5",url:"https://picsum.photos/800/800?random=5",category:"detail",size:"1.8MB",uploadTime:"2024-01-15 13:40",tags:["马克杯","家居","详情图"]},{id:"img_006",name:"SKU图_马克杯_白色.jpg",thumbnail:"https://picsum.photos/300/300?random=6",url:"https://picsum.photos/800/800?random=6",category:"sku",size:"0.9MB",uploadTime:"2024-01-15 13:35",tags:["马克杯","白色"]}]),b=z(()=>{let o=u.value;if(x.value&&(o=o.filter(e=>e.category===x.value)),v.value){const e=v.value.toLowerCase();o=o.filter(a=>{var n;return a.name.toLowerCase().includes(e)||((n=a.tags)==null?void 0:n.some(c=>c.toLowerCase().includes(e)))})}return o}),$=z(()=>{const o=(k.value-1)*m.value,e=o+m.value;return b.value.slice(o,e)}),E=o=>{if(o.raw){const e=new FileReader;e.onload=a=>{var c,f;const n={id:"img_"+Date.now(),name:o.name,thumbnail:(c=a.target)==null?void 0:c.result,url:(f=a.target)==null?void 0:f.result,category:"main",size:P(o.size||0),uploadTime:new Date().toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"}),tags:[]};u.value.unshift(n),C.success(`图片 ${o.name} 上传成功`)},e.readAsDataURL(o.raw)}},H=o=>{const e=u.value.findIndex(a=>a.name===o.name);e>-1&&u.value.splice(e,1)},P=o=>o<1024?o+" B":o<1024*1024?(o/1024).toFixed(1)+" KB":(o/(1024*1024)).toFixed(1)+" MB",M=o=>l.value.some(e=>e.id===o.id),R=o=>{const e=l.value.findIndex(a=>a.id===o.id);e>-1?l.value.splice(e,1):l.value.push(o)},A=o=>({main:"bg-blue-100 text-blue-800",detail:"bg-green-100 text-green-800",sku:"bg-purple-100 text-purple-800"})[o]||"bg-gray-100 text-gray-800",q=o=>({main:"主图",detail:"详情图",sku:"SKU图"})[o]||"未知",G=o=>{j.value=o,w.value=!0},J=o=>{const e=document.createElement("a");e.href=o.url,e.download=o.name,document.body.appendChild(e),e.click(),document.body.removeChild(e),C.success(`正在下载 ${o.name}`)},O=()=>{l.value.length!==0&&(l.value.forEach(o=>{const e=u.value.findIndex(a=>a.id===o.id);e>-1&&u.value.splice(e,1)}),C.success(`已删除 ${l.value.length} 张图片`),l.value=[])},Q=()=>{k.value=1};return(o,e)=>{const a=h("el-upload"),n=h("el-option"),c=h("el-select"),f=h("el-input"),W=h("el-pagination");return p(),g(I,null,[t("div",oe,[e[20]||(e[20]=Y('<div class="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-2xl p-6 border border-blue-100 dark:border-blue-800" data-v-8ce4e857><div class="flex items-center space-x-3" data-v-8ce4e857><div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center" data-v-8ce4e857><svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-8ce4e857><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" data-v-8ce4e857></path></svg></div><div data-v-8ce4e857><h1 class="text-2xl font-bold text-gray-900 dark:text-dark-text" data-v-8ce4e857>商品图库</h1><p class="mt-1 text-sm text-gray-600 dark:text-dark-text-secondary" data-v-8ce4e857>管理商品相关的图片资源</p></div></div></div>',1)),t("div",re,[t("div",se,[t("div",ae,[t("div",null,[e[5]||(e[5]=t("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"商品图片总数",-1)),t("p",le,d(F.value),1)]),e[6]||(e[6]=t("div",{class:"w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center"},[t("svg",{class:"w-6 h-6 text-blue-600 dark:text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})])],-1))])]),t("div",de,[t("div",ne,[t("div",null,[e[7]||(e[7]=t("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"主图数量",-1)),t("p",ie,d(D.value),1)]),e[8]||(e[8]=t("div",{class:"w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center"},[t("svg",{class:"w-6 h-6 text-green-600 dark:text-green-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"})])],-1))])]),t("div",ue,[t("div",ce,[t("div",null,[e[9]||(e[9]=t("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"详情图数量",-1)),t("p",ge,d(K.value),1)]),e[10]||(e[10]=t("div",{class:"w-12 h-12 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center"},[t("svg",{class:"w-6 h-6 text-purple-600 dark:text-purple-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"})])],-1))])]),t("div",pe,[t("div",me,[t("div",null,[e[11]||(e[11]=t("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"SKU图数量",-1)),t("p",he,d(N.value),1)]),e[12]||(e[12]=t("div",{class:"w-12 h-12 bg-orange-100 dark:bg-orange-900/30 rounded-lg flex items-center justify-center"},[t("svg",{class:"w-6 h-6 text-orange-600 dark:text-orange-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"})])],-1))])])]),t("div",ve,[t("div",xe,[t("div",ke,[i(a,{ref_key:"uploadRef",ref:T,"file-list":U.value,"on-change":E,"on-remove":H,"auto-upload":!1,accept:"image/*",multiple:"","show-file-list":!1},{default:y(()=>e[13]||(e[13]=[t("button",{class:"inline-flex items-center px-4 py-2.5 bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white font-medium rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"},[t("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"})]),V(" 上传图片 ")],-1)])),_:1,__:[13]},8,["file-list"]),l.value.length>0?(p(),g("div",be,[t("span",fe," 已选择 "+d(l.value.length)+" 张图片 ",1),t("button",{onClick:O,class:"inline-flex items-center px-3 py-1.5 bg-red-500 hover:bg-red-600 text-white text-sm font-medium rounded-lg transition-all duration-200"},e[14]||(e[14]=[t("svg",{class:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})],-1),V(" 批量删除 ")]))])):_("",!0)]),t("div",we,[i(c,{modelValue:x.value,"onUpdate:modelValue":e[0]||(e[0]=r=>x.value=r),placeholder:"图片分类",style:{width:"120px"},clearable:""},{default:y(()=>[i(n,{label:"全部",value:""}),i(n,{label:"主图",value:"main"}),i(n,{label:"详情图",value:"detail"}),i(n,{label:"SKU图",value:"sku"})]),_:1},8,["modelValue"]),i(f,{modelValue:v.value,"onUpdate:modelValue":e[1]||(e[1]=r=>v.value=r),placeholder:"搜索图片...",style:{width:"200px"},clearable:"",onInput:Q},{prefix:y(()=>e[15]||(e[15]=[t("svg",{class:"w-4 h-4 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})],-1)])),_:1},8,["modelValue"])])])]),t("div",_e,[t("div",ye,[e[16]||(e[16]=t("h3",{class:"text-lg font-semibold text-gray-900 dark:text-dark-text"},"商品图片",-1)),t("p",Ce,"共 "+d(b.value.length)+" 张图片",1)]),t("div",je,[t("div",Me,[(p(!0),g(I,null,Z($.value,r=>(p(),g("div",{key:r.id,class:"group relative cursor-pointer",onClick:B=>R(r)},[M(r)?(p(),g("div",ze,e[17]||(e[17]=[t("svg",{class:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 13l4 4L19 7"})],-1)]))):_("",!0),t("div",Ve,[t("img",{src:r.thumbnail,alt:r.name,class:L(["w-full h-32 object-cover rounded-lg border-2 transition-all duration-200",M(r)?"border-blue-500":"border-gray-200 dark:border-dark-border group-hover:border-blue-300"])},null,10,Ie),t("div",Le,[t("span",{class:L([A(r.category),"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"])},d(q(r.category)),3)]),t("div",Se,[t("div",Te,[t("button",{onClick:S(B=>G(r),["stop"]),class:"p-2 bg-white rounded-full shadow-lg hover:shadow-xl transition-all duration-200"},e[18]||(e[18]=[t("svg",{class:"w-4 h-4 text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"}),t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"})],-1)]),8,Ue),t("button",{onClick:S(B=>J(r),["stop"]),class:"p-2 bg-white rounded-full shadow-lg hover:shadow-xl transition-all duration-200"},e[19]||(e[19]=[t("svg",{class:"w-4 h-4 text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"})],-1)]),8,Fe)])])]),t("div",De,[t("p",{class:"text-sm font-medium text-gray-900 dark:text-dark-text truncate",title:r.name},d(r.name),9,Ke),t("p",Ne,d(r.size)+" • "+d(r.uploadTime),1)])],8,Be))),128))]),b.value.length>m.value?(p(),g("div",$e,[i(W,{"current-page":k.value,"onUpdate:currentPage":e[2]||(e[2]=r=>k.value=r),"page-size":m.value,"onUpdate:pageSize":e[3]||(e[3]=r=>m.value=r),"page-sizes":[24,48,96],total:b.value.length,layout:"sizes, prev, pager, next",class:"modern-pagination"},null,8,["current-page","page-size","total"])])):_("",!0)])])]),i(te,{modelValue:w.value,"onUpdate:modelValue":e[4]||(e[4]=r=>w.value=r),image:j.value},null,8,["modelValue","image"])],64)}}}),Ae=ee(Ee,[["__scopeId","data-v-8ce4e857"]]);export{Ae as default};
