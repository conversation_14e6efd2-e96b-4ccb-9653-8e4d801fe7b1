import{K as ke,d as re,r as h,L as J,e as O,c as x,F as Z,g as s,h as y,j as l,a as e,M,C as _,k as ie,t as d,A as F,E as D,o as g,_ as ae,q as le,b as ue,Q as ce,f as xe,p as pe,G as ve,l as be,n as de}from"./index-BTucjZQh.js";import{_ as ye}from"./GallerySelectDialog.vue_vue_type_script_setup_true_lang-CrzPKmtJ.js";import{g as fe,c as he,a as _e}from"./billing-DsHNwwW1.js";import{r as ne}from"./ArrowDownTrayIcon-B03b1yfF.js";import"./billing-Ciq-WFQK.js";var G=(b=>(b.NO_RISK="no_risk",b.LOW_RISK="low_risk",b.MEDIUM_RISK="medium_risk",b.HIGH_RISK="high_risk",b))(G||{}),j=(b=>(b.PENDING="pending",b.PROCESSING="processing",b.COMPLETED="completed",b.FAILED="failed",b))(j||{});const ee=ke({tasks:[{id:"CD001",imageCount:25,completedCount:25,status:"completed",operator:"admin",createTime:"2024-01-15 14:30:00",riskSummary:{noRisk:15,lowRisk:6,mediumRisk:3,highRisk:1}},{id:"CD002",imageCount:18,completedCount:12,status:"processing",operator:"admin",createTime:"2024-01-15 16:20:00"},{id:"CD003",imageCount:30,completedCount:30,status:"completed",operator:"admin",createTime:"2024-01-14 10:15:00",riskSummary:{noRisk:20,lowRisk:8,mediumRisk:2,highRisk:0}},{id:"CD004",imageCount:8,completedCount:0,status:"pending",operator:"admin",createTime:"2024-01-15 18:45:00"},{id:"CD005",imageCount:15,completedCount:0,status:"failed",operator:"admin",createTime:"2024-01-13 09:30:00"}],results:[{id:"DR001",taskId:"CD001",fileName:"product_image_001.jpg",originalImage:"https://picsum.photos/400/400?random=1",riskLevel:"high_risk",confidence:95,similarImages:[{id:"SI001",url:"https://picsum.photos/400/400?random=101",source:"shutterstock.com",similarity:95,copyright:"Shutterstock Inc."},{id:"SI002",url:"https://picsum.photos/400/400?random=102",source:"getty.com",similarity:88,copyright:"Getty Images"}],detectionTime:"2024-01-15 14:32:15"},{id:"DR002",taskId:"CD001",fileName:"product_image_002.jpg",originalImage:"https://picsum.photos/400/400?random=2",riskLevel:"medium_risk",confidence:72,similarImages:[{id:"SI003",url:"https://picsum.photos/400/400?random=103",source:"unsplash.com",similarity:72,copyright:"Unsplash License"}],detectionTime:"2024-01-15 14:32:28"},{id:"DR003",taskId:"CD001",fileName:"product_image_003.jpg",originalImage:"https://picsum.photos/400/400?random=3",riskLevel:"low_risk",confidence:45,similarImages:[{id:"SI004",url:"https://picsum.photos/400/400?random=104",source:"pexels.com",similarity:45,copyright:"Pexels License"}],detectionTime:"2024-01-15 14:32:41"},{id:"DR004",taskId:"CD001",fileName:"product_image_004.jpg",originalImage:"https://picsum.photos/400/400?random=4",riskLevel:"no_risk",confidence:15,similarImages:[],detectionTime:"2024-01-15 14:32:55"}]}),me=b=>({no_risk:"无风险",low_risk:"低风险",medium_risk:"中风险",high_risk:"高风险"})[b],we=b=>({pending:"等待中",processing:"检测中",completed:"已完成",failed:"失败"})[b],Ce=b=>ee.results.filter(S=>S.taskId===b),$e=b=>new Promise(S=>{setTimeout(()=>{const I={id:`CD${String(ee.tasks.length+1).padStart(3,"0")}`,imageCount:b,completedCount:0,status:"pending",operator:"admin",createTime:new Date().toLocaleString("zh-CN")};ee.tasks.unshift(I),S(I)},1e3)}),Re={class:"space-y-6"},Se={class:"flex justify-start space-x-3"},Ie={key:0,class:"border border-gray-200 dark:border-dark-border rounded-lg p-4"},Ve={class:"grid grid-cols-6 gap-4"},De={class:"relative"},Te=["src","alt"],Le=["onClick"],je=["title"],Me={key:0,class:"mt-4 flex justify-center"},ze={key:1,class:"border-2 border-dashed border-gray-300 dark:border-dark-border rounded-lg p-12 text-center"},Be={key:2,class:"bg-gray-50 dark:bg-dark-card rounded-lg p-4"},Ee={class:"grid grid-cols-2 gap-4"},Ne={class:"flex justify-between items-center"},Pe={class:"text-sm text-gray-500 dark:text-dark-text-secondary"},Ae={key:0},Fe={key:0,class:"text-green-600 dark:text-green-400"},Ge={key:1,class:"text-blue-600 dark:text-blue-400"},He={key:1},Ue={class:"flex space-x-3"},oe="copyright-detection",Ke=re({__name:"CreateDetectionDialog",props:{modelValue:{type:Boolean}},emits:["update:modelValue","success"],setup(b,{emit:S}){const I=b,B=S,w=h(!1),V=h(!1),E=h([]),k=h([]),R=h(1),f=h(18),T=h("standard"),c=h("80");J(()=>I.modelValue,v=>{w.value=v}),J(w,v=>{B("update:modelValue",v)});const o=O(()=>{const v=(R.value-1)*f.value,a=v+f.value;return k.value.slice(v,a)}),H=v=>{const a=new FileReader;a.onload=C=>{var K;const q=Date.now()+Math.random();k.value.push({id:q,name:v.name,url:(K=C.target)==null?void 0:K.result,file:v.raw})},a.readAsDataURL(v.raw)},W=v=>{const a=k.value.findIndex(C=>C.name===v.name);a>-1&&k.value.splice(a,1)},Q=v=>{const a=k.value.findIndex(C=>C.id===v);if(a>-1){k.value.splice(a,1);const C=Math.ceil(k.value.length/f.value);R.value>C&&C>0&&(R.value=C)}},N=v=>{v.forEach(a=>{k.value.find(C=>C.id===a.id)||k.value.push({id:a.id,name:a.name,url:a.url||a.thumbnail})}),V.value=!1},A=v=>{R.value=v},u=fe(oe),r=O(()=>k.value.length===0?0:he(oe,k.value.length)),L=O(()=>_e(oe,k.value.length)),X=O(()=>{if(k.value.length===0)return"开始检测";const v=r.value;return v===0?"开始检测（免费）":`开始检测（¥${v.toFixed(2)}）`}),U=async()=>{if(k.value.length===0){D.warning("请先选择图片");return}try{await $e(k.value.length),D.success(`正在创建侵权检测任务，共 ${k.value.length} 张图片`),z(),B("success"),B("update:modelValue",!1)}catch{D.error("创建任务失败，请重试")}},z=()=>{k.value=[],E.value=[],R.value=1,T.value="standard",c.value="80"};return(v,a)=>{const C=y("el-button"),q=y("el-upload"),K=y("el-pagination"),n=y("el-option"),t=y("el-select"),p=y("el-dialog");return g(),x(Z,null,[s(p,{modelValue:w.value,"onUpdate:modelValue":a[5]||(a[5]=m=>w.value=m),title:"新建侵权检测任务",width:"900px","align-center":"",onClose:z},{footer:l(()=>[e("div",Ne,[e("div",Pe,[k.value.length>0?(g(),x("div",Ae,[e("div",null,"将检测 "+d(k.value.length)+" 张图片的侵权风险",1),L.value.hasFreeQuota&&L.value.freeItems>0?(g(),x("div",Fe," 免费额度："+d(L.value.freeItems)+" 张，付费："+d(L.value.chargeableItems)+" 张 ",1)):M("",!0),F(u)?(g(),x("div",Ge," 单价：¥"+d(F(u).unitPrice.toFixed(2))+"/张 ",1)):M("",!0)])):(g(),x("div",He,"请先选择图片"))]),e("div",Ue,[s(C,{onClick:a[4]||(a[4]=m=>w.value=!1)},{default:l(()=>a[14]||(a[14]=[_("取消")])),_:1,__:[14]}),s(C,{type:"primary",onClick:U,disabled:k.value.length===0},{default:l(()=>[_(d(X.value),1)]),_:1},8,["disabled"])])])]),default:l(()=>[e("div",Re,[e("div",Se,[s(q,{ref:"uploadRef","file-list":E.value,"on-change":H,"on-remove":W,"auto-upload":!1,multiple:"",accept:"image/*","show-file-list":!1},{default:l(()=>[s(C,{type:"primary",size:"large"},{default:l(()=>a[7]||(a[7]=[e("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"})],-1),_(" 上传图片 ")])),_:1,__:[7]})]),_:1},8,["file-list"]),s(C,{size:"large",onClick:a[0]||(a[0]=m=>V.value=!0)},{default:l(()=>a[8]||(a[8]=[e("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1),_(" 从图库选择 ")])),_:1,__:[8]})]),k.value.length>0?(g(),x("div",Ie,[e("div",Ve,[(g(!0),x(Z,null,ie(o.value,m=>(g(),x("div",{key:m.id,class:"relative group"},[e("div",De,[e("img",{src:m.url,alt:m.name,class:"w-full h-24 object-cover rounded-lg border border-gray-200 dark:border-dark-border"},null,8,Te),e("button",{onClick:P=>Q(m.id),class:"absolute -top-2 -right-2 w-6 h-6 bg-red-500 hover:bg-red-600 text-white rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200"},a[9]||(a[9]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]),8,Le)]),e("p",{class:"mt-2 text-xs text-gray-600 dark:text-dark-text-secondary truncate",title:m.name},d(m.name),9,je)]))),128))]),k.value.length>f.value?(g(),x("div",Me,[s(K,{"current-page":R.value,"onUpdate:currentPage":a[1]||(a[1]=m=>R.value=m),"page-size":f.value,total:k.value.length,layout:"prev, pager, next",small:"",onCurrentChange:A},null,8,["current-page","page-size","total"])])):M("",!0)])):(g(),x("div",ze,a[10]||(a[10]=[e("svg",{class:"mx-auto h-12 w-12 text-gray-400 dark:text-dark-text-secondary",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1),e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-dark-text"},"暂无图片",-1),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-dark-text-secondary"},"请上传图片或从图库中选择图片进行侵权检测",-1)]))),k.value.length>0?(g(),x("div",Be,[a[13]||(a[13]=e("h3",{class:"text-sm font-medium text-gray-900 dark:text-dark-text mb-3"},"检测设置",-1)),e("div",Ee,[e("div",null,[a[11]||(a[11]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text-secondary mb-2"}," 检测精度 ",-1)),s(t,{modelValue:T.value,"onUpdate:modelValue":a[2]||(a[2]=m=>T.value=m),style:{width:"100%"}},{default:l(()=>[s(n,{label:"标准检测",value:"standard"}),s(n,{label:"高精度检测",value:"high"}),s(n,{label:"快速检测",value:"fast"})]),_:1},8,["modelValue"])]),e("div",null,[a[12]||(a[12]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text-secondary mb-2"}," 相似度阈值 ",-1)),s(t,{modelValue:c.value,"onUpdate:modelValue":a[3]||(a[3]=m=>c.value=m),style:{width:"100%"}},{default:l(()=>[s(n,{label:"70% (宽松)",value:"70"}),s(n,{label:"80% (标准)",value:"80"}),s(n,{label:"90% (严格)",value:"90"})]),_:1},8,["modelValue"])])])])):M("",!0)])]),_:1},8,["modelValue"]),s(ye,{modelValue:V.value,"onUpdate:modelValue":a[6]||(a[6]=m=>V.value=m),"theme-color":"blue",onSelect:N},null,8,["modelValue"])],64)}}}),Oe=ae(Ke,[["__scopeId","data-v-c67d0a24"]]),We={key:0,class:"space-y-6"},Qe={class:"bg-gray-50 dark:bg-dark-card rounded-lg p-4"},qe={class:"flex items-start space-x-4"},Je={class:"flex-shrink-0"},Xe={class:"flex-1 space-y-2"},Ye={class:"text-sm font-medium text-gray-900 dark:text-dark-text"},Ze={class:"text-sm font-medium text-gray-900 dark:text-dark-text"},et={class:"text-sm font-medium text-gray-900 dark:text-dark-text"},tt={class:"text-lg font-semibold text-gray-900 dark:text-dark-text mb-4"},st={key:0,class:"space-y-4"},rt={class:"flex items-start space-x-4"},at={class:"flex-shrink-0"},ot={class:"flex-1"},lt={class:"grid grid-cols-2 gap-4"},dt={class:"text-sm font-medium text-gray-900 dark:text-dark-text"},nt={class:"text-sm font-medium text-gray-900 dark:text-dark-text"},it={key:0,class:"col-span-2"},ut={class:"text-sm font-medium text-gray-900 dark:text-dark-text"},ct={class:"mt-3"},mt={class:"flex items-center justify-between mb-1"},gt={class:"text-xs font-medium text-gray-900 dark:text-dark-text"},kt={class:"flex-shrink-0 flex flex-col space-y-2"},xt={key:1,class:"text-center py-8"},pt={class:"bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-800"},vt={class:"text-sm text-blue-800 dark:text-blue-200"},bt={key:0},yt={key:1},ft={key:2},ht={key:3},_t={class:"flex justify-between items-center"},wt={class:"text-sm text-gray-500 dark:text-dark-text-secondary"},Ct={class:"flex space-x-3"},$t=re({__name:"SimilarImagesDialog",props:{modelValue:{type:Boolean},detectionResult:{}},emits:["update:modelValue"],setup(b,{emit:S}){const I=b,B=S,w=h(!1);J(()=>I.modelValue,c=>{w.value=c}),J(w,c=>{B("update:modelValue",c)});const V=c=>({[G.NO_RISK]:"success",[G.LOW_RISK]:"warning",[G.MEDIUM_RISK]:"danger",[G.HIGH_RISK]:"danger"})[c],E=c=>c>=90?"#f56565":c>=80?"#ed8936":c>=70?"#ecc94b":"#48bb78",k=c=>{window.open(c,"_blank")},R=c=>{D.success(`正在举报来自 ${c.source} 的侵权图片...`)},f=()=>{D.success("正在导出相似图片报告...")},T=()=>{w.value=!1};return(c,o)=>{const H=y("el-image"),W=y("el-tag"),Q=y("el-progress"),N=y("el-button"),A=y("el-dialog");return g(),le(A,{modelValue:w.value,"onUpdate:modelValue":o[0]||(o[0]=u=>w.value=u),title:"相似图片详情",width:"1000px","align-center":"",onClose:T},{footer:l(()=>{var u,r;return[e("div",_t,[e("div",wt,d(((r=(u=c.detectionResult)==null?void 0:u.similarImages)==null?void 0:r.length)||0)+" 张相似图片 ",1),e("div",Ct,[s(N,{onClick:T},{default:l(()=>o[18]||(o[18]=[_("关闭")])),_:1,__:[18]}),s(N,{type:"primary",onClick:f},{default:l(()=>o[19]||(o[19]=[_("导出报告")])),_:1,__:[19]})])])]}),default:l(()=>{var u;return[c.detectionResult?(g(),x("div",We,[e("div",Qe,[o[5]||(o[5]=e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-dark-text mb-4"},"原图信息",-1)),e("div",qe,[e("div",Je,[s(H,{src:c.detectionResult.originalImage,"preview-src-list":[c.detectionResult.originalImage],fit:"cover",class:"w-32 h-32 rounded-lg border border-gray-200 dark:border-dark-border"},null,8,["src","preview-src-list"])]),e("div",Xe,[e("div",null,[o[1]||(o[1]=e("span",{class:"text-sm text-gray-500 dark:text-dark-text-secondary"},"文件名：",-1)),e("span",Ye,d(c.detectionResult.fileName),1)]),e("div",null,[o[2]||(o[2]=e("span",{class:"text-sm text-gray-500 dark:text-dark-text-secondary"},"风险等级：",-1)),s(W,{type:V(c.detectionResult.riskLevel),size:"small"},{default:l(()=>[_(d(F(me)(c.detectionResult.riskLevel)),1)]),_:1},8,["type"])]),e("div",null,[o[3]||(o[3]=e("span",{class:"text-sm text-gray-500 dark:text-dark-text-secondary"},"置信度：",-1)),e("span",Ze,d(c.detectionResult.confidence)+"%",1)]),e("div",null,[o[4]||(o[4]=e("span",{class:"text-sm text-gray-500 dark:text-dark-text-secondary"},"检测时间：",-1)),e("span",et,d(c.detectionResult.detectionTime),1)])])])]),e("div",null,[e("h3",tt," 相似图片 ("+d(((u=c.detectionResult.similarImages)==null?void 0:u.length)||0)+" 张) ",1),c.detectionResult.similarImages&&c.detectionResult.similarImages.length>0?(g(),x("div",st,[(g(!0),x(Z,null,ie(c.detectionResult.similarImages,r=>(g(),x("div",{key:r.id,class:"bg-white dark:bg-dark-surface rounded-lg border border-gray-200 dark:border-dark-border p-4"},[e("div",rt,[e("div",at,[s(H,{src:r.url,"preview-src-list":[r.url],fit:"cover",class:"w-24 h-24 rounded-lg border border-gray-200 dark:border-dark-border"},null,8,["src","preview-src-list"])]),e("div",ot,[e("div",lt,[e("div",null,[o[6]||(o[6]=e("span",{class:"text-sm text-gray-500 dark:text-dark-text-secondary"},"来源：",-1)),e("span",dt,d(r.source),1)]),e("div",null,[o[7]||(o[7]=e("span",{class:"text-sm text-gray-500 dark:text-dark-text-secondary"},"相似度：",-1)),e("span",nt,d(r.similarity)+"%",1)]),r.copyright?(g(),x("div",it,[o[8]||(o[8]=e("span",{class:"text-sm text-gray-500 dark:text-dark-text-secondary"},"版权信息：",-1)),e("span",ut,d(r.copyright),1)])):M("",!0)]),e("div",ct,[e("div",mt,[o[9]||(o[9]=e("span",{class:"text-xs text-gray-500 dark:text-dark-text-secondary"},"相似度",-1)),e("span",gt,d(r.similarity)+"%",1)]),s(Q,{percentage:r.similarity,"stroke-width":6,"show-text":!1,color:E(r.similarity)},null,8,["percentage","color"])])]),e("div",kt,[s(N,{size:"small",onClick:L=>k(r.url)},{default:l(()=>o[10]||(o[10]=[_(" 查看原图 ")])),_:2,__:[10]},1032,["onClick"]),s(N,{size:"small",type:"primary",onClick:L=>R(r)},{default:l(()=>o[11]||(o[11]=[_(" 举报侵权 ")])),_:2,__:[11]},1032,["onClick"])])])]))),128))])):(g(),x("div",xt,o[12]||(o[12]=[e("svg",{class:"mx-auto h-12 w-12 text-gray-400 dark:text-dark-text-secondary",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1),e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-dark-text"},"未发现相似图片",-1),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-dark-text-secondary"},"该图片未检测到潜在的侵权风险",-1)])))]),e("div",pt,[o[17]||(o[17]=e("h3",{class:"text-lg font-semibold text-blue-900 dark:text-blue-300 mb-2"},"风险评估建议",-1)),e("div",vt,[c.detectionResult.riskLevel==="high_risk"?(g(),x("p",bt,o[13]||(o[13]=[e("strong",null,"高风险：",-1),_("检测到高度相似的图片，建议立即停止使用该图片，或联系版权方获得授权。 ")]))):c.detectionResult.riskLevel==="medium_risk"?(g(),x("p",yt,o[14]||(o[14]=[e("strong",null,"中风险：",-1),_("检测到中度相似的图片，建议谨慎使用，可考虑进行图片修改或寻找替代图片。 ")]))):c.detectionResult.riskLevel==="low_risk"?(g(),x("p",ft,o[15]||(o[15]=[e("strong",null,"低风险：",-1),_("检测到轻微相似的图片，风险较低，但建议保持关注。 ")]))):(g(),x("p",ht,o[16]||(o[16]=[e("strong",null,"无风险：",-1),_("未检测到明显的侵权风险，可以安全使用。 ")])))])])])):M("",!0)]}),_:1},8,["modelValue"])}}}),Rt=ae($t,[["__scopeId","data-v-adf4fa82"]]),St={class:"flex items-center justify-between p-6 border-b border-gray-100 dark:border-dark-border"},It={class:"flex items-center space-x-3"},Vt={class:"text-sm text-gray-500 dark:text-dark-text-secondary"},Dt={class:"px-6 py-4 bg-gray-50 dark:bg-dark-card border-b border-gray-100 dark:border-dark-border"},Tt={class:"grid grid-cols-2 md:grid-cols-4 gap-6"},Lt={class:"text-lg font-semibold text-gray-900 dark:text-dark-text"},jt={class:"text-lg font-semibold text-gray-900 dark:text-dark-text"},Mt={class:"text-lg font-semibold text-gray-900 dark:text-dark-text"},zt={class:"text-lg font-semibold text-gray-900 dark:text-dark-text"},Bt={key:0,class:"px-6 py-4 border-b border-gray-100 dark:border-dark-border"},Et={class:"grid grid-cols-4 gap-4"},Nt={class:"bg-green-50 dark:bg-green-900/20 rounded-lg p-4 text-center"},Pt={class:"text-2xl font-bold text-green-600 dark:text-green-400"},At={class:"bg-yellow-50 dark:bg-yellow-900/20 rounded-lg p-4 text-center"},Ft={class:"text-2xl font-bold text-yellow-600 dark:text-yellow-400"},Gt={class:"bg-orange-50 dark:bg-orange-900/20 rounded-lg p-4 text-center"},Ht={class:"text-2xl font-bold text-orange-600 dark:text-orange-400"},Ut={class:"bg-red-50 dark:bg-red-900/20 rounded-lg p-4 text-center"},Kt={class:"text-2xl font-bold text-red-600 dark:text-red-400"},Ot={class:"px-6 py-4 border-b border-gray-100 dark:border-dark-border"},Wt={class:"flex items-center justify-between"},Qt={class:"flex items-center space-x-4"},qt={class:"flex space-x-2"},Jt={class:"px-6 pb-6"},Xt={class:"bg-white dark:bg-dark-surface rounded-xl border border-gray-200 dark:border-dark-border overflow-hidden"},Yt={class:"flex justify-center"},Zt={class:"flex flex-col items-center"},es={class:"text-sm font-medium"},ts={class:"text-sm"},ss={class:"flex justify-between items-center px-6 py-4 border-t border-gray-100 dark:border-dark-border bg-gray-50 dark:bg-dark-card"},rs={class:"text-sm text-gray-500 dark:text-dark-text-secondary"},as={class:"flex space-x-3"},os=re({__name:"ViewDetailsDialog",props:{modelValue:{type:Boolean},task:{}},emits:["update:modelValue"],setup(b,{emit:S}){const I=b,B=S,w=h(!1),V=h(!1),E=h(!1),k=h(null),R=h([]);J(()=>I.modelValue,u=>{w.value=u,u&&I.task&&c()}),J(w,u=>{B("update:modelValue",u)});const f=O(()=>I.task?Ce(I.task.id):[]),T=O(()=>{let u=f.value;return R.value.length>0&&(u=u.filter(r=>R.value.includes(r.riskLevel))),u}),c=()=>{V.value=!0,setTimeout(()=>{V.value=!1},500)},o=u=>({[G.NO_RISK]:"success",[G.LOW_RISK]:"warning",[G.MEDIUM_RISK]:"danger",[G.HIGH_RISK]:"danger"})[u],H=u=>u>=80?"#f56565":u>=60?"#ed8936":u>=40?"#ecc94b":"#48bb78",W=()=>{},Q=u=>{k.value=u,E.value=!0},N=()=>{D.success("正在导出检测结果...")},A=()=>{w.value=!1,R.value=[]};return(u,r)=>{const L=y("el-option"),X=y("el-select"),U=y("el-button"),z=y("el-table-column"),v=y("el-image"),a=y("el-tag"),C=y("el-progress"),q=y("el-table"),K=y("el-dialog"),n=ce("loading");return g(),x(Z,null,[s(K,{modelValue:w.value,"onUpdate:modelValue":r[1]||(r[1]=t=>w.value=t),width:"1200px","before-close":A,"show-close":!1,class:"modern-dialog"},{header:l(()=>{var t;return[e("div",St,[e("div",It,[r[4]||(r[4]=e("div",{class:"w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"})])],-1)),e("div",null,[r[3]||(r[3]=e("h3",{class:"text-xl font-bold text-gray-900 dark:text-dark-text"},"侵权检测详情",-1)),e("p",Vt,"任务ID: "+d((t=u.task)==null?void 0:t.id),1)])]),e("button",{onClick:A,class:"p-2 hover:bg-gray-100 dark:hover:bg-dark-hover rounded-lg transition-colors duration-200"},r[5]||(r[5]=[e("svg",{class:"w-5 h-5 text-gray-500 dark:text-dark-text-secondary",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))])]}),footer:l(()=>[e("div",ss,[e("div",rs," 共 "+d(T.value.length)+" 条检测结果 ",1),e("div",as,[s(U,{onClick:A},{default:l(()=>r[18]||(r[18]=[_("关闭")])),_:1,__:[18]}),s(U,{type:"primary",onClick:N},{default:l(()=>r[19]||(r[19]=[_("导出详情")])),_:1,__:[19]})])])]),default:l(()=>{var t,p,m,P,te,se;return[e("div",Dt,[e("div",Tt,[e("div",null,[r[6]||(r[6]=e("p",{class:"text-sm text-gray-500 dark:text-dark-text-secondary"},"图片总数",-1)),e("p",Lt,d((t=u.task)==null?void 0:t.imageCount),1)]),e("div",null,[r[7]||(r[7]=e("p",{class:"text-sm text-gray-500 dark:text-dark-text-secondary"},"检测进度",-1)),e("p",jt,d((p=u.task)==null?void 0:p.completedCount)+"/"+d((m=u.task)==null?void 0:m.imageCount),1)]),e("div",null,[r[8]||(r[8]=e("p",{class:"text-sm text-gray-500 dark:text-dark-text-secondary"},"操作员",-1)),e("p",Mt,d((P=u.task)==null?void 0:P.operator),1)]),e("div",null,[r[9]||(r[9]=e("p",{class:"text-sm text-gray-500 dark:text-dark-text-secondary"},"创建时间",-1)),e("p",zt,d((te=u.task)==null?void 0:te.createTime),1)])])]),(se=u.task)!=null&&se.riskSummary?(g(),x("div",Bt,[r[14]||(r[14]=e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-dark-text mb-4"},"风险分布",-1)),e("div",Et,[e("div",Nt,[e("div",Pt,d(u.task.riskSummary.noRisk),1),r[10]||(r[10]=e("div",{class:"text-sm text-green-600 dark:text-green-400"},"无风险",-1))]),e("div",At,[e("div",Ft,d(u.task.riskSummary.lowRisk),1),r[11]||(r[11]=e("div",{class:"text-sm text-yellow-600 dark:text-yellow-400"},"低风险",-1))]),e("div",Gt,[e("div",Ht,d(u.task.riskSummary.mediumRisk),1),r[12]||(r[12]=e("div",{class:"text-sm text-orange-600 dark:text-orange-400"},"中风险",-1))]),e("div",Ut,[e("div",Kt,d(u.task.riskSummary.highRisk),1),r[13]||(r[13]=e("div",{class:"text-sm text-red-600 dark:text-red-400"},"高风险",-1))])])])):M("",!0),e("div",Ot,[e("div",Wt,[e("div",Qt,[r[15]||(r[15]=e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-dark-text"},"检测结果",-1)),s(X,{modelValue:R.value,"onUpdate:modelValue":r[0]||(r[0]=$=>R.value=$),placeholder:"筛选风险等级",multiple:"",clearable:"",style:{width:"200px"},onChange:W},{default:l(()=>[s(L,{label:"无风险",value:"no_risk"}),s(L,{label:"低风险",value:"low_risk"}),s(L,{label:"中风险",value:"medium_risk"}),s(L,{label:"高风险",value:"high_risk"})]),_:1},8,["modelValue"])]),e("div",qt,[s(U,{onClick:N,size:"small"},{default:l(()=>r[16]||(r[16]=[e("svg",{class:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})],-1),_(" 导出结果 ")])),_:1,__:[16]})])])]),e("div",Jt,[e("div",Xt,[ue((g(),le(q,{data:T.value,style:{width:"100%"},"max-height":"400",class:"modern-table"},{default:l(()=>[s(z,{prop:"fileName",label:"文件名",width:"200"}),s(z,{label:"原图",width:"100",align:"center"},{default:l($=>[e("div",Yt,[s(v,{src:$.row.originalImage,"preview-src-list":[$.row.originalImage],fit:"cover",class:"w-16 h-16 rounded-lg border border-gray-200 dark:border-dark-border"},null,8,["src","preview-src-list"])])]),_:1}),s(z,{label:"风险等级",width:"120",align:"center"},{default:l($=>[s(a,{type:o($.row.riskLevel),size:"small"},{default:l(()=>[_(d(F(me)($.row.riskLevel)),1)]),_:2},1032,["type"])]),_:1}),s(z,{label:"置信度",align:"center"},{default:l($=>[e("div",Zt,[e("span",es,d($.row.confidence)+"%",1),s(C,{percentage:$.row.confidence,"stroke-width":4,"show-text":!1,color:H($.row.confidence),class:"w-full mt-1"},null,8,["percentage","color"])])]),_:1}),s(z,{label:"相似图片",width:"120",align:"center"},{default:l($=>{var Y;return[e("span",ts,d(((Y=$.row.similarImages)==null?void 0:Y.length)||0)+" 张",1)]}),_:1}),s(z,{prop:"detectionTime",label:"检测时间",width:"160",align:"center"}),s(z,{label:"操作",width:"120",align:"center",fixed:"right"},{default:l($=>[s(U,{type:"primary",size:"small",onClick:Y=>Q($.row),disabled:!$.row.similarImages||$.row.similarImages.length===0},{default:l(()=>r[17]||(r[17]=[_(" 查看详情 ")])),_:2,__:[17]},1032,["onClick","disabled"])]),_:1})]),_:1},8,["data"])),[[n,V.value]])])])]}),_:1},8,["modelValue"]),s(Rt,{modelValue:E.value,"onUpdate:modelValue":r[2]||(r[2]=t=>E.value=t),"detection-result":k.value},null,8,["modelValue","detection-result"])],64)}}}),ls=ae(os,[["__scopeId","data-v-b64d5a2b"]]),ds={class:"space-y-6"},ns={class:"grid grid-cols-1 md:grid-cols-4 gap-6"},is={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},us={class:"flex items-center justify-between"},cs={class:"text-2xl font-bold text-gray-900 dark:text-dark-text"},ms={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},gs={class:"flex items-center justify-between"},ks={class:"text-2xl font-bold text-yellow-600 dark:text-yellow-400"},xs={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},ps={class:"flex items-center justify-between"},vs={class:"text-2xl font-bold text-orange-600 dark:text-orange-400"},bs={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},ys={class:"flex items-center justify-between"},fs={class:"text-2xl font-bold text-red-600 dark:text-red-400"},hs={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border"},_s={class:"flex justify-between items-center"},ws={class:"flex items-center space-x-3"},Cs={key:0,class:"flex items-center space-x-2 ml-4 pl-4 border-l border-gray-200 dark:border-dark-border"},$s={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},Rs={class:"bg-white dark:bg-dark-surface rounded-xl shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border overflow-hidden"},Ss={class:"overflow-x-auto"},Is={class:"font-mono text-sm font-medium text-primary-600 dark:text-primary-400"},Vs={class:"space-y-1"},Ds={class:"flex items-center space-x-2"},Ts={class:"font-medium text-gray-900 dark:text-dark-text"},Ls={class:"flex items-center space-x-2"},js={class:"font-medium text-green-600 dark:text-green-400"},Ms={class:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5"},zs={class:"space-y-2"},Bs={key:0},Es=["onClick"],Ns={key:0,class:"flex flex-wrap gap-1"},Ps={key:0,class:"inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400"},As={key:1,class:"inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400"},Fs={key:2,class:"inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-400"},Gs={key:3,class:"inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400"},Hs={key:1,class:"text-gray-400 dark:text-dark-text-secondary"},Us={class:"font-medium text-gray-900 dark:text-dark-text"},Ks={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},Os={class:"flex items-center space-x-2"},Ws=["onClick"],Qs={class:"flex justify-between items-center px-6 py-4 border-t border-gray-100 dark:border-dark-border bg-gray-50 dark:bg-dark-card/50"},qs={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},Js=re({__name:"index",setup(b){const S=h(!1),I=h(!1),B=h(!1),w=h(null),V=h([]),E=h(""),k=h([]),R=h(""),f=h({currentPage:1,pageSize:10,total:0}),T=O(()=>ee.tasks.filter(t=>t.status===j.COMPLETED).reduce((t,p)=>(p.riskSummary&&(t.noRisk+=p.riskSummary.noRisk,t.lowRisk+=p.riskSummary.lowRisk,t.mediumRisk+=p.riskSummary.mediumRisk,t.highRisk+=p.riskSummary.highRisk),t),{noRisk:0,lowRisk:0,mediumRisk:0,highRisk:0})),c=O(()=>{let n=ee.tasks;if(E.value&&(n=n.filter(m=>m.status===E.value)),k.value.length>0&&(n=n.filter(m=>m.riskSummary?k.value.some(P=>{switch(P){case"no_risk":return m.riskSummary.noRisk>0;case"low_risk":return m.riskSummary.lowRisk>0;case"medium_risk":return m.riskSummary.mediumRisk>0;case"high_risk":return m.riskSummary.highRisk>0;default:return!1}}):!1)),R.value){const m=R.value.toLowerCase();n=n.filter(P=>P.id.toLowerCase().includes(m)||P.operator.toLowerCase().includes(m))}f.value.total=n.length;const t=(f.value.currentPage-1)*f.value.pageSize,p=t+f.value.pageSize;return n.slice(t,p)});xe(()=>{o()});const o=()=>{S.value=!0,setTimeout(()=>{S.value=!1},500)},H=n=>{V.value=n},W=n=>{w.value=n,B.value=!0},Q=n=>{const{action:t,row:p}=n;switch(t){case"titleGenerate":U(p);break;case"batchListing":z(p);break;case"smartCrop":v(p);break;case"oneClickCutout":a(p);break;case"superSplit":C(p);break}},N=n=>{const t={[j.PENDING]:"bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400",[j.PROCESSING]:"bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400",[j.COMPLETED]:"bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400",[j.FAILED]:"bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400"};return t[n]||t[j.PENDING]},A=n=>{const t={[j.PENDING]:"bg-gray-400",[j.PROCESSING]:"bg-blue-400",[j.COMPLETED]:"bg-green-400",[j.FAILED]:"bg-red-400"};return t[n]||t[j.PENDING]},u=n=>{D.info(`任务 ${n.id} 失败原因：网络连接超时`)},r=()=>{D.success("导出表格功能开发中...")},L=()=>{D.success(`正在批量导出 ${V.value.length} 个任务...`)},X=()=>{D.success("侵权检测任务创建成功！"),o()},U=n=>{D.success(`正在为检测任务 ${n.id} 创建标题生成任务...`)},z=n=>{D.success(`正在为检测任务 ${n.id} 创建批量刊登任务...`)},v=n=>{D.success(`正在为检测任务 ${n.id} 创建智能裁图任务...`)},a=n=>{D.success(`正在为检测任务 ${n.id} 创建一键抠图任务...`)},C=n=>{D.success(`正在为检测任务 ${n.id} 创建超级裂变任务...`)},q=n=>{f.value.pageSize=n,f.value.currentPage=1,o()},K=n=>{f.value.currentPage=n,o()};return(n,t)=>{const p=y("el-table-column"),m=y("el-dropdown-item"),P=y("el-dropdown-menu"),te=y("el-dropdown"),se=y("el-table"),$=y("el-pagination"),Y=ce("loading");return g(),x(Z,null,[e("div",ds,[t[26]||(t[26]=pe('<div class="bg-gradient-to-r from-primary-50 to-blue-50 dark:from-primary-900/20 dark:to-blue-900/20 rounded-2xl p-6 border border-primary-100 dark:border-primary-800" data-v-299511ea><div class="flex items-center space-x-3" data-v-299511ea><div class="w-10 h-10 bg-gradient-to-br from-primary-500 to-primary-600 rounded-xl flex items-center justify-center" data-v-299511ea><svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-299511ea><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" data-v-299511ea></path></svg></div><div data-v-299511ea><h1 class="text-2xl font-bold text-gray-900 dark:text-dark-text" data-v-299511ea>侵权检测</h1><p class="mt-1 text-sm text-gray-600 dark:text-dark-text-secondary" data-v-299511ea>AI智能图片侵权检测和风险评估工具</p></div></div></div>',1)),e("div",ns,[e("div",is,[e("div",us,[e("div",null,[t[5]||(t[5]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"无风险",-1)),e("p",cs,d(T.value.noRisk),1)]),t[6]||(t[6]=e("div",{class:"w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-green-600 dark:text-green-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 13l4 4L19 7"})])],-1))])]),e("div",ms,[e("div",gs,[e("div",null,[t[7]||(t[7]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"低风险",-1)),e("p",ks,d(T.value.lowRisk),1)]),t[8]||(t[8]=e("div",{class:"w-12 h-12 bg-yellow-100 dark:bg-yellow-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-yellow-600 dark:text-yellow-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"})])],-1))])]),e("div",xs,[e("div",ps,[e("div",null,[t[9]||(t[9]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"中风险",-1)),e("p",vs,d(T.value.mediumRisk),1)]),t[10]||(t[10]=e("div",{class:"w-12 h-12 bg-orange-100 dark:bg-orange-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-orange-600 dark:text-orange-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1))])]),e("div",bs,[e("div",ys,[e("div",null,[t[11]||(t[11]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"高风险",-1)),e("p",fs,d(T.value.highRisk),1)]),t[12]||(t[12]=e("div",{class:"w-12 h-12 bg-red-100 dark:bg-red-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-red-600 dark:text-red-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})])],-1))])])]),e("div",hs,[e("div",_s,[e("div",ws,[e("button",{onClick:t[0]||(t[0]=i=>I.value=!0),class:"inline-flex items-center px-4 py-2.5 bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 text-white font-medium rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"},[s(F(ve),{class:"w-5 h-5 mr-2"}),t[13]||(t[13]=_(" 新建检测 "))]),e("button",{onClick:r,class:"inline-flex items-center px-4 py-2.5 bg-white dark:bg-dark-card text-gray-700 dark:text-dark-text font-medium rounded-lg border border-gray-300 dark:border-dark-border hover:bg-gray-50 dark:hover:bg-dark-border shadow-sm hover:shadow-md transition-all duration-200"},[s(F(ne),{class:"w-5 h-5 mr-2"}),t[14]||(t[14]=_(" 导出表格 "))]),V.value.length>0?(g(),x("div",Cs,[e("span",$s," 已选择 "+d(V.value.length)+" 项 ",1),e("button",{onClick:L,class:"inline-flex items-center px-3 py-1.5 bg-blue-500 hover:bg-blue-600 text-white text-sm font-medium rounded-lg transition-all duration-200"},[s(F(ne),{class:"w-4 h-4 mr-1"}),t[15]||(t[15]=_(" 批量导出 "))])])):M("",!0)])])]),e("div",Rs,[t[25]||(t[25]=e("div",{class:"px-6 py-4 border-b border-gray-100 dark:border-dark-border"},[e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-dark-text"},"检测任务列表"),e("p",{class:"text-sm text-gray-600 dark:text-dark-text-secondary mt-1"},"管理和监控您的所有侵权检测任务")],-1)),e("div",Ss,[ue((g(),le(se,{data:c.value,style:{width:"100%"},"header-cell-style":{backgroundColor:"var(--el-bg-color-page)",color:"var(--el-text-color-primary)",fontWeight:"600",borderBottom:"1px solid var(--el-border-color-light)"},"row-style":{backgroundColor:"transparent"},class:"modern-table",onSelectionChange:H},{default:l(()=>[s(p,{type:"selection",width:"55",align:"center"}),s(p,{prop:"id",label:"检测ID",width:"120"},{default:l(i=>[e("div",Is,d(i.row.id),1)]),_:1}),s(p,{label:"检测类型",width:"200"},{default:l(()=>t[16]||(t[16]=[e("div",{class:"flex items-center space-x-3"},[e("div",{class:"w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-blue-600 dark:text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"})])]),e("div",null,[e("div",{class:"font-medium text-gray-900 dark:text-dark-text"},"侵权检测"),e("div",{class:"text-sm text-gray-500 dark:text-dark-text-secondary"},"AI智能检测")])],-1)])),_:1}),s(p,{label:"检测数量"},{default:l(i=>[e("div",Vs,[e("div",Ds,[t[17]||(t[17]=e("span",{class:"text-xs text-gray-500 dark:text-dark-text-secondary"},"目标:",-1)),e("span",Ts,d(i.row.imageCount),1)]),e("div",Ls,[t[18]||(t[18]=e("span",{class:"text-xs text-gray-500 dark:text-dark-text-secondary"},"完成:",-1)),e("span",js,d(i.row.completedCount),1)]),e("div",Ms,[e("div",{class:"bg-green-500 h-1.5 rounded-full transition-all duration-300",style:be({width:`${i.row.completedCount/i.row.imageCount*100}%`})},null,4)])])]),_:1}),s(p,{label:"检测状态",width:"150"},{default:l(i=>[e("div",zs,[e("span",{class:de(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",N(i.row.status)])},[e("span",{class:de(["w-1.5 h-1.5 rounded-full mr-1.5",A(i.row.status)])},null,2),_(" "+d(F(we)(i.row.status)),1)],2),i.row.status===F(j).FAILED?(g(),x("div",Bs,[e("button",{onClick:ge=>u(i.row),class:"text-xs text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 underline"}," 查看原因 ",8,Es)])):M("",!0)])]),_:1}),s(p,{label:"风险分布",width:"200"},{default:l(i=>[i.row.riskSummary?(g(),x("div",Ns,[i.row.riskSummary.noRisk>0?(g(),x("span",Ps," 无风险: "+d(i.row.riskSummary.noRisk),1)):M("",!0),i.row.riskSummary.lowRisk>0?(g(),x("span",As," 低风险: "+d(i.row.riskSummary.lowRisk),1)):M("",!0),i.row.riskSummary.mediumRisk>0?(g(),x("span",Fs," 中风险: "+d(i.row.riskSummary.mediumRisk),1)):M("",!0),i.row.riskSummary.highRisk>0?(g(),x("span",Gs," 高风险: "+d(i.row.riskSummary.highRisk),1)):M("",!0)])):(g(),x("span",Hs,"-"))]),_:1}),s(p,{prop:"operator",label:"创建人",width:"100"},{default:l(i=>[e("div",Us,d(i.row.operator),1)]),_:1}),s(p,{prop:"createTime",label:"创建时间",width:"160"},{default:l(i=>[e("div",Ks,d(i.row.createTime),1)]),_:1}),s(p,{label:"操作",width:"180"},{default:l(i=>[e("div",Os,[e("button",{onClick:ge=>W(i.row),class:"inline-flex items-center px-3 py-1.5 text-sm font-medium text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 bg-primary-50 dark:bg-primary-900/20 hover:bg-primary-100 dark:hover:bg-primary-900/30 rounded-lg transition-all duration-200"}," 查看详情 ",8,Ws),s(te,{onCommand:Q,trigger:"click"},{dropdown:l(()=>[s(P,null,{default:l(()=>[s(m,{command:{action:"smartCrop",row:i.row}},{default:l(()=>t[19]||(t[19]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-green-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m0 0V1a1 1 0 011-1h2a1 1 0 011 1v18a1 1 0 01-1 1H4a1 1 0 01-1-1V1a1 1 0 011-1h2a1 1 0 011 1v3m0 0h8m-8 0H4a1 1 0 00-1 1v3a1 1 0 001 1h3m0 0h10m0 0h3a1 1 0 001-1V5a1 1 0 00-1-1h-3m-3 0v8a1 1 0 01-1 1H8a1 1 0 01-1-1V4"})]),e("span",null,"智能裁图")],-1)])),_:2,__:[19]},1032,["command"]),s(m,{command:{action:"titleGenerate",row:i.row}},{default:l(()=>t[20]||(t[20]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-blue-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})]),e("span",null,"标题生成")],-1)])),_:2,__:[20]},1032,["command"]),s(m,{command:{action:"batchListing",row:i.row}},{default:l(()=>t[21]||(t[21]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-purple-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"})]),e("span",null,"批量刊登")],-1)])),_:2,__:[21]},1032,["command"]),s(m,{command:{action:"oneClickCutout",row:i.row}},{default:l(()=>t[22]||(t[22]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-pink-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})]),e("span",null,"一键抠图")],-1)])),_:2,__:[22]},1032,["command"]),s(m,{command:{action:"superSplit",row:i.row}},{default:l(()=>t[23]||(t[23]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-orange-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 10V3L4 14h7v7l9-11h-7z"})]),e("span",null,"超级裂变")],-1)])),_:2,__:[23]},1032,["command"])]),_:2},1024)]),default:l(()=>[t[24]||(t[24]=e("button",{class:"inline-flex items-center px-2 py-1.5 text-sm font-medium text-gray-600 dark:text-dark-text-secondary hover:text-gray-700 dark:hover:text-dark-text bg-gray-50 dark:bg-dark-card hover:bg-gray-100 dark:hover:bg-dark-border rounded-lg transition-all duration-200"},[_(" 更多 "),e("svg",{class:"w-4 h-4 ml-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 9l-7 7-7-7"})])],-1))]),_:2,__:[24]},1024)])]),_:1})]),_:1},8,["data"])),[[Y,S.value]])]),e("div",Qs,[e("div",qs," 共 "+d(f.value.total)+" 条记录 ",1),s($,{"current-page":f.value.currentPage,"onUpdate:currentPage":t[1]||(t[1]=i=>f.value.currentPage=i),"page-size":f.value.pageSize,"onUpdate:pageSize":t[2]||(t[2]=i=>f.value.pageSize=i),"page-sizes":[10,20,50,100],total:f.value.total,layout:"sizes, prev, pager, next, jumper",onSizeChange:q,onCurrentChange:K,class:"modern-pagination"},null,8,["current-page","page-size","total"])])])]),s(Oe,{modelValue:I.value,"onUpdate:modelValue":t[3]||(t[3]=i=>I.value=i),onSuccess:X},null,8,["modelValue"]),s(ls,{modelValue:B.value,"onUpdate:modelValue":t[4]||(t[4]=i=>B.value=i),task:w.value},null,8,["modelValue","task"])],64)}}}),sr=ae(Js,[["__scopeId","data-v-299511ea"]]);export{sr as default};
