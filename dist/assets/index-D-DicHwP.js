import{d as me,e as _,r as k,q as Q,j as n,h as f,a as e,c as m,M as y,n as W,C,t as a,g as l,F,k as q,A as ke,E as V,o as u,_ as ge,f as ye,p as we,b as ve,v as _e,Q as Ce}from"./index-BTucjZQh.js";import{g as $e,c as Pe,a as je}from"./billing-DsHNwwW1.js";import"./billing-Ciq-WFQK.js";const Ve={class:"space-y-6"},Be={class:"flex items-center justify-center space-x-4 mb-8"},Me={class:"flex items-center"},Te={class:"flex items-center"},De={class:"flex items-center"},ze={key:0,class:"space-y-4"},Se={class:"flex items-center justify-between"},Le={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},Ie={class:"font-medium text-purple-600 dark:text-purple-400"},Oe={class:"border border-gray-200 dark:border-dark-border rounded-lg p-4"},Ne={class:"flex items-center space-x-4 mb-4"},Ue={class:"grid grid-cols-4 gap-4 max-h-96 overflow-y-auto"},He=["onClick"],Ae={key:0,class:"absolute top-2 right-2 w-6 h-6 bg-purple-500 rounded-full flex items-center justify-center"},Fe=["src","alt"],Ee={class:"text-sm font-medium text-gray-900 dark:text-dark-text truncate"},Ge={class:"text-xs text-gray-500 dark:text-dark-text-secondary"},Re={class:"text-sm font-medium text-green-600 dark:text-green-400 mt-1"},Ke={key:1,class:"space-y-4"},Qe={class:"flex space-x-3 mb-4"},We={key:0,class:"border border-gray-200 dark:border-dark-border rounded-lg p-4"},qe={class:"flex items-center space-x-4 mb-4"},Je={class:"grid grid-cols-5 gap-4 max-h-96 overflow-y-auto"},Xe=["onClick"],Ye={class:"relative"},Ze=["src","alt"],et={key:0,class:"absolute top-1 right-1 w-5 h-5 bg-purple-500 rounded-full flex items-center justify-center"},tt={class:"text-xs font-medium text-gray-900 dark:text-dark-text truncate"},rt={key:1,class:"border border-gray-200 dark:border-dark-border rounded-lg p-4"},st={key:0,class:"grid grid-cols-5 gap-4 mt-4"},ot=["src","alt"],at=["onClick"],dt={key:2,class:"space-y-4"},lt={class:"bg-gray-50 dark:bg-dark-card rounded-lg p-6"},nt={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},it={class:"text-sm font-medium text-gray-900 dark:text-dark-text mb-3"},ut={class:"max-h-64 overflow-y-auto space-y-2"},ct=["src","alt"],pt={class:"flex-1"},mt={class:"font-medium text-gray-900 dark:text-dark-text"},gt={class:"text-sm text-gray-500 dark:text-dark-text-secondary"},xt={class:"text-sm font-medium text-green-600 dark:text-green-400"},kt={key:0,class:"text-center p-4 text-gray-500 dark:text-dark-text-secondary"},vt={class:"text-sm font-medium text-gray-900 dark:text-dark-text mb-3"},bt={class:"grid grid-cols-3 gap-2 max-h-32 overflow-y-auto"},ht=["src","alt"],ft={class:"mt-6 pt-6 border-t border-gray-200 dark:border-dark-border"},yt={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},wt={class:"mt-6 pt-6 border-t border-gray-200 dark:border-dark-border"},_t={class:"grid grid-cols-4 gap-4 text-center"},Ct={class:"bg-white dark:bg-dark-surface p-4 rounded-lg border border-gray-200 dark:border-dark-border"},$t={class:"text-2xl font-bold text-orange-600 dark:text-orange-400"},Pt={class:"bg-white dark:bg-dark-surface p-4 rounded-lg border border-gray-200 dark:border-dark-border"},jt={class:"text-2xl font-bold text-purple-600 dark:text-purple-400"},Vt={class:"bg-white dark:bg-dark-surface p-4 rounded-lg border border-gray-200 dark:border-dark-border"},Bt={class:"text-2xl font-bold text-blue-600 dark:text-blue-400"},Mt={class:"bg-white dark:bg-dark-surface p-4 rounded-lg border border-gray-200 dark:border-dark-border"},Tt={class:"text-2xl font-bold text-green-600 dark:text-green-400"},Dt={class:"flex justify-between items-center"},zt={class:"flex items-center space-x-4"},St={key:1,class:"text-sm text-gray-500 dark:text-dark-text-secondary"},Lt={key:0,class:"text-green-600 dark:text-green-400"},It={key:1,class:"text-blue-600 dark:text-blue-400"},Ot={class:"flex space-x-3"},pe="pod-compose",Nt=me({__name:"CreatePodTaskDialog",props:{modelValue:{type:Boolean}},emits:["update:modelValue","success"],setup(Z,{emit:E}){const B=Z,L=E,S=_({get:()=>B.modelValue,set:c=>L("update:modelValue",c)}),b=k(1),$=k(!1),I=k(""),O=k(""),g=k([]),T=k("gallery"),v=k(""),D=k(""),j=k([]),s=k([]),o=k("个性化"),P=k(20),U=k([{id:"BP001",name:"纯棉圆领T恤",category:"服装",price:39.9,image:"https://picsum.photos/200/200?random=501"},{id:"BP002",name:"陶瓷马克杯",category:"家居",price:25.9,image:"https://picsum.photos/200/200?random=502"},{id:"BP003",name:"帆布手提袋",category:"配饰",price:29.9,image:"https://picsum.photos/200/200?random=503"},{id:"BP004",name:"iPhone手机壳",category:"配饰",price:19.9,image:"https://picsum.photos/200/200?random=504"},{id:"BP005",name:"方形抱枕",category:"家居",price:49.9,image:"https://picsum.photos/200/200?random=505"},{id:"BP006",name:"连帽卫衣",category:"服装",price:89.9,image:"https://picsum.photos/200/200?random=506"},{id:"BP007",name:"棒球帽",category:"配饰",price:35.9,image:"https://picsum.photos/200/200?random=507"},{id:"BP008",name:"保温杯",category:"家居",price:59.9,image:"https://picsum.photos/200/200?random=508"}]),N=k([{id:"PT001",name:"可爱猫咪",category:"animal",image:"https://picsum.photos/150/150?random=601"},{id:"PT002",name:"几何图案",category:"geometric",image:"https://picsum.photos/150/150?random=602"},{id:"PT003",name:"热带植物",category:"plant",image:"https://picsum.photos/150/150?random=603"},{id:"PT004",name:"励志文字",category:"text",image:"https://picsum.photos/150/150?random=604"},{id:"PT005",name:"抽象艺术",category:"geometric",image:"https://picsum.photos/150/150?random=605"},{id:"PT006",name:"卡通狗狗",category:"animal",image:"https://picsum.photos/150/150?random=606"},{id:"PT007",name:"花卉图案",category:"plant",image:"https://picsum.photos/150/150?random=607"},{id:"PT008",name:"英文字母",category:"text",image:"https://picsum.photos/150/150?random=608"}]),X=_(()=>{let c=U.value;return O.value&&(c=c.filter(t=>t.category===O.value)),I.value&&(c=c.filter(t=>t.name.toLowerCase().includes(I.value.toLowerCase()))),c}),Y=_(()=>{let c=N.value;return D.value&&(c=c.filter(t=>t.category===D.value)),v.value&&(c=c.filter(t=>t.name.toLowerCase().includes(v.value.toLowerCase()))),c}),p=_(()=>[...j.value,...s.value]),w=_(()=>j.value.length+s.value.length),se=_(()=>g.value.length===0?0:g.value.length*w.value*6),oe=_(()=>g.value.length===0?0:g.value.reduce((t,z)=>t+z.price,0)/g.value.length),ae=_(()=>g.value.length===0?0:(oe.value+P.value).toFixed(2)),ee=_(()=>b.value===1?g.value.length>0:b.value===2?w.value>0:!0),de=c=>{const t=g.value.findIndex(z=>z.id===c.id);t>-1?g.value.splice(t,1):g.value.push(c)},te=c=>g.value.some(t=>t.id===c),le=()=>{g.value=[]},ne=c=>{const t=j.value.findIndex(z=>z.id===c.id);t>-1?j.value.splice(t,1):j.value.push(c)},x=c=>{const t=new FileReader;t.onload=z=>{var M;s.value.push({name:c.name,url:(M=z.target)==null?void 0:M.result,file:c.raw})},t.readAsDataURL(c.raw)},r=c=>{s.value.splice(c,1)},h=()=>{},G=()=>{ee.value&&b.value++},R=()=>{b.value--},re=$e(pe),H=_(()=>g.value.length*w.value),ie=_(()=>H.value===0?0:Pe(pe,H.value)),J=_(()=>je(pe,H.value)),ue=_(()=>{if(H.value===0)return"创建任务";const c=ie.value;return c===0?"创建任务（免费）":`创建任务（¥${c.toFixed(2)}）`}),i=()=>{$.value=!0,console.log("选中的白品:",g.value),console.log("选中的图案:",[...j.value,...s.value]),console.log("预计生成商品数:",g.value.length*w.value),setTimeout(()=>{$.value=!1,V.success(`POD合成任务创建成功！将基于 ${g.value.length} 个白品和 ${w.value} 个图案生成 ${g.value.length*w.value} 个商品`),ce(),L("success"),L("update:modelValue",!1)},2e3)},ce=()=>{b.value=1,g.value=[],j.value=[],s.value=[],I.value="",O.value="",v.value="",D.value="",T.value="gallery",o.value="个性化",P.value=20};return(c,t)=>{const z=f("el-input"),M=f("el-option"),xe=f("el-select"),K=f("el-button"),be=f("el-upload"),he=f("el-input-number"),fe=f("el-dialog");return u(),Q(fe,{modelValue:S.value,"onUpdate:modelValue":t[9]||(t[9]=d=>S.value=d),title:"创建POD合成任务",width:"1000px","align-center":"",onClose:ce,class:"create-pod-dialog"},{footer:n(()=>[e("div",Dt,[e("div",zt,[b.value>1?(u(),Q(K,{key:0,onClick:R},{default:n(()=>t[37]||(t[37]=[C("上一步")])),_:1,__:[37]})):y("",!0),b.value===3&&H.value>0?(u(),m("div",St,[e("div",null,"将生成 "+a(H.value)+" 个POD商品",1),J.value.hasFreeQuota&&J.value.freeItems>0?(u(),m("div",Lt," 免费额度："+a(J.value.freeItems)+" 个，付费："+a(J.value.chargeableItems)+" 个 ",1)):y("",!0),ke(re)?(u(),m("div",It," 单价：¥"+a(ke(re).unitPrice.toFixed(2))+"/个商品 ",1)):y("",!0)])):y("",!0)]),e("div",Ot,[l(K,{onClick:t[8]||(t[8]=d=>S.value=!1)},{default:n(()=>t[38]||(t[38]=[C("取消")])),_:1,__:[38]}),b.value<3?(u(),Q(K,{key:0,type:"primary",onClick:G,disabled:!ee.value},{default:n(()=>t[39]||(t[39]=[C(" 下一步 ")])),_:1,__:[39]},8,["disabled"])):(u(),Q(K,{key:1,type:"primary",onClick:i,loading:$.value},{default:n(()=>[C(a($.value?"创建中...":ue.value),1)]),_:1},8,["loading"]))])])]),default:n(()=>[e("div",Ve,[e("div",Be,[e("div",Me,[e("div",{class:W(["w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium",b.value>=1?"bg-purple-500 text-white":"bg-gray-200 dark:bg-gray-700 text-gray-500"])}," 1 ",2),t[10]||(t[10]=e("span",{class:"ml-2 text-sm font-medium text-gray-900 dark:text-dark-text"},"选择白品",-1))]),t[13]||(t[13]=e("div",{class:"w-16 h-0.5 bg-gray-200 dark:bg-gray-700"},null,-1)),e("div",Te,[e("div",{class:W(["w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium",b.value>=2?"bg-purple-500 text-white":"bg-gray-200 dark:bg-gray-700 text-gray-500"])}," 2 ",2),t[11]||(t[11]=e("span",{class:"ml-2 text-sm font-medium text-gray-900 dark:text-dark-text"},"选择图案",-1))]),t[14]||(t[14]=e("div",{class:"w-16 h-0.5 bg-gray-200 dark:bg-gray-700"},null,-1)),e("div",De,[e("div",{class:W(["w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium",b.value>=3?"bg-purple-500 text-white":"bg-gray-200 dark:bg-gray-700 text-gray-500"])}," 3 ",2),t[12]||(t[12]=e("span",{class:"ml-2 text-sm font-medium text-gray-900 dark:text-dark-text"},"确认合成",-1))])]),b.value===1?(u(),m("div",ze,[e("div",Se,[t[17]||(t[17]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-dark-text"},"选择白品商品",-1)),e("div",Le,[t[15]||(t[15]=C(" 已选择 ")),e("span",Ie,a(g.value.length),1),t[16]||(t[16]=C(" 个白品 "))])]),e("div",Oe,[e("div",Ne,[l(z,{modelValue:I.value,"onUpdate:modelValue":t[0]||(t[0]=d=>I.value=d),placeholder:"搜索白品商品...",style:{width:"300px"},onInput:h},{prefix:n(()=>t[18]||(t[18]=[e("svg",{class:"w-4 h-4 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})],-1)])),_:1},8,["modelValue"]),l(xe,{modelValue:O.value,"onUpdate:modelValue":t[1]||(t[1]=d=>O.value=d),placeholder:"选择分类",style:{width:"150px"}},{default:n(()=>[l(M,{label:"全部分类",value:""}),l(M,{label:"服装",value:"clothing"}),l(M,{label:"配饰",value:"accessories"}),l(M,{label:"家居",value:"home"})]),_:1},8,["modelValue"]),g.value.length>0?(u(),Q(K,{key:0,onClick:le,size:"small",type:"danger",plain:""},{default:n(()=>t[19]||(t[19]=[C(" 清空选择 ")])),_:1,__:[19]})):y("",!0)]),e("div",Ue,[(u(!0),m(F,null,q(X.value,d=>(u(),m("div",{key:d.id,class:W(["border-2 rounded-lg p-3 cursor-pointer transition-all duration-200 hover:shadow-md relative",te(d.id)?"border-purple-500 bg-purple-50 dark:bg-purple-900/20":"border-gray-200 dark:border-dark-border hover:border-purple-300"]),onClick:A=>de(d)},[te(d.id)?(u(),m("div",Ae,t[20]||(t[20]=[e("svg",{class:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 13l4 4L19 7"})],-1)]))):y("",!0),e("img",{src:d.image,alt:d.name,class:"w-full h-32 object-cover rounded-lg mb-2"},null,8,Fe),e("h4",Ee,a(d.name),1),e("p",Ge,a(d.category),1),e("p",Re,"¥"+a(d.price),1)],10,He))),128))])])])):y("",!0),b.value===2?(u(),m("div",Ke,[t[27]||(t[27]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-dark-text"},"选择印刷图案",-1)),e("div",Qe,[l(K,{type:T.value==="gallery"?"primary":"default",onClick:t[2]||(t[2]=d=>T.value="gallery")},{default:n(()=>t[21]||(t[21]=[C(" 从图库选择 ")])),_:1,__:[21]},8,["type"]),l(K,{type:T.value==="upload"?"primary":"default",onClick:t[3]||(t[3]=d=>T.value="upload")},{default:n(()=>t[22]||(t[22]=[C(" 上传图片 ")])),_:1,__:[22]},8,["type"])]),T.value==="gallery"?(u(),m("div",We,[e("div",qe,[l(z,{modelValue:v.value,"onUpdate:modelValue":t[4]||(t[4]=d=>v.value=d),placeholder:"搜索图案...",style:{width:"300px"}},{prefix:n(()=>t[23]||(t[23]=[e("svg",{class:"w-4 h-4 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})],-1)])),_:1},8,["modelValue"]),l(xe,{modelValue:D.value,"onUpdate:modelValue":t[5]||(t[5]=d=>D.value=d),placeholder:"选择分类",style:{width:"150px"}},{default:n(()=>[l(M,{label:"全部分类",value:""}),l(M,{label:"动物",value:"animal"}),l(M,{label:"植物",value:"plant"}),l(M,{label:"几何",value:"geometric"}),l(M,{label:"文字",value:"text"})]),_:1},8,["modelValue"])]),e("div",Je,[(u(!0),m(F,null,q(Y.value,d=>(u(),m("div",{key:d.id,class:W(["border-2 rounded-lg p-2 cursor-pointer transition-all duration-200 hover:shadow-md",j.value.some(A=>A.id===d.id)?"border-purple-500 bg-purple-50 dark:bg-purple-900/20":"border-gray-200 dark:border-dark-border hover:border-purple-300"]),onClick:A=>ne(d)},[e("div",Ye,[e("img",{src:d.image,alt:d.name,class:"w-full h-24 object-cover rounded-lg mb-2"},null,8,Ze),j.value.some(A=>A.id===d.id)?(u(),m("div",et,t[24]||(t[24]=[e("svg",{class:"w-3 h-3 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 13l4 4L19 7"})],-1)]))):y("",!0)]),e("h4",tt,a(d.name),1)],10,Xe))),128))])])):y("",!0),T.value==="upload"?(u(),m("div",rt,[l(be,{ref:"uploadRef","file-list":s.value,"on-change":x,"auto-upload":!1,multiple:"",accept:"image/*",drag:"",class:"w-full"},{default:n(()=>t[25]||(t[25]=[e("div",{class:"text-center py-8"},[e("svg",{class:"w-12 h-12 text-gray-400 mx-auto mb-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"})]),e("p",{class:"text-gray-600 dark:text-dark-text-secondary"},"拖拽图片到此处或点击上传"),e("p",{class:"text-sm text-gray-500 dark:text-dark-text-secondary mt-1"},"支持 JPG、PNG 格式")],-1)])),_:1,__:[25]},8,["file-list"]),s.value.length>0?(u(),m("div",st,[(u(!0),m(F,null,q(s.value,(d,A)=>(u(),m("div",{key:A,class:"relative border border-gray-200 dark:border-dark-border rounded-lg p-2"},[e("img",{src:d.url,alt:d.name,class:"w-full h-24 object-cover rounded-lg"},null,8,ot),e("button",{onClick:Bs=>r(A),class:"absolute top-1 right-1 w-5 h-5 bg-red-500 text-white rounded-full flex items-center justify-center hover:bg-red-600"},t[26]||(t[26]=[e("svg",{class:"w-3 h-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]),8,at)]))),128))])):y("",!0)])):y("",!0)])):y("",!0),b.value===3?(u(),m("div",dt,[t[36]||(t[36]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-dark-text"},"确认合成信息",-1)),e("div",lt,[e("div",nt,[e("div",null,[e("h4",it," 选中的白品 ("+a(g.value.length)+") ",1),e("div",ut,[(u(!0),m(F,null,q(g.value,d=>(u(),m("div",{key:d.id,class:"flex items-center space-x-3 p-3 border border-gray-200 dark:border-dark-border rounded-lg"},[e("img",{src:d.image,alt:d.name,class:"w-16 h-16 object-cover rounded-lg"},null,8,ct),e("div",pt,[e("p",mt,a(d.name),1),e("p",gt,a(d.category),1),e("p",xt,"¥"+a(d.price),1)])]))),128)),g.value.length===0?(u(),m("div",kt," 未选择任何白品 ")):y("",!0)])]),e("div",null,[e("h4",vt,"选中的图案 ("+a(w.value)+")",1),e("div",bt,[(u(!0),m(F,null,q(p.value,d=>(u(),m("div",{key:"id"in d?d.id:d.name,class:"relative"},[e("img",{src:"image"in d?d.image:d.url,alt:d.name,class:"w-full h-16 object-cover rounded-lg"},null,8,ht)]))),128))])])]),e("div",ft,[t[30]||(t[30]=e("h4",{class:"text-sm font-medium text-gray-900 dark:text-dark-text mb-3"},"合成设置",-1)),e("div",yt,[e("div",null,[t[28]||(t[28]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text-secondary mb-2"},"商品名称前缀",-1)),l(z,{modelValue:o.value,"onUpdate:modelValue":t[6]||(t[6]=d=>o.value=d),placeholder:"如：个性化、定制"},null,8,["modelValue"])]),e("div",null,[t[29]||(t[29]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text-secondary mb-2"},"价格调整",-1)),l(he,{modelValue:P.value,"onUpdate:modelValue":t[7]||(t[7]=d=>P.value=d),min:0,precision:2,step:1,style:{width:"100%"}},null,8,["modelValue"])])])]),e("div",wt,[t[35]||(t[35]=e("h4",{class:"text-sm font-medium text-gray-900 dark:text-dark-text mb-3"},"预计结果",-1)),e("div",_t,[e("div",Ct,[e("p",$t,a(g.value.length),1),t[31]||(t[31]=e("p",{class:"text-sm text-gray-600 dark:text-dark-text-secondary"},"白品数量",-1))]),e("div",Pt,[e("p",jt,a(g.value.length*w.value),1),t[32]||(t[32]=e("p",{class:"text-sm text-gray-600 dark:text-dark-text-secondary"},"将生成商品数",-1))]),e("div",Vt,[e("p",Bt,a(se.value),1),t[33]||(t[33]=e("p",{class:"text-sm text-gray-600 dark:text-dark-text-secondary"},"预计SKU数",-1))]),e("div",Mt,[e("p",Tt,"¥"+a(ae.value),1),t[34]||(t[34]=e("p",{class:"text-sm text-gray-600 dark:text-dark-text-secondary"},"最终售价",-1))])])])])])):y("",!0)])]),_:1},8,["modelValue"])}}}),Ut=ge(Nt,[["__scopeId","data-v-e5d9a4ab"]]),Ht={class:"flex items-center justify-between p-6 border-b border-gray-100 dark:border-dark-border"},At={class:"flex items-center space-x-3"},Ft={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},Et={key:0,class:"p-6 grid grid-cols-2 md:grid-cols-4 gap-4"},Gt={class:"bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 p-4 rounded-xl border border-purple-200 dark:border-purple-800"},Rt={class:"flex items-center space-x-2"},Kt={class:"text-sm font-bold text-purple-900 dark:text-purple-100"},Qt={class:"bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 p-4 rounded-xl border border-blue-200 dark:border-blue-800"},Wt={class:"flex items-center space-x-2"},qt={class:"text-sm font-bold text-blue-900 dark:text-blue-100"},Jt={class:"bg-gradient-to-br from-emerald-50 to-emerald-100 dark:from-emerald-900/20 dark:to-emerald-800/20 p-4 rounded-xl border border-emerald-200 dark:border-emerald-800"},Xt={class:"flex items-center space-x-2"},Yt={class:"text-sm font-bold text-emerald-900 dark:text-emerald-100"},Zt={class:"bg-gradient-to-br from-amber-50 to-amber-100 dark:from-amber-900/20 dark:to-amber-800/20 p-4 rounded-xl border border-amber-200 dark:border-amber-800"},er={class:"flex items-center space-x-2"},tr={class:"text-sm font-bold text-amber-900 dark:text-amber-100"},rr={key:1,class:"px-6 pb-6 space-y-6"},sr={class:"flex items-center justify-between"},or={class:"flex space-x-3"},ar={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},dr={class:"bg-white dark:bg-dark-surface rounded-xl border border-gray-200 dark:border-dark-border overflow-hidden"},lr={class:"p-4"},nr={class:"flex items-center space-x-4 mb-4"},ir={class:"text-lg font-medium text-gray-900 dark:text-dark-text"},ur={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},cr={class:"grid grid-cols-2 gap-4 text-sm"},pr={class:"font-mono text-gray-900 dark:text-dark-text"},mr={class:"text-gray-900 dark:text-dark-text"},gr={class:"bg-white dark:bg-dark-surface rounded-xl border border-gray-200 dark:border-dark-border overflow-hidden"},xr={class:"p-4"},kr={class:"space-y-4"},vr={class:"text-xs text-gray-500 dark:text-dark-text-secondary mb-3"},br={class:"space-y-3 max-h-48 overflow-y-auto"},hr=["src","alt"],fr={class:"flex-1"},yr={class:"text-sm font-medium text-gray-900 dark:text-dark-text"},wr={class:"text-xs text-green-600 dark:text-green-400"},_r={class:"pt-4 border-t border-gray-100 dark:border-dark-border"},Cr={class:"text-xs text-gray-500 dark:text-dark-text-secondary mb-2"},$r={class:"grid grid-cols-3 gap-2"},Pr=["src","alt"],jr={key:0,class:"bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center h-16"},Vr={class:"text-xs text-gray-500 dark:text-gray-400"},Br={class:"bg-white dark:bg-dark-surface rounded-xl border border-gray-200 dark:border-dark-border overflow-hidden"},Mr={class:"overflow-x-auto"},Tr={class:"flex justify-center"},Dr={class:"text-sm font-medium text-gray-900 dark:text-dark-text"},zr={class:"text-xs text-gray-500 dark:text-dark-text-secondary"},Sr={class:"font-medium text-green-600 dark:text-green-400"},Lr={class:"font-medium text-gray-900 dark:text-dark-text"},Ir={class:"flex space-x-2"},Or=["onClick"],Nr=["onClick"],Ur={class:"flex items-center justify-between p-6 border-t border-gray-100 dark:border-dark-border bg-gray-50 dark:bg-dark-card/50"},Hr={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},Ar=me({__name:"ViewDetailsDialog",props:{modelValue:{type:Boolean},task:{}},emits:["update:modelValue"],setup(Z,{emit:E}){const B=Z,L=E,S=_({get:()=>B.modelValue,set:s=>L("update:modelValue",s)}),b=_(()=>B.task?Array.from({length:B.task.successCount},(s,o)=>({id:`POD${String(o+1).padStart(3,"0")}`,name:`${B.task.productName} - 图案${o+1}`,image:`https://picsum.photos/100/100?random=${800+o+1}`,price:"79.90",skuCount:6})):[]),$=()=>{S.value=!1},I=(s,o)=>o===1?[s]:s.includes("、")||s.includes(",")?s.split(/[、,]/).map(P=>P.trim()).filter(P=>P):Array.from({length:o},(P,U)=>`${s} ${U+1}`),O=s=>{const o={completed:"bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300",processing:"bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300",failed:"bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300",pending:"bg-gray-100 dark:bg-gray-900/30 text-gray-800 dark:text-gray-300"};return o[s]||o.pending},g=s=>({completed:"已完成",processing:"处理中",failed:"失败",pending:"等待中"})[s]||"未知",T=s=>{V.info(`查看POD商品: ${s.name}`)},v=s=>{V.success(`正在刊登POD商品: ${s.name}`)},D=()=>{var s;V.success(`正在批量刊登 ${((s=B.task)==null?void 0:s.successCount)||0} 个POD商品`)},j=()=>{var s;V.success(`正在下载 ${((s=B.task)==null?void 0:s.successCount)||0} 个商品图片`)};return(s,o)=>{const P=f("el-button"),U=f("el-image"),N=f("el-table-column"),X=f("el-table"),Y=f("el-dialog");return u(),Q(Y,{modelValue:S.value,"onUpdate:modelValue":o[0]||(o[0]=p=>S.value=p),width:"1200px","before-close":$,"show-close":!1,class:"modern-dialog"},{header:n(()=>{var p;return[e("div",Ht,[e("div",At,[o[2]||(o[2]=e("div",{class:"w-10 h-10 bg-gradient-to-br from-purple-500 to-pink-600 rounded-xl flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"})])],-1)),e("div",null,[o[1]||(o[1]=e("h3",{class:"text-xl font-bold text-gray-900 dark:text-dark-text"},"POD合成任务详情",-1)),e("p",Ft,"任务ID: "+a(((p=s.task)==null?void 0:p.id)||""),1)])]),e("button",{onClick:$,class:"p-2 text-gray-400 hover:text-gray-600 dark:text-dark-text-secondary dark:hover:text-dark-text rounded-lg hover:bg-gray-100 dark:hover:bg-dark-card transition-all duration-200"},o[3]||(o[3]=[e("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))])]}),footer:n(()=>{var p;return[e("div",Ur,[e("div",Hr," 共生成 "+a(((p=s.task)==null?void 0:p.successCount)||0)+" 个POD商品 ",1),e("div",{class:"flex items-center space-x-3"},[e("button",{onClick:$,class:"px-6 py-2.5 text-gray-700 dark:text-dark-text font-medium rounded-lg border border-gray-300 dark:border-dark-border hover:bg-gray-50 dark:hover:bg-dark-border transition-all duration-200"}," 关闭 "),e("button",{onClick:D,class:"inline-flex items-center px-6 py-2.5 bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white font-medium rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"},o[22]||(o[22]=[e("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"})],-1),C(" 批量刊登 ")]))])])]}),default:n(()=>[s.task?(u(),m("div",Et,[e("div",Gt,[e("div",Rt,[o[5]||(o[5]=e("div",{class:"w-8 h-8 bg-purple-500 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1)),e("div",null,[o[4]||(o[4]=e("p",{class:"text-xs text-purple-600 dark:text-purple-400 font-medium"},"任务状态",-1)),e("p",Kt,a(g(s.task.status)),1)])])]),e("div",Qt,[e("div",Wt,[o[7]||(o[7]=e("div",{class:"w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})])],-1)),e("div",null,[o[6]||(o[6]=e("p",{class:"text-xs text-blue-600 dark:text-blue-400 font-medium"},"合成数量",-1)),e("p",qt,a(s.task.baseCount)+" × "+a(s.task.patternCount),1)])])]),e("div",Jt,[e("div",Xt,[o[9]||(o[9]=e("div",{class:"w-8 h-8 bg-emerald-500 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1)),e("div",null,[o[8]||(o[8]=e("p",{class:"text-xs text-emerald-600 dark:text-emerald-400 font-medium"},"成功数量",-1)),e("p",Yt,a(s.task.successCount),1)])])]),e("div",Zt,[e("div",er,[o[11]||(o[11]=e("div",{class:"w-8 h-8 bg-amber-500 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})])],-1)),e("div",null,[o[10]||(o[10]=e("p",{class:"text-xs text-amber-600 dark:text-amber-400 font-medium"},"操作人",-1)),e("p",tr,a(s.task.operator),1)])])])])):y("",!0),s.task?(u(),m("div",rr,[e("div",sr,[o[14]||(o[14]=e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-dark-text"},"合成详情",-1)),e("div",or,[l(P,{type:"primary",size:"small",onClick:D},{default:n(()=>o[12]||(o[12]=[e("svg",{class:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"})],-1),C(" 批量刊登 ")])),_:1,__:[12]}),l(P,{size:"small",onClick:j},{default:n(()=>o[13]||(o[13]=[e("svg",{class:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"})],-1),C(" 下载图片 ")])),_:1,__:[13]})])]),e("div",ar,[e("div",dr,[o[17]||(o[17]=e("div",{class:"p-4 border-b border-gray-100 dark:border-dark-border"},[e("h4",{class:"text-sm font-medium text-gray-900 dark:text-dark-text"},"合成结果")],-1)),e("div",lr,[e("div",nr,[l(U,{src:s.task.resultImage,"preview-src-list":[s.task.resultImage],fit:"cover",class:"w-24 h-24 rounded-lg border border-gray-200 dark:border-dark-border","preview-teleported":!0},null,8,["src","preview-src-list"]),e("div",null,[e("h4",ir,a(s.task.productName),1),e("p",ur,"基于: "+a(s.task.baseProduct),1),e("span",{class:W([O(s.task.status),"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium mt-2"])},a(g(s.task.status)),3)])]),e("div",cr,[e("div",null,[o[15]||(o[15]=e("p",{class:"text-gray-500 dark:text-dark-text-secondary"},"任务ID",-1)),e("p",pr,a(s.task.id),1)]),e("div",null,[o[16]||(o[16]=e("p",{class:"text-gray-500 dark:text-dark-text-secondary"},"创建时间",-1)),e("p",mr,a(s.task.createTime),1)])])])]),e("div",gr,[o[18]||(o[18]=e("div",{class:"p-4 border-b border-gray-100 dark:border-dark-border"},[e("h4",{class:"text-sm font-medium text-gray-900 dark:text-dark-text"},"合成材料")],-1)),e("div",xr,[e("div",kr,[e("div",null,[e("p",vr,"基础商品 ("+a(s.task.baseCount)+")",1),e("div",br,[(u(!0),m(F,null,q(I(s.task.baseProduct,s.task.baseCount),(p,w)=>(u(),m("div",{key:w,class:"flex items-center space-x-3 p-3 bg-gray-50 dark:bg-gray-800/50 rounded-lg"},[e("img",{src:`https://picsum.photos/100/100?random=${701+w}`,alt:p,class:"w-12 h-12 rounded-lg object-cover"},null,8,hr),e("div",fr,[e("p",yr,a(p),1),e("p",wr,"白品 "+a(w+1),1)])]))),128))])]),e("div",_r,[e("p",Cr,"印刷图案 ("+a(s.task.patternCount)+")",1),e("div",$r,[(u(!0),m(F,null,q(Math.min(s.task.patternCount,6),p=>(u(),m("div",{key:p,class:"relative"},[e("img",{src:`https://picsum.photos/100/100?random=${700+p}`,alt:`图案${p}`,class:"w-full h-16 rounded-lg object-cover"},null,8,Pr)]))),128)),s.task.patternCount>6?(u(),m("div",jr,[e("span",Vr,"+"+a(s.task.patternCount-6),1)])):y("",!0)])])])])])]),e("div",Br,[o[21]||(o[21]=e("div",{class:"p-4 border-b border-gray-100 dark:border-dark-border"},[e("h4",{class:"text-sm font-medium text-gray-900 dark:text-dark-text"},"生成的POD商品")],-1)),e("div",Mr,[l(X,{data:b.value,style:{width:"100%"},"max-height":"400",class:"modern-table","header-cell-style":{backgroundColor:"var(--el-bg-color-page)",color:"var(--el-text-color-primary)",fontWeight:"600",borderBottom:"1px solid var(--el-border-color-light)"},"row-style":{backgroundColor:"transparent"}},{default:n(()=>[l(N,{label:"商品图片",width:"100"},{default:n(p=>[e("div",Tr,[l(U,{src:p.row.image,"preview-src-list":[p.row.image],fit:"cover",class:"w-12 h-12 rounded-lg border border-gray-200 dark:border-dark-border","preview-teleported":!0},null,8,["src","preview-src-list"])])]),_:1}),l(N,{prop:"name",label:"商品名称","min-width":"200"},{default:n(p=>[e("div",null,[e("p",Dr,a(p.row.name),1),e("p",zr,a(p.row.id),1)])]),_:1}),l(N,{prop:"price",label:"价格",width:"100"},{default:n(p=>[e("span",Sr,"¥"+a(p.row.price),1)]),_:1}),l(N,{prop:"skuCount",label:"SKU数量",width:"100"},{default:n(p=>[e("span",Lr,a(p.row.skuCount),1)]),_:1}),l(N,{label:"操作",width:"150"},{default:n(p=>[e("div",Ir,[e("button",{onClick:w=>T(p.row),class:"text-purple-600 hover:text-purple-700 transition-colors"},o[19]||(o[19]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"}),e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"})],-1)]),8,Or),e("button",{onClick:w=>v(p.row),class:"text-blue-600 hover:text-blue-700 transition-colors"},o[20]||(o[20]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"})],-1)]),8,Nr)])]),_:1})]),_:1},8,["data"])])])])):y("",!0)]),_:1},8,["modelValue"])}}}),Fr=ge(Ar,[["__scopeId","data-v-d37f4746"]]),Er={class:"space-y-6"},Gr={class:"grid grid-cols-1 md:grid-cols-4 gap-6"},Rr={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},Kr={class:"flex items-center justify-between"},Qr={class:"text-2xl font-bold text-gray-900 dark:text-dark-text"},Wr={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},qr={class:"flex items-center justify-between"},Jr={class:"text-2xl font-bold text-green-600 dark:text-green-400"},Xr={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},Yr={class:"flex items-center justify-between"},Zr={class:"text-2xl font-bold text-blue-600 dark:text-blue-400"},es={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},ts={class:"flex items-center justify-between"},rs={class:"text-2xl font-bold text-orange-600 dark:text-orange-400"},ss={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border"},os={class:"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0"},as={class:"flex items-center space-x-3"},ds={key:0,class:"flex items-center space-x-2 ml-4 pl-4 border-l border-gray-200 dark:border-dark-border"},ls={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},ns={class:"relative"},is={class:"bg-white dark:bg-dark-surface rounded-xl shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border overflow-hidden"},us={class:"overflow-x-auto"},cs={class:"font-mono text-sm text-gray-600 dark:text-dark-text-secondary"},ps={class:"flex items-center space-x-3"},ms={class:"font-medium text-gray-900 dark:text-dark-text"},gs={class:"text-xs text-gray-500 dark:text-dark-text-secondary mt-1"},xs={class:"flex items-center space-x-2"},ks={class:"font-medium text-gray-900 dark:text-dark-text"},vs={class:"font-medium text-purple-600 dark:text-purple-400"},bs={class:"text-sm font-medium text-green-600 dark:text-green-400"},hs={class:"flex items-center space-x-2"},fs={class:"w-6 h-6 bg-gradient-to-br from-purple-400 to-pink-500 rounded-full flex items-center justify-center"},ys={class:"text-white text-xs font-medium"},ws={class:"text-sm text-gray-900 dark:text-dark-text"},_s={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},Cs={class:"flex items-center space-x-2"},$s=["onClick"],Ps={class:"flex justify-between items-center px-6 py-4 border-t border-gray-100 dark:border-dark-border bg-gray-50 dark:bg-dark-card/50"},js={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},Vs=me({__name:"index",setup(Z){const E=k(!1),B=k(!1),L=k(!1),S=k(null),b=k([]),$=k(""),I=k(156),O=k(128),g=k(18),T=k(12),v=k({currentPage:1,pageSize:10,total:0}),D=k([{id:"POD001",productName:"个性化T恤 - 猫咪图案",baseProduct:"纯棉圆领T恤",baseCount:1,patternCount:1,status:"completed",successCount:6,resultImage:"https://picsum.photos/400/400?random=401",operator:"张三",createTime:"2024-01-15 14:30:25"},{id:"POD002",productName:"多品类合成 - 风景图案",baseProduct:"陶瓷马克杯、保温杯、方形抱枕",baseCount:3,patternCount:2,status:"processing",successCount:0,resultImage:"https://picsum.photos/400/400?random=402",operator:"李四",createTime:"2024-01-15 13:45:12"},{id:"POD003",productName:"艺术帆布包 - 抽象图案",baseProduct:"帆布手提袋",baseCount:1,patternCount:2,status:"completed",successCount:8,resultImage:"https://picsum.photos/400/400?random=403",operator:"王五",createTime:"2024-01-15 12:20:08"},{id:"POD004",productName:"服装系列合成 - 几何图案",baseProduct:"纯棉圆领T恤、连帽卫衣",baseCount:2,patternCount:1,status:"failed",successCount:0,resultImage:"https://picsum.photos/400/400?random=404",operator:"赵六",createTime:"2024-01-15 11:15:33"},{id:"POD005",productName:"定制抱枕 - 卡通图案",baseProduct:"方形抱枕",baseCount:1,patternCount:3,status:"completed",successCount:12,resultImage:"https://picsum.photos/400/400?random=405",operator:"钱七",createTime:"2024-01-15 10:30:45"},{id:"POD006",productName:"全品类合成 - 动物主题",baseProduct:"纯棉圆领T恤、陶瓷马克杯、帆布手提袋、iPhone手机壳、棒球帽",baseCount:5,patternCount:4,status:"completed",successCount:60,resultImage:"https://picsum.photos/400/400?random=406",operator:"孙八",createTime:"2024-01-15 09:15:20"},{id:"POD007",productName:"家居用品合成 - 简约风格",baseProduct:"陶瓷马克杯、保温杯、方形抱枕",baseCount:3,patternCount:2,status:"completed",successCount:18,resultImage:"https://picsum.photos/400/400?random=407",operator:"周九",createTime:"2024-01-15 08:30:15"}]),j=_(()=>{let x=D.value;$.value&&(x=x.filter(G=>G.id.toLowerCase().includes($.value.toLowerCase())||G.productName.toLowerCase().includes($.value.toLowerCase())||G.baseProduct.toLowerCase().includes($.value.toLowerCase())));const r=(v.value.currentPage-1)*v.value.pageSize,h=r+v.value.pageSize;return x.slice(r,h)});ye(()=>{s()});const s=()=>{E.value=!0,setTimeout(()=>{v.value.total=D.value.length,E.value=!1},500)},o=x=>{const r={completed:"bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300",processing:"bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300",failed:"bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300",pending:"bg-gray-100 dark:bg-gray-900/30 text-gray-800 dark:text-gray-300"};return r[x]||r.pending},P=x=>({completed:"已完成",processing:"处理中",failed:"失败",pending:"等待中"})[x]||"未知",U=x=>{b.value=x},N=()=>{v.value.currentPage=1,s()},X=x=>{S.value=x,L.value=!0},Y=x=>{const{action:r,row:h}=x;switch(r){case"smartCrop":p(h);break;case"oneClickCutout":w(h);break;case"superSplit":se(h);break;case"titleGenerate":oe(h);break;case"batchListing":ae(h);break}},p=x=>{V.success(`正在为POD合成任务 ${x.id} 创建智能裁图任务...`)},w=x=>{V.success(`正在为POD合成任务 ${x.id} 创建一键抠图任务...`)},se=x=>{V.success(`正在为POD合成任务 ${x.id} 创建超级裂变任务...`)},oe=x=>{V.success(`正在为POD合成任务 ${x.id} 创建标题生成任务...`)},ae=x=>{V.success(`正在为POD合成任务 ${x.id} 创建批量刊登任务...`)},ee=()=>{V.success("导出任务功能开发中...")},de=()=>{V.success(`正在批量导出 ${b.value.length} 个任务...`)},te=()=>{V.success("操作成功！"),s()},le=x=>{v.value.pageSize=x,v.value.currentPage=1,s()},ne=x=>{v.value.currentPage=x,s()};return(x,r)=>{const h=f("el-table-column"),G=f("el-image"),R=f("el-dropdown-item"),re=f("el-dropdown-menu"),H=f("el-dropdown"),ie=f("el-table"),J=f("el-pagination"),ue=Ce("loading");return u(),m(F,null,[e("div",Er,[r[28]||(r[28]=we('<div class="bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 rounded-2xl p-6 border border-purple-100 dark:border-purple-800" data-v-2e09f4d2><div class="flex items-center space-x-3" data-v-2e09f4d2><div class="w-10 h-10 bg-gradient-to-br from-purple-500 to-pink-600 rounded-xl flex items-center justify-center" data-v-2e09f4d2><svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-2e09f4d2><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" data-v-2e09f4d2></path></svg></div><div data-v-2e09f4d2><h1 class="text-2xl font-bold text-gray-900 dark:text-dark-text" data-v-2e09f4d2>POD合成</h1><p class="mt-1 text-sm text-gray-600 dark:text-dark-text-secondary" data-v-2e09f4d2>选择白品和图案，创建按需印刷商品</p></div></div></div>',1)),e("div",Gr,[e("div",Rr,[e("div",Kr,[e("div",null,[r[6]||(r[6]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"合成任务总数",-1)),e("p",Qr,a(I.value),1)]),r[7]||(r[7]=e("div",{class:"w-12 h-12 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-purple-600 dark:text-purple-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"})])],-1))])]),e("div",Wr,[e("div",qr,[e("div",null,[r[8]||(r[8]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"成功数量",-1)),e("p",Jr,a(O.value),1)]),r[9]||(r[9]=e("div",{class:"w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-green-600 dark:text-green-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1))])]),e("div",Xr,[e("div",Yr,[e("div",null,[r[10]||(r[10]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"今日新增",-1)),e("p",Zr,a(g.value),1)]),r[11]||(r[11]=e("div",{class:"w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-blue-600 dark:text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})])],-1))])]),e("div",es,[e("div",ts,[e("div",null,[r[12]||(r[12]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"处理中",-1)),e("p",rs,a(T.value),1)]),r[13]||(r[13]=e("div",{class:"w-12 h-12 bg-orange-100 dark:bg-orange-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-orange-600 dark:text-orange-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1))])])]),e("div",ss,[e("div",os,[e("div",as,[e("button",{onClick:r[0]||(r[0]=i=>B.value=!0),class:"inline-flex items-center px-4 py-2.5 bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700 text-white font-medium rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"},r[14]||(r[14]=[e("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),C(" 创建合成任务 ")])),e("button",{onClick:ee,class:"inline-flex items-center px-4 py-2.5 bg-white dark:bg-dark-card text-gray-700 dark:text-dark-text font-medium rounded-lg border border-gray-300 dark:border-dark-border hover:bg-gray-50 dark:hover:bg-dark-border shadow-sm hover:shadow-md transition-all duration-200"},r[15]||(r[15]=[e("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"})],-1),C(" 导出任务 ")])),b.value.length>0?(u(),m("div",ds,[e("span",ls," 已选择 "+a(b.value.length)+" 项 ",1),e("button",{onClick:de,class:"inline-flex items-center px-3 py-1.5 bg-purple-500 hover:bg-purple-600 text-white text-sm font-medium rounded-lg transition-all duration-200"},r[16]||(r[16]=[e("svg",{class:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"})],-1),C(" 批量导出 ")]))])):y("",!0)]),e("div",ns,[ve(e("input",{"onUpdate:modelValue":r[1]||(r[1]=i=>$.value=i),type:"text",placeholder:"搜索任务ID、商品名称...",class:"w-full sm:w-64 pl-10 pr-4 py-2 border border-gray-300 dark:border-dark-border rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 dark:bg-dark-card dark:text-dark-text",onInput:N},null,544),[[_e,$.value]]),r[17]||(r[17]=e("svg",{class:"w-5 h-5 text-gray-400 absolute left-3 top-2.5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})],-1))])])]),e("div",is,[r[27]||(r[27]=e("div",{class:"px-6 py-4 border-b border-gray-100 dark:border-dark-border"},[e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-dark-text"},"POD合成任务"),e("p",{class:"text-sm text-gray-600 dark:text-dark-text-secondary mt-1"},"管理您的POD合成任务")],-1)),e("div",us,[ve((u(),Q(ie,{data:j.value,style:{width:"100%"},onSelectionChange:U,class:"modern-table","header-cell-style":{backgroundColor:"var(--el-bg-color-page)",color:"var(--el-text-color-primary)",fontWeight:"600",borderBottom:"1px solid var(--el-border-color-light)"},"row-style":{backgroundColor:"transparent"}},{default:n(()=>[l(h,{type:"selection",width:"55"}),l(h,{prop:"id",label:"任务ID",width:"120"},{default:n(i=>[e("span",cs,a(i.row.id),1)]),_:1}),l(h,{label:"商品信息","min-width":"200"},{default:n(i=>[e("div",ps,[l(G,{src:i.row.resultImage,"preview-src-list":[i.row.resultImage],fit:"cover",class:"w-12 h-12 rounded-lg border border-gray-200 dark:border-dark-border","preview-teleported":!0},null,8,["src","preview-src-list"]),e("div",null,[e("div",ms,a(i.row.productName),1),e("div",gs," 基于: "+a(i.row.baseProduct),1)])])]),_:1}),l(h,{label:"合成信息",width:"150"},{default:n(i=>[e("div",xs,[r[18]||(r[18]=e("span",{class:"text-sm text-gray-600 dark:text-dark-text-secondary"},"白品:",-1)),e("span",ks,a(i.row.baseCount),1),r[19]||(r[19]=e("span",{class:"text-gray-400"},"|",-1)),r[20]||(r[20]=e("span",{class:"text-sm text-gray-600 dark:text-dark-text-secondary"},"图案:",-1)),e("span",vs,a(i.row.patternCount),1)])]),_:1}),l(h,{prop:"status",label:"合成状态",width:"120"},{default:n(i=>[e("span",{class:W([o(i.row.status),"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"])},a(P(i.row.status)),3)]),_:1}),l(h,{prop:"successCount",label:"成功数量",width:"100"},{default:n(i=>[e("span",bs,a(i.row.successCount),1)]),_:1}),l(h,{prop:"operator",label:"操作人",width:"100"},{default:n(i=>[e("div",hs,[e("div",fs,[e("span",ys,a(i.row.operator.charAt(0)),1)]),e("span",ws,a(i.row.operator),1)])]),_:1}),l(h,{prop:"createTime",label:"创建时间",width:"180"},{default:n(i=>[e("div",_s,a(i.row.createTime),1)]),_:1}),l(h,{label:"操作",width:"180"},{default:n(i=>[e("div",Cs,[e("button",{onClick:ce=>X(i.row),class:"inline-flex items-center px-3 py-1.5 text-sm font-medium text-purple-600 dark:text-purple-400 hover:text-purple-700 dark:hover:text-purple-300 bg-purple-50 dark:bg-purple-900/20 hover:bg-purple-100 dark:hover:bg-purple-900/30 rounded-lg transition-all duration-200"}," 查看详情 ",8,$s),l(H,{onCommand:Y,trigger:"click"},{dropdown:n(()=>[l(re,null,{default:n(()=>[l(R,{command:{action:"smartCrop",row:i.row}},{default:n(()=>r[21]||(r[21]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-purple-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m0 0V1a1 1 0 011-1h2a1 1 0 011 1v18a1 1 0 01-1 1H4a1 1 0 01-1-1V1a1 1 0 011-1h2a1 1 0 011 1v3m0 0h8m-8 0H4a1 1 0 00-1 1v3a1 1 0 001 1h3m0 0h10m0 0h3a1 1 0 001-1V5a1 1 0 00-1-1h-3m-3 0v8a1 1 0 01-1 1H8a1 1 0 01-1-1V4"})]),e("span",null,"智能裁图")],-1)])),_:2,__:[21]},1032,["command"]),l(R,{command:{action:"oneClickCutout",row:i.row}},{default:n(()=>r[22]||(r[22]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-pink-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})]),e("span",null,"一键抠图")],-1)])),_:2,__:[22]},1032,["command"]),l(R,{command:{action:"superSplit",row:i.row}},{default:n(()=>r[23]||(r[23]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-orange-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 10V3L4 14h7v7l9-11h-7z"})]),e("span",null,"超级裂变")],-1)])),_:2,__:[23]},1032,["command"]),l(R,{command:{action:"titleGenerate",row:i.row}},{default:n(()=>r[24]||(r[24]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-blue-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})]),e("span",null,"标题生成")],-1)])),_:2,__:[24]},1032,["command"]),l(R,{command:{action:"batchListing",row:i.row}},{default:n(()=>r[25]||(r[25]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-green-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"})]),e("span",null,"批量刊登")],-1)])),_:2,__:[25]},1032,["command"])]),_:2},1024)]),default:n(()=>[r[26]||(r[26]=e("button",{class:"inline-flex items-center px-2 py-1.5 text-sm font-medium text-gray-600 dark:text-dark-text-secondary hover:text-gray-700 dark:hover:text-dark-text bg-gray-50 dark:bg-dark-card hover:bg-gray-100 dark:hover:bg-dark-border rounded-lg transition-all duration-200"},[C(" 更多 "),e("svg",{class:"w-4 h-4 ml-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 9l-7 7-7-7"})])],-1))]),_:2,__:[26]},1024)])]),_:1})]),_:1},8,["data"])),[[ue,E.value]])]),e("div",Ps,[e("div",js," 共 "+a(v.value.total)+" 条记录 ",1),l(J,{"current-page":v.value.currentPage,"onUpdate:currentPage":r[2]||(r[2]=i=>v.value.currentPage=i),"page-size":v.value.pageSize,"onUpdate:pageSize":r[3]||(r[3]=i=>v.value.pageSize=i),"page-sizes":[10,20,50,100],total:v.value.total,layout:"sizes, prev, pager, next, jumper",onSizeChange:le,onCurrentChange:ne,class:"modern-pagination"},null,8,["current-page","page-size","total"])])])]),l(Ut,{modelValue:B.value,"onUpdate:modelValue":r[4]||(r[4]=i=>B.value=i),onSuccess:te},null,8,["modelValue"]),l(Fr,{modelValue:L.value,"onUpdate:modelValue":r[5]||(r[5]=i=>L.value=i),task:S.value},null,8,["modelValue","task"])],64)}}}),zs=ge(Vs,[["__scopeId","data-v-2e09f4d2"]]);export{zs as default};
