import{d as q,r as o,e as F,f as G,c as l,a as e,g as n,t as a,F as w,k as C,j as b,h as R,n as c,M as N,A as p,E as y,C as f,b as J,N as K,o as i}from"./index-84KJ6UvQ.js";import{r as V}from"./CreditCardIcon-CsWaKFOb.js";import{r as O}from"./CheckIcon-C62n6LJL.js";const Q={class:"p-8"},W={class:"max-w-4xl"},X={class:"bg-gradient-to-r from-amber-500 to-orange-500 rounded-2xl p-6 text-white mb-8"},Y={class:"flex items-center justify-between"},Z={class:"text-3xl font-bold"},ee={class:"text-right"},te={class:"font-mono text-lg"},se={class:"grid grid-cols-1 lg:grid-cols-2 gap-8"},ae={class:"bg-white dark:bg-dark-card rounded-lg p-6 shadow-sm"},re={class:"grid grid-cols-3 gap-3 mb-6"},oe=["onClick"],ne={class:"text-lg font-semibold"},de={class:"mb-6"},le={class:"mb-6"},ie={class:"space-y-3"},ue=["value"],ce=["src","alt"],me={class:"flex-1"},xe={class:"font-medium text-gray-900 dark:text-dark-text"},be={class:"text-sm text-gray-500 dark:text-dark-text-secondary"},ge={key:0,class:"w-5 h-5 bg-amber-500 rounded-full flex items-center justify-center"},ve=["disabled"],ke={class:"bg-white dark:bg-dark-card rounded-lg p-6 shadow-sm"},pe={class:"space-y-4 max-h-96 overflow-y-auto"},ye={class:"flex items-center"},fe={class:"font-medium text-gray-900 dark:text-dark-text"},_e={class:"text-sm text-gray-500 dark:text-dark-text-secondary"},he={class:"text-right"},we={key:0,class:"text-center py-8"},Ce={class:"text-center py-4"},Re={class:"w-16 h-16 bg-amber-100 dark:bg-amber-900/20 rounded-full flex items-center justify-center mx-auto mb-4"},Ve={class:"text-gray-600 dark:text-dark-text-secondary mb-4"},Me={class:"font-semibold text-amber-600 dark:text-amber-400"},je={class:"text-gray-600 dark:text-dark-text-secondary"},Te={class:"flex justify-center space-x-3"},Be=q({__name:"index",setup(Ae){const M=o(1234.56),B=o("ACC20240001"),m=o(0),g=o(null),d=o("alipay"),u=o(!1),v=o(!1),$=[50,100,200,500,1e3,2e3],j=[{id:"alipay",name:"支付宝",description:"推荐使用，到账快速",icon:"/payment-icons/alipay.png"},{id:"wechat",name:"微信支付",description:"便捷支付，安全可靠",icon:"/payment-icons/wechat.png"},{id:"bank",name:"银行卡",description:"支持各大银行卡",icon:"/payment-icons/bank.png"}],k=o([]),x=F(()=>g.value||m.value),_=F(()=>x.value>0&&d.value),E=r=>{r&&r>0&&(m.value=0)},T=r=>{const t=j.find(h=>h.id===r);return(t==null?void 0:t.name)||""},H=r=>({success:"成功",pending:"处理中",failed:"失败"})[r]||r,I=()=>{if(!_.value){y.warning("请选择充值金额和支付方式");return}u.value=!0},P=async()=>{v.value=!0;try{await new Promise(t=>setTimeout(t,2e3)),M.value+=x.value;const r={id:`R${Date.now()}`,amount:x.value,method:T(d.value),status:"success",createTime:new Date().toLocaleString()};k.value.unshift(r),m.value=0,g.value=null,u.value=!1,y.success("充值成功！")}catch{y.error("充值失败，请重试")}finally{v.value=!1}},S=()=>{v.value||(u.value=!1)},U=()=>{y.success("充值记录已刷新")},L=()=>{k.value=[{id:"R001",amount:500,method:"支付宝",status:"success",createTime:"2024-01-15 14:30:00"},{id:"R002",amount:200,method:"微信支付",status:"success",createTime:"2024-01-10 09:15:00"},{id:"R003",amount:100,method:"银行卡",status:"pending",createTime:"2024-01-08 16:45:00"}]};return G(()=>{L()}),(r,t)=>{const h=R("el-input"),A=R("el-button"),z=R("el-dialog");return i(),l("div",Q,[t[16]||(t[16]=e("div",{class:"mb-8"},[e("h2",{class:"text-2xl font-bold text-gray-900 dark:text-dark-text"},"账号充值"),e("p",{class:"mt-2 text-sm text-gray-600 dark:text-dark-text-secondary"},"为您的账号充值，享受更多服务")],-1)),e("div",W,[e("div",X,[e("div",Y,[e("div",null,[t[4]||(t[4]=e("h3",{class:"text-lg font-medium mb-2"},"当前余额",-1)),e("p",Z,"¥"+a(M.value.toFixed(2)),1)]),e("div",ee,[t[5]||(t[5]=e("p",{class:"text-amber-100 text-sm"},"账户ID",-1)),e("p",te,a(B.value),1)])])]),e("div",se,[e("div",ae,[t[9]||(t[9]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-dark-text mb-6"},"选择充值金额",-1)),e("div",re,[(i(),l(w,null,C($,s=>e("button",{key:s,onClick:D=>m.value=s,class:c(["p-4 text-center border-2 rounded-lg transition-all duration-200",[m.value===s?"border-amber-500 bg-amber-50 dark:bg-amber-900/20 text-amber-700 dark:text-amber-400":"border-gray-200 dark:border-dark-border hover:border-amber-300 dark:hover:border-amber-600 text-gray-700 dark:text-dark-text-secondary hover:text-amber-600 dark:hover:text-amber-400"]])},[e("div",ne,"¥"+a(s),1)],10,oe)),64))]),e("div",de,[t[7]||(t[7]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text-secondary mb-2"}," 自定义金额 ",-1)),n(h,{modelValue:g.value,"onUpdate:modelValue":t[0]||(t[0]=s=>g.value=s),modelModifiers:{number:!0},type:"number",placeholder:"请输入充值金额",min:1,max:1e4,onInput:E},{prefix:b(()=>t[6]||(t[6]=[f("¥")])),_:1},8,["modelValue"])]),e("div",le,[t[8]||(t[8]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text-secondary mb-3"}," 选择支付方式 ",-1)),e("div",ie,[(i(),l(w,null,C(j,s=>e("label",{key:s.id,class:c(["flex items-center p-3 border rounded-lg cursor-pointer transition-all duration-200",[d.value===s.id?"border-amber-500 bg-amber-50 dark:bg-amber-900/20":"border-gray-200 dark:border-dark-border hover:border-amber-300 dark:hover:border-amber-600"]])},[J(e("input",{type:"radio",value:s.id,"onUpdate:modelValue":t[1]||(t[1]=D=>d.value=D),class:"sr-only"},null,8,ue),[[K,d.value]]),e("img",{src:s.icon,alt:s.name,class:"w-8 h-8 mr-3"},null,8,ce),e("div",me,[e("div",xe,a(s.name),1),e("div",be,a(s.description),1)]),d.value===s.id?(i(),l("div",ge,[n(p(O),{class:"w-3 h-3 text-white"})])):N("",!0)],2)),64))])]),e("button",{onClick:I,disabled:!_.value,class:c(["w-full py-3 px-4 rounded-lg font-medium transition-all duration-200",_.value?"bg-amber-500 hover:bg-amber-600 text-white":"bg-gray-300 dark:bg-dark-border text-gray-500 dark:text-dark-text-secondary cursor-not-allowed"])}," 立即充值 ¥"+a(x.value.toFixed(2)),11,ve)]),e("div",ke,[e("div",{class:"flex items-center justify-between mb-6"},[t[10]||(t[10]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-dark-text"},"充值记录",-1)),e("button",{onClick:U,class:"text-sm text-amber-600 dark:text-amber-400 hover:text-amber-700 dark:hover:text-amber-300"}," 刷新 ")]),e("div",pe,[(i(!0),l(w,null,C(k.value,s=>(i(),l("div",{key:s.id,class:"flex items-center justify-between p-3 bg-gray-50 dark:bg-dark-border rounded-lg"},[e("div",ye,[e("div",{class:c(["w-10 h-10 rounded-full flex items-center justify-center mr-3",[s.status==="success"?"bg-green-100 dark:bg-green-900/20":s.status==="pending"?"bg-yellow-100 dark:bg-yellow-900/20":"bg-red-100 dark:bg-red-900/20"]])},[n(p(V),{class:c(["w-5 h-5",[s.status==="success"?"text-green-600 dark:text-green-400":s.status==="pending"?"text-yellow-600 dark:text-yellow-400":"text-red-600 dark:text-red-400"]])},null,8,["class"])],2),e("div",null,[e("div",fe," ¥"+a(s.amount.toFixed(2)),1),e("div",_e,a(s.method)+" · "+a(s.createTime),1)])]),e("div",he,[e("div",{class:c(["text-sm font-medium",[s.status==="success"?"text-green-600 dark:text-green-400":s.status==="pending"?"text-yellow-600 dark:text-yellow-400":"text-red-600 dark:text-red-400"]])},a(H(s.status)),3)])]))),128)),k.value.length===0?(i(),l("div",we,[n(p(V),{class:"w-12 h-12 text-gray-400 mx-auto mb-4"}),t[11]||(t[11]=e("p",{class:"text-gray-500 dark:text-dark-text-secondary"},"暂无充值记录",-1))])):N("",!0)])])])]),n(z,{modelValue:u.value,"onUpdate:modelValue":t[3]||(t[3]=s=>u.value=s),title:"确认充值",width:"400px","before-close":S},{footer:b(()=>[e("div",Te,[n(A,{onClick:t[2]||(t[2]=s=>u.value=!1)},{default:b(()=>t[14]||(t[14]=[f("取消")])),_:1,__:[14]}),n(A,{type:"primary",onClick:P,loading:v.value},{default:b(()=>t[15]||(t[15]=[f(" 确认支付 ")])),_:1,__:[15]},8,["loading"])])]),default:b(()=>[e("div",Ce,[e("div",Re,[n(p(V),{class:"w-8 h-8 text-amber-600 dark:text-amber-400"})]),t[13]||(t[13]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-dark-text mb-2"},"确认充值信息",-1)),e("p",Ve,[t[12]||(t[12]=f(" 充值金额：")),e("span",Me,"¥"+a(x.value.toFixed(2)),1)]),e("p",je," 支付方式："+a(T(d.value)),1)])]),_:1},8,["modelValue"])])}}});export{Be as default};
