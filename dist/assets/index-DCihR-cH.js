import{c as n,a as e,o as t,d as J,e as A,n as O,q as V,M as C,h as S,j as k,C as _,t as i,_ as q,w as Q,g,A as M,F as T,k as D,r as h,L as Ve,p as de,b as W,N as Ie,E as ee,x as xe,l as Me,H as Se,f as Ne,v as Te,U as le,s as me}from"./index-84KJ6UvQ.js";import{r as ve}from"./CheckCircleIcon-Jgkg8RrJ.js";import{r as be}from"./InformationCircleIcon-DvB6crIG.js";import{r as De}from"./CheckIcon-C62n6LJL.js";import{r as je}from"./ChartBarIcon-nW-6KFkg.js";import{r as Oe}from"./MagnifyingGlassIcon-AJjgLD-m.js";function ze(s,u){return t(),n("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M19.5 13.5 12 21m0 0-7.5-7.5M12 21V3"})])}function Ue(s,u){return t(),n("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M4.5 10.5 12 3m0 0 7.5 7.5M12 3v18"})])}function Fe(s,u){return t(),n("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m3.75 13.5 10.5-11.25L12 10.5h8.25L9.75 21.75 12 13.5H3.75Z"})])}function Ee(s,u){return t(),n("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M12 6v12m-3-2.818.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.45-.22-2.003-.659-1.106-.879-1.106-2.303 0-3.182s2.9-.879 4.006 0l.415.33M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"})])}function Be(s,u){return t(),n("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"}),e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"})])}function pe(s,u){return t(),n("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M21 8.25c0-2.485-2.099-4.5-4.688-4.5-1.935 0-3.597 1.126-4.312 2.733-.715-1.607-2.377-2.733-4.313-2.733C5.1 3.75 3 5.765 3 8.25c0 7.22 9 12 9 12s9-4.78 9-12Z"})])}function Pe(s,u){return t(),n("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m15.75 15.75-2.489-2.489m0 0a3.375 3.375 0 1 0-4.773-4.773 3.375 3.375 0 0 0 4.774 4.774ZM21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"})])}function Le(s,u){return t(),n("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L6.832 19.82a4.5 4.5 0 0 1-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 0 1 1.13-1.897L16.863 4.487Zm0 0L19.5 7.125"})])}function Re(s,u){return t(),n("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3.75 3v11.25A2.25 2.25 0 0 0 6 16.5h2.25M3.75 3h-1.5m1.5 0h16.5m0 0h1.5m-1.5 0v11.25A2.25 2.25 0 0 1 18 16.5h-2.25m-7.5 0h7.5m-7.5 0-1 3m8.5-3 1 3m0 0 .5 1.5m-.5-1.5h-9.5m0 0-.5 1.5m.75-9 3-3 2.148 2.148A12.061 12.061 0 0 1 16.5 7.605"})])}function oe(s,u){return t(),n("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M11.48 3.499a.562.562 0 0 1 1.04 0l2.125 5.111a.563.563 0 0 0 .475.345l5.518.442c.499.04.701.663.321.988l-4.204 3.602a.563.563 0 0 0-.182.557l1.285 5.385a.562.562 0 0 1-.84.61l-4.725-2.885a.562.562 0 0 0-.586 0L6.982 20.54a.562.562 0 0 1-.84-.61l1.285-5.386a.562.562 0 0 0-.182-.557l-4.204-3.602a.562.562 0 0 1 .321-.988l5.518-.442a.563.563 0 0 0 .475-.345L11.48 3.5Z"})])}const Ye={key:0,class:"flex items-center space-x-2"},He={key:1,class:"space-y-1"},Ge={class:"flex items-baseline space-x-2"},Ze={class:"price-text text-primary-600 dark:text-primary-400 font-bold"},Je={key:0,class:"original-price text-gray-400 dark:text-gray-500 line-through text-sm"},qe={key:2,class:"space-y-1"},Ke={class:"flex items-baseline space-x-2"},Qe={class:"price-text text-purple-600 dark:text-purple-400 font-bold"},We={class:"price-unit text-gray-500 dark:text-dark-text-secondary"},Xe={key:0,class:"original-price text-gray-400 dark:text-gray-500 line-through text-sm"},et={class:"price-desc text-gray-500 dark:text-dark-text-secondary"},tt={key:3,class:"space-y-1"},rt={class:"flex items-baseline space-x-2"},at={class:"price-text text-orange-600 dark:text-orange-400 font-bold"},st={class:"price-unit text-gray-500 dark:text-dark-text-secondary"},ot={class:"price-desc text-gray-500 dark:text-dark-text-secondary"},nt={key:4,class:"space-y-1"},it=J({__name:"PriceDisplay",props:{price:{},size:{},showDescription:{type:Boolean}},setup(s){const u=s,l=A(()=>{switch(u.size||"medium"){case"small":return"price-small";case"large":return"price-large";default:return"price-medium"}});return(a,r)=>{const p=S("el-tag");return t(),n("div",{class:O(["price-display",l.value])},[a.price.type==="free"?(t(),n("div",Ye,[r[1]||(r[1]=e("span",{class:"price-text text-green-600 dark:text-green-400 font-bold"}," 免费 ",-1)),a.size!=="small"?(t(),V(p,{key:0,type:"success",size:"small",effect:"plain"},{default:k(()=>r[0]||(r[0]=[_(" FREE ")])),_:1,__:[0]})):C("",!0)])):a.price.type==="one_time"?(t(),n("div",He,[e("div",Ge,[e("span",Ze," ¥"+i(a.price.amount),1),a.price.originalAmount&&a.price.originalAmount>a.price.amount?(t(),n("span",Je," ¥"+i(a.price.originalAmount),1)):C("",!0)]),r[2]||(r[2]=e("div",{class:"price-desc text-gray-500 dark:text-dark-text-secondary"}," 一次购买，终身使用 ",-1)),a.price.originalAmount&&a.price.originalAmount>a.price.amount&&a.size!=="small"?(t(),V(p,{key:0,type:"danger",size:"small",effect:"plain",class:"discount-tag"},{default:k(()=>[_(i(Math.round((1-a.price.amount/a.price.originalAmount)*100))+"% OFF ",1)]),_:1})):C("",!0)])):a.price.type==="monthly"?(t(),n("div",qe,[e("div",Ke,[e("span",Qe," ¥"+i(a.price.amount),1),e("span",We,i(a.price.unit||"/月"),1),a.price.originalAmount&&a.price.originalAmount>a.price.amount?(t(),n("span",Xe," ¥"+i(a.price.originalAmount),1)):C("",!0)]),e("div",et,i(a.price.description||"按月订阅，随时取消"),1),a.price.originalAmount&&a.price.originalAmount>a.price.amount&&a.size!=="small"?(t(),V(p,{key:0,type:"warning",size:"small",effect:"plain",class:"discount-tag"},{default:k(()=>r[3]||(r[3]=[_(" 限时优惠 ")])),_:1,__:[3]})):C("",!0)])):a.price.type==="per_use"?(t(),n("div",tt,[e("div",rt,[e("span",at," ¥"+i(a.price.amount),1),e("span",st,i(a.price.unit||"/次"),1)]),e("div",ot,i(a.price.description||"按使用次数计费"),1),a.size!=="small"?(t(),V(p,{key:0,type:"info",size:"small",effect:"plain"},{default:k(()=>r[4]||(r[4]=[_(" 按需付费 ")])),_:1,__:[4]})):C("",!0)])):(t(),n("div",nt,r[5]||(r[5]=[e("span",{class:"price-text text-gray-600 dark:text-gray-400"}," 价格待定 ",-1)])))],2)}}}),ce=q(it,[["__scopeId","data-v-470f89ed"]]),lt={class:"relative p-6"},dt={class:"flex justify-center mb-4"},pt={class:"w-20 h-20 bg-gradient-to-br from-primary-500 to-blue-500 rounded-2xl flex items-center justify-center text-3xl font-bold text-white shadow-lg transform group-hover:scale-105 transition-transform duration-300"},ut={class:"text-center mb-4"},ct={class:"flex items-center justify-center space-x-2 mb-2"},gt={class:"text-xl font-bold text-gray-900 dark:text-dark-text"},mt={class:"text-sm text-gray-500 dark:text-dark-text-secondary mb-3"},vt={class:"flex items-center justify-center space-x-2 mb-3"},yt={class:"flex items-center"},ft={class:"text-sm font-medium text-gray-700 dark:text-dark-text"},kt={class:"text-xs text-gray-500 dark:text-dark-text-secondary"},ht={class:"mb-4"},xt={class:"text-sm text-gray-600 dark:text-dark-text-secondary line-clamp-2 leading-relaxed text-center"},bt={class:"mb-6"},wt={class:"flex flex-wrap justify-center gap-2"},_t={class:"px-6 pb-6"},$t={class:"text-center mb-4"},Ct={class:"flex flex-col space-y-2"},At={class:"flex space-x-2"},Vt=J({__name:"AppCard",props:{app:{}},emits:["click","favorite-toggle","install-toggle","purchase"],setup(s){const u=s,l=()=>{switch(u.app.price.type){case"free":return"安装";case"one_time":return"购买";case"monthly":return"订阅";case"per_use":return"充值";default:return"安装"}},a=r=>r>=1e4?`${(r/1e4).toFixed(1)}万`:r>=1e3?`${(r/1e3).toFixed(1)}k`:r.toString();return(r,p)=>{const I=S("el-tag");return t(),n("div",{class:"app-card group relative bg-white dark:bg-dark-surface rounded-2xl shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-xl dark:hover:shadow-2xl transition-all duration-300 cursor-pointer overflow-hidden transform hover:-translate-y-1",onClick:p[5]||(p[5]=f=>r.$emit("click",r.app))},[p[9]||(p[9]=e("div",{class:"absolute top-0 left-0 right-0 h-24 bg-gradient-to-br from-primary-500/10 to-blue-500/10 dark:from-primary-400/20 dark:to-blue-400/20"},null,-1)),e("button",{onClick:p[0]||(p[0]=Q(f=>r.$emit("favorite-toggle",r.app.id),["stop"])),class:O(["absolute top-4 right-4 z-10 p-2 rounded-full bg-white/90 dark:bg-dark-surface/90 backdrop-blur-md shadow-lg border border-white/20 dark:border-dark-border/50 hover:bg-white dark:hover:bg-dark-surface transition-all duration-200 group/fav",r.app.isFavorited?"text-red-500 hover:text-red-600":"text-gray-400 hover:text-red-500"])},[g(M(pe),{class:O(["w-4 h-4 transition-transform duration-200 group-hover/fav:scale-110",r.app.isFavorited?"fill-current":""])},null,8,["class"])],2),e("div",lt,[e("div",dt,[e("div",pt,i(r.app.icon),1)]),e("div",ut,[e("div",ct,[e("h3",gt,i(r.app.name),1),r.app.status==="maintenance"?(t(),V(I,{key:0,type:"warning",size:"small",effect:"light",round:""},{default:k(()=>p[6]||(p[6]=[_(" 维护中 ")])),_:1,__:[6]})):r.app.status==="deprecated"?(t(),V(I,{key:1,type:"danger",size:"small",effect:"light",round:""},{default:k(()=>p[7]||(p[7]=[_(" 已废弃 ")])),_:1,__:[7]})):C("",!0)]),e("p",mt,i(r.app.developer),1),e("div",vt,[e("div",yt,[(t(),n(T,null,D(5,f=>g(M(oe),{key:f,class:O(["w-4 h-4",f<=Math.floor(r.app.rating)?"text-yellow-400 fill-current":"text-gray-300 dark:text-gray-600"])},null,8,["class"])),64))]),e("span",ft,i(r.app.rating),1),e("span",kt," ("+i(a(r.app.downloadCount))+") ",1)])]),e("div",ht,[e("p",xt,i(r.app.description),1)]),e("div",bt,[e("div",wt,[(t(!0),n(T,null,D(r.app.tags.slice(0,3),f=>(t(),V(I,{key:f,size:"small",effect:"light",round:"",class:"text-xs"},{default:k(()=>[_(i(f),1)]),_:2},1024))),128)),r.app.tags.length>3?(t(),V(I,{key:0,size:"small",effect:"plain",round:"",class:"text-xs"},{default:k(()=>[_(" +"+i(r.app.tags.length-3),1)]),_:1})):C("",!0)])])]),e("div",_t,[e("div",$t,[g(ce,{price:r.app.price,size:"medium"},null,8,["price"])]),e("div",Ct,[r.app.isInstalled?(t(),n("button",{key:1,onClick:p[2]||(p[2]=Q(f=>r.$emit("install-toggle",r.app.id),["stop"])),class:"w-full py-3 px-4 bg-green-500 hover:bg-green-600 text-white font-medium rounded-xl transition-all duration-200 flex items-center justify-center space-x-2"},p[8]||(p[8]=[e("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 13l4 4L19 7"})],-1),e("span",null,"已安装",-1)]))):(t(),n("button",{key:0,onClick:p[1]||(p[1]=Q(f=>r.$emit("purchase",r.app),["stop"])),class:"w-full py-3 px-4 bg-gradient-to-r from-primary-500 to-blue-500 hover:from-primary-600 hover:to-blue-600 text-white font-medium rounded-xl transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl"},i(l()),1)),e("div",At,[e("button",{onClick:p[3]||(p[3]=Q(f=>r.$emit("click",r.app),["stop"])),class:"flex-1 py-2 px-3 text-sm font-medium text-gray-600 dark:text-dark-text-secondary hover:text-primary-600 dark:hover:text-primary-400 bg-gray-100 dark:bg-dark-card hover:bg-gray-200 dark:hover:bg-dark-border rounded-lg transition-all duration-200"}," 详情 "),e("button",{onClick:p[4]||(p[4]=Q(f=>r.$emit("favorite-toggle",r.app.id),["stop"])),class:O(["px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200",r.app.isFavorited?"text-red-500 bg-red-50 dark:bg-red-900/20 hover:bg-red-100 dark:hover:bg-red-900/30":"text-gray-500 dark:text-dark-text-secondary bg-gray-100 dark:bg-dark-card hover:bg-gray-200 dark:hover:bg-dark-border hover:text-red-500"])},[g(M(pe),{class:O(["w-4 h-4",r.app.isFavorited?"fill-current":""])},null,8,["class"])],2)])])]),p[10]||(p[10]=e("div",{class:"absolute inset-0 bg-gradient-to-t from-primary-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none rounded-2xl"},null,-1))])}}}),It=q(Vt,[["__scopeId","data-v-2764c877"]]),Mt={class:"app-grid-container"},St={key:0,class:"loading-container"},Nt={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"},Tt={key:1,class:"apps-container"},Dt={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"},jt={key:0,class:"pagination-container mt-8 flex justify-center"},Ot={key:2,class:"empty-state"},zt={class:"text-center py-16"},Ut={class:"text-lg font-medium text-gray-900 dark:text-dark-text mb-2"},Ft={class:"text-gray-500 dark:text-dark-text-secondary max-w-md mx-auto"},Et={class:"mt-6"},Bt=J({__name:"AppGrid",props:{apps:{},loading:{type:Boolean},emptyTitle:{},emptyDescription:{}},emits:["app-click","favorite-toggle","install-toggle","purchase","clear-filters"],setup(s){const u=s,l=h(1),a=h(12),r=A(()=>Math.ceil(u.apps.length/a.value)),p=A(()=>{const w=(l.value-1)*a.value,x=w+a.value;return u.apps.slice(w,x)}),I=A(()=>u.emptyTitle||"暂无应用"),f=A(()=>u.emptyDescription||"当前筛选条件下没有找到相关应用，请尝试调整筛选条件或搜索关键词。"),j=w=>{a.value=w,l.value=1},F=w=>{l.value=w,window.scrollTo({top:0,behavior:"smooth"})};return Ve(()=>u.apps.length,()=>{l.value=1}),(w,x)=>{const E=S("el-pagination"),B=S("el-button"),y=S("el-backtop");return t(),n("div",Mt,[w.loading?(t(),n("div",St,[e("div",Nt,[(t(),n(T,null,D(8,d=>e("div",{key:d,class:"app-card-skeleton bg-white dark:bg-dark-surface rounded-xl border border-gray-200 dark:border-dark-border overflow-hidden"},x[6]||(x[6]=[de('<div class="p-6 space-y-4" data-v-ff91018e><div class="flex items-start space-x-4" data-v-ff91018e><div class="w-16 h-16 bg-gray-200 dark:bg-gray-700 rounded-xl animate-pulse" data-v-ff91018e></div><div class="flex-1 space-y-2" data-v-ff91018e><div class="h-5 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" data-v-ff91018e></div><div class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 animate-pulse" data-v-ff91018e></div></div></div><div class="space-y-2" data-v-ff91018e><div class="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" data-v-ff91018e></div><div class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-5/6 animate-pulse" data-v-ff91018e></div></div><div class="flex space-x-2" data-v-ff91018e><div class="h-6 w-16 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" data-v-ff91018e></div><div class="h-6 w-12 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" data-v-ff91018e></div></div></div><div class="px-6 py-4 bg-gray-50 dark:bg-dark-card/50 border-t border-gray-100 dark:border-dark-border" data-v-ff91018e><div class="flex items-center justify-between" data-v-ff91018e><div class="h-6 w-20 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" data-v-ff91018e></div><div class="h-8 w-16 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" data-v-ff91018e></div></div></div>',2)]))),64))])])):w.apps.length>0?(t(),n("div",Tt,[e("div",Dt,[(t(!0),n(T,null,D(p.value,d=>(t(),V(It,{key:d.id,app:d,onClick:o=>w.$emit("app-click",d),onFavoriteToggle:x[0]||(x[0]=o=>w.$emit("favorite-toggle",o)),onInstallToggle:x[1]||(x[1]=o=>w.$emit("install-toggle",o)),onPurchase:x[2]||(x[2]=o=>w.$emit("purchase",o))},null,8,["app","onClick"]))),128))]),r.value>1?(t(),n("div",jt,[g(E,{"current-page":l.value,"onUpdate:currentPage":x[3]||(x[3]=d=>l.value=d),"page-size":a.value,"onUpdate:pageSize":x[4]||(x[4]=d=>a.value=d),"page-sizes":[12,24,36,48],total:w.apps.length,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:j,onCurrentChange:F,class:"modern-pagination"},null,8,["current-page","page-size","total"])])):C("",!0)])):(t(),n("div",Ot,[e("div",zt,[x[8]||(x[8]=e("div",{class:"w-24 h-24 mx-auto mb-6 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center"},[e("svg",{class:"w-12 h-12 text-gray-400 dark:text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2M4 13h2m13-8V4a1 1 0 00-1-1H7a1 1 0 00-1 1v1m8 0V4.5"})])],-1)),e("h3",Ut,i(I.value),1),e("p",Ft,i(f.value),1),e("div",Et,[g(B,{onClick:x[5]||(x[5]=d=>w.$emit("clear-filters")),type:"primary",plain:""},{default:k(()=>x[7]||(x[7]=[_(" 清除筛选条件 ")])),_:1,__:[7]})])])])),g(y,{right:40,bottom:40,"visibility-height":300})])}}}),ye=q(Bt,[["__scopeId","data-v-ff91018e"]]),Pt={key:0,class:"purchase-content"},Lt={class:"app-info flex items-center space-x-4 mb-6 p-4 bg-gray-50 dark:bg-dark-card rounded-lg"},Rt={class:"w-16 h-16 bg-gradient-to-br from-primary-100 to-primary-200 dark:from-primary-900/30 dark:to-primary-800/30 rounded-xl flex items-center justify-center text-2xl font-bold border border-primary-200 dark:border-primary-700"},Yt={class:"flex-1"},Ht={class:"text-lg font-semibold text-gray-900 dark:text-dark-text"},Gt={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},Zt={class:"price-section mb-6"},Jt={class:"bg-white dark:bg-dark-surface border border-gray-200 dark:border-dark-border rounded-lg p-4"},qt={key:0,class:"mt-4 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg"},Kt={class:"flex items-center space-x-2"},Qt={key:1,class:"mt-4 space-y-2"},Wt={class:"flex items-center justify-between text-sm"},Xt={class:"font-medium text-gray-900 dark:text-dark-text"},er={key:0,class:"flex items-center justify-between text-sm"},tr={class:"text-gray-400 line-through"},rr={class:"flex items-center justify-between text-sm font-medium border-t border-gray-200 dark:border-dark-border pt-2"},ar={class:"text-lg text-primary-600 dark:text-primary-400"},sr={key:2,class:"mt-4 space-y-3"},or={class:"p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg"},nr={class:"flex items-center space-x-2 mb-2"},ir={class:"flex items-center justify-between text-sm font-medium border-t border-gray-200 dark:border-dark-border pt-2"},lr={class:"text-lg text-primary-600 dark:text-primary-400"},dr={key:3,class:"mt-4 space-y-3"},pr={class:"p-3 bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-lg"},ur={class:"flex items-center space-x-2 mb-2"},cr={class:"space-y-2"},gr={class:"flex items-center justify-between text-sm"},mr={class:"font-medium text-gray-900 dark:text-dark-text"},vr={class:"flex items-center justify-between text-sm"},yr={class:"flex items-center justify-between text-sm"},fr={class:"font-medium text-gray-900 dark:text-dark-text"},kr={key:0,class:"payment-section mb-6"},hr={class:"space-y-2"},xr=["value"],br={class:"flex items-center space-x-3 flex-1"},wr={class:"text-2xl"},_r={class:"font-medium text-gray-900 dark:text-dark-text"},$r={class:"text-sm text-gray-500 dark:text-dark-text-secondary"},Cr={key:0,class:"text-primary-600 dark:text-primary-400"},Ar={key:1,class:"agreement-section mb-6"},Vr={class:"flex items-start space-x-3 cursor-pointer"},Ir={class:"flex justify-end space-x-3"},Mr=J({__name:"PurchaseDialog",props:{modelValue:{type:Boolean},app:{}},emits:["update:modelValue","purchase-success"],setup(s,{emit:u}){const l=s,a=u,r=h(!1),p=h("alipay"),I=h(!1),f=h(50),j=A({get:()=>l.modelValue,set:y=>a("update:modelValue",y)}),F=A(()=>l.app?l.app.price.type==="free"?!0:I.value&&p.value:!1),w=h([{id:"alipay",name:"支付宝",icon:"💙",description:"推荐使用，安全快捷"},{id:"wechat",name:"微信支付",icon:"💚",description:"微信扫码支付"},{id:"unionpay",name:"银联支付",icon:"💳",description:"银行卡支付"}]),x=()=>{if(!l.app)return"购买应用";switch(l.app.price.type){case"free":return"安装免费应用";case"one_time":return"购买应用";case"monthly":return"订阅应用";case"per_use":return"充值使用";default:return"购买应用"}},E=()=>{if(!l.app)return"确认";switch(l.app.price.type){case"free":return"立即安装";case"one_time":return`立即购买 ¥${l.app.price.amount}`;case"monthly":return`立即订阅 ¥${l.app.price.amount}/月`;case"per_use":return`充值 ¥${f.value}`;default:return"确认购买"}},B=async()=>{if(!(!l.app||!F.value)){r.value=!0;try{await new Promise(y=>setTimeout(y,2e3)),l.app.price.type==="free"?ee.success(`${l.app.name} 安装成功！`):ee.success(`${l.app.name} 购买成功！`),a("purchase-success",l.app.id),j.value=!1}catch{ee.error("操作失败，请重试")}finally{r.value=!1}}};return(y,d)=>{const o=S("el-input-number"),v=S("el-checkbox"),L=S("el-button"),R=S("el-dialog");return t(),V(R,{modelValue:j.value,"onUpdate:modelValue":d[4]||(d[4]=$=>j.value=$),title:x(),width:"500px","align-center":"","close-on-click-modal":!1,class:"purchase-dialog"},{footer:k(()=>[e("div",Ir,[g(L,{onClick:d[3]||(d[3]=$=>j.value=!1),size:"large"},{default:k(()=>d[20]||(d[20]=[_(" 取消 ")])),_:1,__:[20]}),g(L,{onClick:B,type:"primary",size:"large",loading:r.value,disabled:!F.value},{default:k(()=>[_(i(E()),1)]),_:1},8,["loading","disabled"])])]),default:k(()=>[y.app?(t(),n("div",Pt,[e("div",Lt,[e("div",Rt,i(y.app.icon),1),e("div",Yt,[e("h3",Ht,i(y.app.name),1),e("p",Gt,i(y.app.developer),1)])]),e("div",Zt,[d[17]||(d[17]=e("h4",{class:"text-base font-medium text-gray-900 dark:text-dark-text mb-3"}," 价格详情 ",-1)),e("div",Jt,[g(ce,{price:y.app.price,size:"large"},null,8,["price"]),y.app.price.type==="free"?(t(),n("div",qt,[e("div",Kt,[g(M(ve),{class:"w-5 h-5 text-green-600 dark:text-green-400"}),d[5]||(d[5]=e("span",{class:"text-sm text-green-800 dark:text-green-300"}," 此应用完全免费，无需付费即可使用所有功能 ",-1))])])):y.app.price.type==="one_time"?(t(),n("div",Qt,[e("div",Wt,[d[6]||(d[6]=e("span",{class:"text-gray-600 dark:text-dark-text-secondary"},"应用价格",-1)),e("span",Xt,"¥"+i(y.app.price.amount),1)]),y.app.price.originalAmount&&y.app.price.originalAmount>y.app.price.amount?(t(),n("div",er,[d[7]||(d[7]=e("span",{class:"text-gray-600 dark:text-dark-text-secondary"},"原价",-1)),e("span",tr,"¥"+i(y.app.price.originalAmount),1)])):C("",!0),e("div",rr,[d[8]||(d[8]=e("span",{class:"text-gray-900 dark:text-dark-text"},"总计",-1)),e("span",ar,"¥"+i(y.app.price.amount),1)])])):y.app.price.type==="monthly"?(t(),n("div",sr,[e("div",or,[e("div",nr,[g(M(be),{class:"w-5 h-5 text-blue-600 dark:text-blue-400"}),d[9]||(d[9]=e("span",{class:"text-sm font-medium text-blue-800 dark:text-blue-300"},"订阅说明",-1))]),d[10]||(d[10]=e("ul",{class:"text-sm text-blue-700 dark:text-blue-300 space-y-1"},[e("li",null,"• 按月自动续费，可随时取消"),e("li",null,"• 享受所有高级功能"),e("li",null,"• 优先技术支持"),e("li",null,"• 无使用次数限制")],-1))]),e("div",ir,[d[11]||(d[11]=e("span",{class:"text-gray-900 dark:text-dark-text"},"月费",-1)),e("span",lr,"¥"+i(y.app.price.amount)+"/月",1)])])):y.app.price.type==="per_use"?(t(),n("div",dr,[e("div",pr,[e("div",ur,[g(M(Ee),{class:"w-5 h-5 text-orange-600 dark:text-orange-400"}),d[12]||(d[12]=e("span",{class:"text-sm font-medium text-orange-800 dark:text-orange-300"},"按需付费",-1))]),d[13]||(d[13]=e("p",{class:"text-sm text-orange-700 dark:text-orange-300"}," 根据实际使用次数计费，用多少付多少，经济实惠 ",-1))]),e("div",cr,[e("div",gr,[d[14]||(d[14]=e("span",{class:"text-gray-600 dark:text-dark-text-secondary"},"单次使用费用",-1)),e("span",mr,"¥"+i(y.app.price.amount),1)]),e("div",vr,[d[15]||(d[15]=e("span",{class:"text-gray-600 dark:text-dark-text-secondary"},"预充值金额",-1)),g(o,{modelValue:f.value,"onUpdate:modelValue":d[0]||(d[0]=$=>f.value=$),min:10,max:1e3,step:10,size:"small",style:{width:"120px"}},null,8,["modelValue"])]),e("div",yr,[d[16]||(d[16]=e("span",{class:"text-gray-600 dark:text-dark-text-secondary"},"可使用次数",-1)),e("span",fr," 约 "+i(Math.floor(f.value/y.app.price.amount))+" 次 ",1)])])])):C("",!0)])]),y.app.price.type!=="free"?(t(),n("div",kr,[d[18]||(d[18]=e("h4",{class:"text-base font-medium text-gray-900 dark:text-dark-text mb-3"}," 支付方式 ",-1)),e("div",hr,[(t(!0),n(T,null,D(w.value,$=>(t(),n("label",{key:$.id,class:O(["flex items-center p-3 border border-gray-200 dark:border-dark-border rounded-lg cursor-pointer hover:border-primary-300 dark:hover:border-primary-600 transition-colors",p.value===$.id?"border-primary-500 bg-primary-50 dark:bg-primary-900/20":""])},[W(e("input",{"onUpdate:modelValue":d[1]||(d[1]=K=>p.value=K),value:$.id,type:"radio",class:"sr-only"},null,8,xr),[[Ie,p.value]]),e("div",br,[e("div",wr,i($.icon),1),e("div",null,[e("div",_r,i($.name),1),e("div",$r,i($.description),1)])]),p.value===$.id?(t(),n("div",Cr,[g(M(ve),{class:"w-5 h-5"})])):C("",!0)],2))),128))])])):C("",!0),y.app.price.type!=="free"?(t(),n("div",Ar,[e("label",Vr,[g(v,{modelValue:I.value,"onUpdate:modelValue":d[2]||(d[2]=$=>I.value=$)},null,8,["modelValue"]),d[19]||(d[19]=e("span",{class:"text-sm text-gray-600 dark:text-dark-text-secondary"},[_(" 我已阅读并同意 "),e("a",{href:"#",class:"text-primary-600 dark:text-primary-400 hover:underline"},"《用户服务协议》"),_(" 和 "),e("a",{href:"#",class:"text-primary-600 dark:text-primary-400 hover:underline"},"《隐私政策》")],-1))])])):C("",!0)])):C("",!0)]),_:1},8,["modelValue","title"])}}}),we=q(Mr,[["__scopeId","data-v-2b0e2073"]]),Sr={key:0,class:"app-details-content"},Nr={class:"app-header flex items-start space-x-6 mb-6"},Tr={class:"flex-shrink-0"},Dr={class:"w-20 h-20 bg-gradient-to-br from-primary-100 to-primary-200 dark:from-primary-900/30 dark:to-primary-800/30 rounded-2xl flex items-center justify-center text-3xl font-bold border border-primary-200 dark:border-primary-700"},jr={class:"flex-1 min-w-0"},Or={class:"flex items-center space-x-3 mb-2"},zr={class:"text-2xl font-bold text-gray-900 dark:text-dark-text"},Ur={class:"text-gray-600 dark:text-dark-text-secondary mb-3"},Fr={class:"flex items-center space-x-6 text-sm mb-4"},Er={class:"flex items-center space-x-2"},Br={class:"flex items-center"},Pr={class:"font-medium text-gray-900 dark:text-dark-text"},Lr={class:"text-gray-500 dark:text-dark-text-secondary"},Rr={class:"text-gray-500 dark:text-dark-text-secondary"},Yr={class:"text-gray-500 dark:text-dark-text-secondary"},Hr={class:"flex flex-wrap gap-2"},Gr={class:"flex-shrink-0 space-y-3"},Zr={class:"text-right"},Jr={class:"flex flex-col space-y-2"},qr={class:"space-y-6"},Kr={class:"text-gray-600 dark:text-dark-text-secondary leading-relaxed"},Qr={key:0},Wr={class:"space-y-2"},Xr={class:"text-gray-600 dark:text-dark-text-secondary"},ea={key:1},ta={class:"space-y-2"},ra={class:"text-gray-600 dark:text-dark-text-secondary"},aa={key:0,class:"space-y-4"},sa={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},oa=["onClick"],na=["src","alt"],ia={class:"absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors duration-200 rounded-lg flex items-center justify-center"},la={key:1,class:"text-center py-12"},da={class:"space-y-6"},pa={class:"bg-gray-50 dark:bg-dark-card rounded-lg p-6"},ua={class:"flex items-center space-x-8"},ca={class:"text-center"},ga={class:"text-3xl font-bold text-gray-900 dark:text-dark-text"},ma={class:"flex items-center justify-center mt-1"},va={class:"text-sm text-gray-500 dark:text-dark-text-secondary mt-1"},ya={class:"flex-1 space-y-2"},fa={class:"text-sm text-gray-600 dark:text-dark-text-secondary w-8"},ka={class:"flex-1 bg-gray-200 dark:bg-gray-700 rounded-full h-2"},ha={class:"text-sm text-gray-500 dark:text-dark-text-secondary w-8"},xa={class:"space-y-4"},ba={class:"flex items-start space-x-4"},wa={class:"w-10 h-10 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full flex items-center justify-center text-white font-medium"},_a={class:"flex-1"},$a={class:"flex items-center space-x-3 mb-2"},Ca={class:"font-medium text-gray-900 dark:text-dark-text"},Aa={class:"flex items-center"},Va={class:"text-sm text-gray-500 dark:text-dark-text-secondary"},Ia={class:"text-gray-600 dark:text-dark-text-secondary leading-relaxed"},Ma=J({__name:"AppDetailsDialog",props:{modelValue:{type:Boolean},app:{}},emits:["update:modelValue","favorite-toggle","install-toggle"],setup(s,{emit:u}){const l=s,a=u,r=h("overview"),p=h(!1),I=h(0),f=h(!1),j=A({get:()=>l.modelValue,set:o=>a("update:modelValue",o)}),F=h([{id:1,user:"张三",rating:5,date:"2024-01-15",content:"非常好用的工具，界面简洁，功能强大，大大提高了工作效率！"},{id:2,user:"李四",rating:4,date:"2024-01-12",content:"整体不错，就是有些功能还需要完善，期待后续更新。"},{id:3,user:"王五",rating:5,date:"2024-01-10",content:"强烈推荐！客服响应也很及时，解决问题很专业。"}]),w=o=>o>=1e4?`${(o/1e4).toFixed(1)}万`:o>=1e3?`${(o/1e3).toFixed(1)}k`:o.toString(),x=o=>new Date(o).toLocaleDateString("zh-CN"),E=o=>({5:65,4:20,3:10,2:3,1:2})[o]||0,B=()=>{if(!l.app)return"立即安装";switch(l.app.price.type){case"free":return"立即安装";case"one_time":return`立即购买 ¥${l.app.price.amount}`;case"monthly":return`立即订阅 ¥${l.app.price.amount}/月`;case"per_use":return"立即充值";default:return"立即安装"}},y=(o,v)=>{I.value=v,p.value=!0},d=o=>{a("install-toggle",o),f.value=!1};return(o,v)=>{var re;const L=S("el-tag"),R=S("el-button"),$=S("el-tab-pane"),K=S("el-tabs"),ne=S("el-image-viewer"),ie=S("el-dialog");return t(),V(ie,{modelValue:j.value,"onUpdate:modelValue":v[6]||(v[6]=z=>j.value=z),title:((re=o.app)==null?void 0:re.name)||"应用详情",width:"900px","align-center":"","close-on-click-modal":!1,class:"app-details-dialog"},{default:k(()=>{var z;return[o.app?(t(),n("div",Sr,[e("div",Nr,[e("div",Tr,[e("div",Dr,i(o.app.icon),1)]),e("div",jr,[e("div",Or,[e("h2",zr,i(o.app.name),1),o.app.status==="maintenance"?(t(),V(L,{key:0,type:"warning",effect:"plain"},{default:k(()=>v[7]||(v[7]=[_(" 维护中 ")])),_:1,__:[7]})):o.app.status==="deprecated"?(t(),V(L,{key:1,type:"danger",effect:"plain"},{default:k(()=>v[8]||(v[8]=[_(" 已废弃 ")])),_:1,__:[8]})):C("",!0)]),e("p",Ur,i(o.app.developer)+" • v"+i(o.app.version),1),e("div",Fr,[e("div",Er,[e("div",Br,[(t(),n(T,null,D(5,m=>g(M(oe),{key:m,class:O(["w-5 h-5",m<=Math.floor(o.app.rating)?"text-yellow-400 fill-current":"text-gray-300 dark:text-gray-600"])},null,8,["class"])),64))]),e("span",Pr,i(o.app.rating),1),e("span",Lr," ("+i(o.app.reviewCount)+" 评价) ",1)]),e("div",Rr,i(w(o.app.downloadCount))+" 次使用 ",1),e("div",Yr," 更新于 "+i(x(o.app.lastUpdated)),1)]),e("div",Hr,[(t(!0),n(T,null,D(o.app.tags,m=>(t(),V(L,{key:m,size:"small",effect:"plain"},{default:k(()=>[_(i(m),1)]),_:2},1024))),128))])]),e("div",Gr,[e("div",Zr,[g(ce,{price:o.app.price,size:"large"},null,8,["price"])]),e("div",Jr,[o.app.isInstalled?(t(),V(R,{key:1,onClick:v[1]||(v[1]=m=>o.$emit("install-toggle",o.app.id)),type:"success",plain:"",size:"large",class:"w-full"},{default:k(()=>v[9]||(v[9]=[_(" 已安装 ")])),_:1,__:[9]})):(t(),V(R,{key:0,onClick:v[0]||(v[0]=m=>f.value=!0),type:"primary",size:"large",class:"w-full"},{default:k(()=>[_(i(B()),1)]),_:1})),g(R,{onClick:v[2]||(v[2]=m=>o.$emit("favorite-toggle",o.app.id)),type:o.app.isFavorited?"danger":"default",plain:!o.app.isFavorited,size:"large",class:"w-full"},{default:k(()=>[g(M(pe),{class:O(["w-4 h-4 mr-2",o.app.isFavorited?"fill-current":""])},null,8,["class"]),_(" "+i(o.app.isFavorited?"已收藏":"收藏"),1)]),_:1},8,["type","plain"])])])]),g(K,{modelValue:r.value,"onUpdate:modelValue":v[3]||(v[3]=m=>r.value=m),class:"app-details-tabs"},{default:k(()=>[g($,{label:"应用介绍",name:"overview"},{default:k(()=>[e("div",qr,[e("div",null,[v[10]||(v[10]=e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-dark-text mb-3"}," 应用简介 ",-1)),e("p",Kr,i(o.app.longDescription||o.app.description),1)]),o.app.features&&o.app.features.length>0?(t(),n("div",Qr,[v[11]||(v[11]=e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-dark-text mb-3"}," 主要功能 ",-1)),e("ul",Wr,[(t(!0),n(T,null,D(o.app.features,m=>(t(),n("li",{key:m,class:"flex items-center space-x-3"},[g(M(De),{class:"w-5 h-5 text-green-500 flex-shrink-0"}),e("span",Xr,i(m),1)]))),128))])])):C("",!0),o.app.requirements&&o.app.requirements.length>0?(t(),n("div",ea,[v[12]||(v[12]=e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-dark-text mb-3"}," 使用要求 ",-1)),e("ul",ta,[(t(!0),n(T,null,D(o.app.requirements,m=>(t(),n("li",{key:m,class:"flex items-center space-x-3"},[g(M(be),{class:"w-5 h-5 text-blue-500 flex-shrink-0"}),e("span",ra,i(m),1)]))),128))])])):C("",!0)])]),_:1}),g($,{label:"应用截图",name:"screenshots"},{default:k(()=>[o.app.screenshots&&o.app.screenshots.length>0?(t(),n("div",aa,[e("div",sa,[(t(!0),n(T,null,D(o.app.screenshots,(m,P)=>(t(),n("div",{key:P,class:"relative group cursor-pointer",onClick:ae=>y(m,P)},[e("img",{src:m,alt:`应用截图 ${P+1}`,class:"w-full h-48 object-cover rounded-lg border border-gray-200 dark:border-dark-border group-hover:border-primary-300 dark:group-hover:border-primary-600 transition-colors duration-200"},null,8,na),e("div",ia,[g(M(Be),{class:"w-8 h-8 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-200"})])],8,oa))),128))])])):(t(),n("div",la,[g(M(xe),{class:"w-16 h-16 text-gray-300 dark:text-gray-600 mx-auto mb-4"}),v[13]||(v[13]=e("p",{class:"text-gray-500 dark:text-dark-text-secondary"},"暂无应用截图",-1))]))]),_:1}),g($,{label:"用户评价",name:"reviews"},{default:k(()=>[e("div",da,[e("div",pa,[e("div",ua,[e("div",ca,[e("div",ga,i(o.app.rating),1),e("div",ma,[(t(),n(T,null,D(5,m=>g(M(oe),{key:m,class:O(["w-4 h-4",m<=Math.floor(o.app.rating)?"text-yellow-400 fill-current":"text-gray-300 dark:text-gray-600"])},null,8,["class"])),64))]),e("div",va,i(o.app.reviewCount)+" 条评价 ",1)]),e("div",ya,[(t(),n(T,null,D([5,4,3,2,1],m=>e("div",{key:m,class:"flex items-center space-x-3"},[e("span",fa,i(m)+"星 ",1),e("div",ka,[e("div",{class:"bg-yellow-400 h-2 rounded-full",style:Me({width:`${E(m)}%`})},null,4)]),e("span",ha,i(E(m))+"% ",1)])),64))])])]),e("div",xa,[(t(!0),n(T,null,D(F.value,m=>(t(),n("div",{key:m.id,class:"border border-gray-200 dark:border-dark-border rounded-lg p-4"},[e("div",ba,[e("div",wa,i(m.user.charAt(0)),1),e("div",_a,[e("div",$a,[e("span",Ca,i(m.user),1),e("div",Aa,[(t(),n(T,null,D(5,P=>g(M(oe),{key:P,class:O(["w-4 h-4",P<=m.rating?"text-yellow-400 fill-current":"text-gray-300 dark:text-gray-600"])},null,8,["class"])),64))]),e("span",Va,i(m.date),1)]),e("p",Ia,i(m.content),1)])])]))),128))])])]),_:1})]),_:1},8,["modelValue"])])):C("",!0),p.value?(t(),V(ne,{key:1,"url-list":((z=o.app)==null?void 0:z.screenshots)||[],"initial-index":I.value,onClose:v[4]||(v[4]=m=>p.value=!1)},null,8,["url-list","initial-index"])):C("",!0),g(we,{modelValue:f.value,"onUpdate:modelValue":v[5]||(v[5]=m=>f.value=m),app:o.app,onPurchaseSuccess:d},null,8,["modelValue","app"])]}),_:1},8,["modelValue","title"])}}}),Sa=q(Ma,[["__scopeId","data-v-5098ad69"]]);var X=(s=>(s.FREE="free",s.ONE_TIME="one_time",s.MONTHLY="monthly",s.PER_USE="per_use",s))(X||{}),Y=(s=>(s.IMAGE_PROCESSING="image_processing",s.DATA_ANALYSIS="data_analysis",s.SEO_TOOLS="seo_tools",s.MARKET_ANALYSIS="market_analysis",s.MANAGEMENT_TOOLS="management_tools",s.AUTOMATION="automation",s.CONTENT_CREATION="content_creation",s))(Y||{});const H=h([]),G=h([]),Z=h([]),ue=h(!1),te=h({}),Na=[{id:"product-collection",name:"商品采集",description:"智能采集全球电商平台商品信息，支持Amazon、Temu、Shein等主流平台",longDescription:"商品采集是一款专业的电商数据采集工具，支持从Amazon、Temu、Shein等主流电商平台批量采集商品信息。具备智能去重、数据清洗、格式转换等功能，是电商从业者的必备工具。",icon:"🛒",screenshots:["https://picsum.photos/800/600?random=1","https://picsum.photos/800/600?random=2","https://picsum.photos/800/600?random=3"],category:"data_analysis",tags:["数据采集","电商","批量处理","多平台"],price:{type:"monthly",amount:49.9,currency:"CNY",originalAmount:69.9,unit:"/月",description:"专业版功能，支持无限采集"},rating:4.9,reviewCount:2341,downloadCount:28750,developer:"RiinAI团队",version:"3.2.1",lastUpdated:"2024-01-16",status:"active",features:["多平台支持","智能去重","数据清洗","批量导出","定时采集"],requirements:["需要网络连接","支持Chrome浏览器插件"]},{id:"smart-crop",name:"智能裁图",description:"AI智能图片裁剪和优化，自动识别主体，支持多种裁剪比例",longDescription:"智能裁图是一款基于深度学习的图片裁剪工具，能够自动识别图片主体，进行智能裁剪。支持批量处理、多种裁剪比例、自定义裁剪区域等功能。",icon:"✂️",screenshots:["https://picsum.photos/800/600?random=4","https://picsum.photos/800/600?random=5","https://picsum.photos/800/600?random=6"],category:"image_processing",tags:["AI","图片处理","批量处理","智能裁剪"],price:{type:"monthly",amount:29.9,currency:"CNY",originalAmount:39.9,unit:"/月",description:"包含所有高级功能，无限制使用"},rating:4.8,reviewCount:1856,downloadCount:22420,developer:"RiinAI团队",version:"2.1.0",lastUpdated:"2024-01-15",status:"active",features:["智能主体识别","批量处理支持","多种裁剪比例","高质量输出","云端处理"],requirements:["需要网络连接","支持JPG/PNG格式"]},{id:"one-click-cutout",name:"一键抠图",description:"一键智能抠图，去除背景，AI驱动的背景移除工具",longDescription:"一键抠图使用先进的AI算法，能够精确识别图片主体，自动移除背景。支持批量处理，输出高质量透明背景图片，是设计师和电商从业者的得力助手。",icon:"🎨",screenshots:["https://picsum.photos/800/600?random=7","https://picsum.photos/800/600?random=8"],category:"image_processing",tags:["AI","抠图","背景移除","图片处理"],price:{type:"per_use",amount:.5,currency:"CNY",unit:"/张",description:"按使用次数计费，高质量处理"},rating:4.9,reviewCount:3241,downloadCount:45750,developer:"RiinAI团队",version:"1.8.0",lastUpdated:"2024-01-14",status:"active",features:["AI智能识别","高精度抠图","批量处理","多格式支持","云端处理"]},{id:"super-split",name:"超级裂变",description:"营销裂变工具，快速传播，支持多种裂变模式和数据分析",longDescription:"超级裂变是一款专业的营销裂变工具，支持多种裂变模式，包括分享裂变、任务裂变、拼团裂变等。提供详细的数据分析和用户行为追踪，帮助企业快速扩大用户规模。",icon:"🚀",screenshots:["https://picsum.photos/800/600?random=9","https://picsum.photos/800/600?random=10","https://picsum.photos/800/600?random=11"],category:"automation",tags:["营销","裂变","传播","数据分析"],price:{type:"monthly",amount:199,currency:"CNY",unit:"/月",description:"专业营销工具，支持无限裂变活动"},rating:4.7,reviewCount:1567,downloadCount:18920,developer:"RiinAI团队",version:"2.5.0",lastUpdated:"2024-01-13",status:"active",features:["多种裂变模式","数据分析","用户追踪","活动管理","效果统计"]},{id:"title-generator",name:"标题生成",description:"AI智能生成吸引人的标题，支持多种模板和平台优化",longDescription:"AI标题生成器基于大语言模型，能够为不同平台生成吸引人的标题。支持电商、自媒体、广告等多种场景，提供模板库和个性化定制功能。",icon:"📝",screenshots:["https://picsum.photos/800/600?random=12","https://picsum.photos/800/600?random=13"],category:"content_creation",tags:["AI","标题生成","内容创作","营销"],price:{type:"per_use",amount:.1,currency:"CNY",unit:"/次",description:"按生成次数计费，经济实惠"},rating:4.6,reviewCount:2890,downloadCount:35670,developer:"RiinAI团队",version:"1.9.2",lastUpdated:"2024-01-12",status:"active",features:["AI智能生成","多平台优化","模板库","批量生成","效果预测"]},{id:"pod-compose",name:"POD合成",description:"按需印刷商品合成工具，支持图案与产品的智能合成",longDescription:"POD合成工具专为按需印刷业务设计，支持将设计图案与各种产品模板进行智能合成。提供丰富的产品库、智能排版、批量处理等功能。",icon:"🎨",screenshots:["https://picsum.photos/800/600?random=14","https://picsum.photos/800/600?random=15","https://picsum.photos/800/600?random=16"],category:"image_processing",tags:["POD","合成","印刷","设计"],price:{type:"monthly",amount:89,currency:"CNY",unit:"/月",description:"专业POD工具，支持无限合成"},rating:4.5,reviewCount:1234,downloadCount:12450,developer:"RiinAI团队",version:"1.6.0",lastUpdated:"2024-01-11",status:"active",features:["智能合成","产品模板库","批量处理","高清输出","自动排版"]},{id:"batch-listing",name:"批量刊登",description:"批量发布商品到各大平台，支持模板导入和自动化发布",longDescription:"批量刊登工具支持将商品信息批量发布到Amazon、eBay、Shopify等多个电商平台。提供模板导入、数据映射、自动化发布等功能，大大提高运营效率。",icon:"📦",screenshots:["https://picsum.photos/800/600?random=17","https://picsum.photos/800/600?random=18"],category:"automation",tags:["批量发布","电商","自动化","多平台"],price:{type:"monthly",amount:159,currency:"CNY",unit:"/月",description:"专业电商工具，支持无限发布"},rating:4.4,reviewCount:987,downloadCount:15680,developer:"RiinAI团队",version:"2.3.1",lastUpdated:"2024-01-10",status:"active",features:["多平台支持","模板导入","批量发布","数据映射","发布监控"]},{id:"price-monitor",name:"价格监控",description:"实时监控商品价格变化，支持多平台价格对比和降价提醒",longDescription:"价格监控工具帮助您实时跟踪商品价格变化，支持Amazon、Temu、Shein等主流电商平台。提供价格历史图表、降价提醒、竞品对比等功能。",icon:"📊",screenshots:["https://picsum.photos/800/600?random=19","https://picsum.photos/800/600?random=20"],category:"data_analysis",tags:["价格监控","数据分析","电商","提醒"],price:{type:"free",amount:0,currency:"CNY",description:"免费版本，每日可监控10个商品"},rating:4.2,reviewCount:1892,downloadCount:28750,developer:"第三方开发者",version:"1.5.2",lastUpdated:"2024-01-10",status:"active",features:["多平台支持","价格历史图表","降价提醒","数据导出"],requirements:["需要网络连接"]},{id:"keyword-research",name:"关键词研究",description:"专业的关键词挖掘和分析工具，助力SEO优化",longDescription:"关键词研究专家是一款专业的SEO工具，提供关键词挖掘、竞争度分析、搜索量预测等功能。帮助您找到高价值的关键词，提升网站排名。",icon:"🔍",screenshots:["https://picsum.photos/800/600?random=21","https://picsum.photos/800/600?random=22","https://picsum.photos/800/600?random=23"],category:"seo_tools",tags:["SEO","关键词","搜索优化","竞争分析"],price:{type:"one_time",amount:199,currency:"CNY",originalAmount:299,description:"一次购买，终身使用"},rating:4.6,reviewCount:1567,downloadCount:13240,developer:"SEO专家团队",version:"3.0.1",lastUpdated:"2024-01-12",status:"active",features:["关键词挖掘","竞争度分析","搜索量预测","长尾词推荐","数据报告"]},{id:"review-analyzer",name:"评论分析",description:"智能分析商品评论情感，提取用户反馈洞察",longDescription:"评论分析工具使用自然语言处理技术，智能分析商品评论的情感倾向、关键词提取、用户满意度等。帮助商家了解产品优缺点，优化产品和服务。",icon:"💬",screenshots:["https://picsum.photos/800/600?random=24","https://picsum.photos/800/600?random=25"],category:"data_analysis",tags:["评论分析","NLP","情感分析","用户洞察"],price:{type:"monthly",amount:79,currency:"CNY",unit:"/月",description:"专业分析工具，无限制使用"},rating:4.3,reviewCount:756,downloadCount:9870,developer:"AI分析团队",version:"2.1.0",lastUpdated:"2024-01-09",status:"active",features:["情感分析","关键词提取","满意度评分","趋势分析","报告生成"]},{id:"competitor-analysis",name:"竞品分析",description:"深度分析竞争对手策略，洞察市场机会",longDescription:"竞品分析大师帮助您深入了解竞争对手的产品策略、价格策略、营销手段等。提供详细的分析报告和市场洞察，助力商业决策。",icon:"🎯",screenshots:["https://picsum.photos/800/600?random=26","https://picsum.photos/800/600?random=27"],category:"market_analysis",tags:["竞品分析","市场研究","策略分析","商业智能"],price:{type:"monthly",amount:299,currency:"CNY",unit:"/月",description:"专业版功能，深度分析报告"},rating:4.4,reviewCount:623,downloadCount:7890,developer:"商业分析专家",version:"2.3.0",lastUpdated:"2024-01-08",status:"active",features:["竞品监控","价格对比","营销策略分析","市场趋势预测","定制报告"]},{id:"inventory-management",name:"库存管理",description:"智能库存管理系统，支持多仓库、多渠道库存同步",longDescription:"智能库存管理系统提供全面的库存控制功能，支持多仓库管理、库存预警、自动补货、销售预测等。帮助企业优化库存结构，降低运营成本。",icon:"📋",screenshots:["https://picsum.photos/800/600?random=28","https://picsum.photos/800/600?random=29","https://picsum.photos/800/600?random=30"],category:"management_tools",tags:["库存管理","仓储","供应链","预测"],price:{type:"monthly",amount:399,currency:"CNY",unit:"/月",description:"企业级库存管理解决方案"},rating:4.5,reviewCount:445,downloadCount:5670,developer:"企业管理专家",version:"3.1.2",lastUpdated:"2024-01-07",status:"active",features:["多仓库管理","库存预警","自动补货","销售预测","报表分析"]},{id:"customer-service",name:"客服助手",description:"AI智能客服机器人，24小时自动回复客户咨询",longDescription:"AI客服助手基于大语言模型，能够理解客户问题并提供准确回复。支持多平台接入、知识库管理、人工客服转接等功能，大幅提升客服效率。",icon:"🎧",screenshots:["https://picsum.photos/800/600?random=31","https://picsum.photos/800/600?random=32"],category:"automation",tags:["AI客服","自动回复","知识库","多平台"],price:{type:"monthly",amount:199,currency:"CNY",unit:"/月",description:"AI客服解决方案，支持无限对话"},rating:4.6,reviewCount:1234,downloadCount:15670,developer:"AI服务团队",version:"2.0.5",lastUpdated:"2024-01-06",status:"active",features:["AI智能回复","多平台接入","知识库管理","人工转接","对话分析"]},{id:"data-export",name:"数据导出",description:"多格式数据导出工具，支持Excel、CSV、JSON等格式",longDescription:"数据导出工具支持将各种业务数据导出为多种格式，包括Excel、CSV、JSON、PDF等。提供数据清洗、格式转换、定时导出等功能。",icon:"📤",screenshots:["https://picsum.photos/800/600?random=33","https://picsum.photos/800/600?random=34"],category:"data_analysis",tags:["数据导出","格式转换","数据清洗","自动化"],price:{type:"free",amount:0,currency:"CNY",description:"免费工具，基础导出功能"},rating:4.1,reviewCount:567,downloadCount:12340,developer:"数据工具团队",version:"1.4.0",lastUpdated:"2024-01-05",status:"active",features:["多格式支持","数据清洗","批量导出","定时任务","模板定制"]},{id:"report-generator",name:"报表生成",description:"自动生成业务报表，支持多种图表和数据可视化",longDescription:"报表生成器能够自动收集业务数据，生成专业的分析报表。支持多种图表类型、数据可视化、定时发送等功能，让数据分析更简单。",icon:"📈",screenshots:["https://picsum.photos/800/600?random=35","https://picsum.photos/800/600?random=36","https://picsum.photos/800/600?random=37"],category:"data_analysis",tags:["报表生成","数据可视化","图表","自动化"],price:{type:"monthly",amount:129,currency:"CNY",unit:"/月",description:"专业报表工具，无限制生成"},rating:4.7,reviewCount:890,downloadCount:11230,developer:"数据分析专家",version:"2.2.1",lastUpdated:"2024-01-04",status:"active",features:["自动数据收集","多种图表","数据可视化","定时发送","模板库"]},{id:"workflow-automation",name:"工作流自动化",description:"无代码工作流自动化平台，连接各种应用和服务",longDescription:"工作流自动化平台让您无需编程即可创建复杂的自动化流程。支持连接数百种应用和服务，实现数据同步、任务自动化、通知提醒等功能。",icon:"⚡",screenshots:["https://picsum.photos/800/600?random=38","https://picsum.photos/800/600?random=39","https://picsum.photos/800/600?random=40"],category:"automation",tags:["工作流","自动化","无代码","集成"],price:{type:"monthly",amount:299,currency:"CNY",originalAmount:399,unit:"/月",description:"企业级自动化解决方案"},rating:4.8,reviewCount:1567,downloadCount:8900,developer:"自动化专家",version:"3.0.0",lastUpdated:"2024-01-03",status:"active",features:["无代码设计","应用集成","条件触发","数据转换","监控告警"]}],Ta=()=>{H.value=Na.map(s=>({...s,isFavorited:G.value.includes(s.id),isInstalled:Z.value.includes(s.id)}))},fe=()=>H.value,ke=A(()=>H.value.filter(s=>s.isFavorited)),Da=A(()=>{let s=H.value;const u=te.value;if(u.searchKeyword){const l=u.searchKeyword.toLowerCase();s=s.filter(a=>a.name.toLowerCase().includes(l)||a.description.toLowerCase().includes(l)||a.tags.some(r=>r.toLowerCase().includes(l)))}return u.category&&(s=s.filter(l=>l.category===u.category)),u.priceType&&(s=s.filter(l=>l.price.type===u.priceType)),u.rating!==void 0&&(s=s.filter(l=>l.rating>=u.rating)),u.sortBy&&s.sort((l,a)=>{let r,p;switch(u.sortBy){case"name":r=l.name,p=a.name;break;case"rating":r=l.rating,p=a.rating;break;case"downloadCount":r=l.downloadCount,p=a.downloadCount;break;case"lastUpdated":r=new Date(l.lastUpdated),p=new Date(a.lastUpdated);break;case"price":r=l.price.amount,p=a.price.amount;break;default:return 0}return u.sortOrder==="desc"?r>p?-1:r<p?1:0:r<p?-1:r>p?1:0}),s}),ja=s=>{te.value={...te.value,...s}},Oa=()=>{te.value={}},za=s=>{const u=H.value.find(a=>a.id===s);if(!u)return!1;const l=G.value.indexOf(s);return l>-1?(G.value.splice(l,1),u.isFavorited=!1):(G.value.push(s),u.isFavorited=!0),localStorage.setItem("app-market-favorites",JSON.stringify(G.value)),u.isFavorited},Ua=s=>{const u=H.value.find(a=>a.id===s);if(!u)return!1;const l=Z.value.indexOf(s);return l>-1?(Z.value.splice(l,1),u.isInstalled=!1):(Z.value.push(s),u.isInstalled=!0),localStorage.setItem("app-market-installed",JSON.stringify(Z.value)),u.isInstalled},he=s=>H.value.find(u=>u.id===s),Fa=()=>{ue.value=!0;const s=localStorage.getItem("app-market-favorites");s&&(G.value=JSON.parse(s));const u=localStorage.getItem("app-market-installed");u&&(Z.value=JSON.parse(u)),Ta(),ue.value=!1},Ea={apps:A(()=>H.value),loading:A(()=>ue.value),currentFilter:A(()=>te.value)},Ba={class:"space-y-8"},Pa={class:"bg-white dark:bg-dark-card rounded-2xl shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border p-6"},La={class:"flex items-center justify-between mb-6"},Ra={class:"text-sm text-gray-500 dark:text-dark-text-secondary"},Ya={class:"flex flex-wrap gap-3"},Ha={class:"ml-2 px-2 py-0.5 bg-white/20 rounded-full text-xs"},Ga=["onClick"],Za={class:"ml-2 px-2 py-0.5 bg-white/20 rounded-full text-xs"},Ja={class:"bg-white dark:bg-dark-card rounded-2xl shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border p-6"},qa={class:"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6"},Ka={class:"flex-1 max-w-2xl"},Qa={class:"relative"},Wa={class:"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none"},Xa={class:"flex flex-wrap items-center gap-4"},es={class:"relative"},ts=["value"],rs={class:"relative"},as={class:"flex items-center space-x-2"},ss={class:"relative"},os=["title"],ns={class:"bg-white dark:bg-dark-card rounded-xl shadow-sm border border-gray-100 dark:border-dark-border overflow-hidden"},is={class:"flex items-center"},ls=J({__name:"index",setup(s){const u=h("all"),l=h(""),a=h(""),r=h(""),p=h(""),I=h("name"),f=h("asc"),j=h(!1),F=h(null),w=h(!1),x=h(null),E=A(()=>Ea.loading.value),B=A(()=>fe().length),y=A(()=>ke.value.length),d=A(()=>Da.value),o=A(()=>ke.value),v=A(()=>[{label:"图像处理",value:Y.IMAGE_PROCESSING,icon:xe},{label:"数据分析",value:Y.DATA_ANALYSIS,icon:je},{label:"SEO工具",value:Y.SEO_TOOLS,icon:Pe},{label:"市场分析",value:Y.MARKET_ANALYSIS,icon:Re},{label:"管理工具",value:Y.MANAGEMENT_TOOLS,icon:Se},{label:"自动化工具",value:Y.AUTOMATION,icon:Fe},{label:"内容创作",value:Y.CONTENT_CREATION,icon:Le}]),L=A(()=>[{label:"免费",value:X.FREE},{label:"一口价",value:X.ONE_TIME},{label:"包月",value:X.MONTHLY},{label:"按次计费",value:X.PER_USE}]),R=()=>{z()},$=()=>{z()},K=()=>{z()},ne=()=>{z()},ie=()=>{z()},re=()=>{f.value=f.value==="asc"?"desc":"asc",z()},z=()=>{const N={searchKeyword:l.value||void 0,category:a.value||void 0,priceType:r.value||void 0,rating:p.value||void 0,sortBy:I.value,sortOrder:f.value};ja(N)},m=()=>{l.value="",a.value="",r.value="",p.value="",I.value="name",f.value="asc",Oa()},P=N=>{F.value=N,j.value=!0},ae=N=>{const c=za(N),U=he(N);U&&ee.success(c?`已收藏 ${U.name}`:`已取消收藏 ${U.name}`)},se=N=>{const c=Ua(N),U=he(N);U&&ee.success(c?`已安装 ${U.name}`:`已卸载 ${U.name}`)},ge=N=>{x.value=N,w.value=!0},_e=N=>{se(N),w.value=!1},$e=N=>N?fe().filter(c=>c.category===N).length:B.value;return Ne(()=>{Fa()}),(N,c)=>{const U=S("el-tab-pane"),Ce=S("el-badge"),Ae=S("el-tabs");return t(),n("div",Ba,[c[21]||(c[21]=de('<div class="bg-gradient-to-r from-primary-50 to-blue-50 dark:from-primary-900/20 dark:to-blue-900/20 rounded-2xl p-6 border border-primary-100 dark:border-primary-800" data-v-f3376840><div class="flex items-center space-x-3" data-v-f3376840><div class="w-10 h-10 bg-gradient-to-br from-primary-500 to-primary-600 rounded-xl flex items-center justify-center" data-v-f3376840><svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-f3376840><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" data-v-f3376840></path></svg></div><div data-v-f3376840><h1 class="text-2xl font-bold text-gray-900 dark:text-dark-text" data-v-f3376840>应用市场</h1><p class="mt-1 text-sm text-gray-600 dark:text-dark-text-secondary" data-v-f3376840>发现、安装和管理您的应用工具</p></div></div></div>',1)),e("div",Pa,[e("div",La,[c[9]||(c[9]=e("h2",{class:"text-xl font-bold text-gray-900 dark:text-dark-text flex items-center"},[e("svg",{class:"w-5 h-5 mr-2 text-purple-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"})]),_(" 应用分类 ")],-1)),e("div",Ra,i(d.value.length)+" / "+i(B.value)+" 个应用 ",1)]),e("div",Ya,[e("button",{onClick:c[0]||(c[0]=b=>{a.value="",$()}),class:O(["px-4 py-2 rounded-xl text-sm font-medium transition-all duration-200 transform hover:scale-105",a.value===""?"bg-gradient-to-r from-purple-500 to-pink-500 text-white shadow-lg":"bg-gray-100 dark:bg-dark-surface text-gray-700 dark:text-dark-text hover:bg-gray-200 dark:hover:bg-dark-border"])},[c[10]||(c[10]=e("svg",{class:"w-4 h-4 mr-2 inline",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"})],-1)),c[11]||(c[11]=_(" 全部应用 ")),e("span",Ha,i(B.value),1)],2),(t(!0),n(T,null,D(v.value,b=>(t(),n("button",{key:b.value,onClick:ds=>{a.value=b.value,$()},class:O(["px-4 py-2 rounded-xl text-sm font-medium transition-all duration-200 transform hover:scale-105 flex items-center",a.value===b.value?"bg-gradient-to-r from-purple-500 to-pink-500 text-white shadow-lg":"bg-gray-100 dark:bg-dark-surface text-gray-700 dark:text-dark-text hover:bg-gray-200 dark:hover:bg-dark-border"])},[(t(),V(me(b.icon),{class:"w-4 h-4 mr-2"})),_(" "+i(b.label)+" ",1),e("span",Za,i($e(b.value)),1)],10,Ga))),128))])]),e("div",Ja,[e("div",qa,[e("div",Ka,[e("div",Qa,[e("div",Wa,[g(M(Oe),{class:"w-5 h-5 text-gray-400"})]),W(e("input",{"onUpdate:modelValue":c[1]||(c[1]=b=>l.value=b),onInput:R,type:"text",placeholder:"搜索应用名称、描述或标签...",class:"w-full pl-12 pr-12 py-4 bg-gray-50 dark:bg-dark-surface border border-gray-200 dark:border-dark-border rounded-2xl focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200 placeholder-gray-400 dark:placeholder-gray-500 text-gray-900 dark:text-dark-text hover:border-gray-300 dark:hover:border-dark-text-secondary"},null,544),[[Te,l.value]]),l.value?(t(),n("button",{key:0,onClick:c[2]||(c[2]=b=>{l.value="",R()}),class:"absolute inset-y-0 right-0 pr-4 flex items-center text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors duration-200"},c[12]||(c[12]=[e("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))):C("",!0)])]),e("div",Xa,[e("div",es,[W(e("select",{"onUpdate:modelValue":c[3]||(c[3]=b=>r.value=b),onChange:K,class:"appearance-none px-4 py-3 pr-10 bg-gray-50 dark:bg-dark-surface border border-gray-200 dark:border-dark-border rounded-xl text-sm text-gray-900 dark:text-dark-text focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200 hover:border-gray-300 dark:hover:border-dark-text-secondary cursor-pointer"},[c[13]||(c[13]=e("option",{value:""},"全部价格",-1)),(t(!0),n(T,null,D(L.value,b=>(t(),n("option",{key:b.value,value:b.value},i(b.label),9,ts))),128))],544),[[le,r.value]]),c[14]||(c[14]=e("div",{class:"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none"},[e("svg",{class:"w-4 h-4 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 9l-7 7-7-7"})])],-1))]),e("div",rs,[W(e("select",{"onUpdate:modelValue":c[4]||(c[4]=b=>p.value=b),onChange:ne,class:"appearance-none px-4 py-3 pr-10 bg-gray-50 dark:bg-dark-surface border border-gray-200 dark:border-dark-border rounded-xl text-sm text-gray-900 dark:text-dark-text focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200 hover:border-gray-300 dark:hover:border-dark-text-secondary cursor-pointer"},c[15]||(c[15]=[e("option",{value:""},"全部评分",-1),e("option",{value:4},"4星以上",-1),e("option",{value:3},"3星以上",-1),e("option",{value:2},"2星以上",-1)]),544),[[le,p.value]]),c[16]||(c[16]=e("div",{class:"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none"},[e("svg",{class:"w-4 h-4 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 9l-7 7-7-7"})])],-1))]),e("div",as,[e("div",ss,[W(e("select",{"onUpdate:modelValue":c[5]||(c[5]=b=>I.value=b),onChange:ie,class:"appearance-none px-4 py-3 pr-10 bg-gray-50 dark:bg-dark-surface border border-gray-200 dark:border-dark-border rounded-xl text-sm text-gray-900 dark:text-dark-text focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200 hover:border-gray-300 dark:hover:border-dark-text-secondary cursor-pointer"},c[17]||(c[17]=[de('<option value="name" data-v-f3376840>按名称</option><option value="rating" data-v-f3376840>按评分</option><option value="downloadCount" data-v-f3376840>按下载量</option><option value="lastUpdated" data-v-f3376840>按更新时间</option><option value="price" data-v-f3376840>按价格</option>',5)]),544),[[le,I.value]]),c[18]||(c[18]=e("div",{class:"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none"},[e("svg",{class:"w-4 h-4 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 9l-7 7-7-7"})])],-1))]),e("button",{onClick:re,class:"p-3 rounded-xl bg-gray-50 dark:bg-dark-surface hover:bg-gray-100 dark:hover:bg-dark-border transition-all duration-200 border border-gray-200 dark:border-dark-border transform hover:scale-105",title:f.value==="asc"?"升序":"降序"},[(t(),V(me(f.value==="asc"?M(Ue):M(ze)),{class:"w-4 h-4 text-gray-600 dark:text-dark-text-secondary"}))],8,os)]),e("button",{onClick:m,class:"px-4 py-3 text-sm font-medium text-gray-600 dark:text-dark-text-secondary hover:text-purple-600 dark:hover:text-purple-400 bg-gray-50 dark:bg-dark-surface hover:bg-gray-100 dark:hover:bg-dark-border rounded-xl transition-all duration-200 border border-gray-200 dark:border-dark-border transform hover:scale-105"},c[19]||(c[19]=[e("svg",{class:"w-4 h-4 mr-2 inline",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1),_(" 清除筛选 ")]))])])]),e("div",ns,[g(Ae,{modelValue:u.value,"onUpdate:modelValue":c[6]||(c[6]=b=>u.value=b),class:"app-market-tabs"},{default:k(()=>[g(U,{label:"全部应用",name:"all"},{default:k(()=>[g(ye,{apps:d.value,loading:E.value,onAppClick:P,onFavoriteToggle:ae,onInstallToggle:se,onPurchase:ge},null,8,["apps","loading"])]),_:1}),g(U,{name:"favorites"},{label:k(()=>[e("span",is,[c[20]||(c[20]=_(" 我的收藏 ")),g(Ce,{value:y.value,hidden:y.value===0,class:"ml-2"},null,8,["value","hidden"])])]),default:k(()=>[g(ye,{apps:o.value,loading:E.value,onAppClick:P,onFavoriteToggle:ae,onInstallToggle:se,onPurchase:ge},null,8,["apps","loading"])]),_:1})]),_:1},8,["modelValue"])]),g(Sa,{modelValue:j.value,"onUpdate:modelValue":c[7]||(c[7]=b=>j.value=b),app:F.value,onFavoriteToggle:ae,onInstallToggle:se},null,8,["modelValue","app"]),g(we,{modelValue:w.value,"onUpdate:modelValue":c[8]||(c[8]=b=>w.value=b),app:x.value,onPurchaseSuccess:_e},null,8,["modelValue","app"])])}}}),ys=q(ls,[["__scopeId","data-v-f3376840"]]);export{ys as default};
