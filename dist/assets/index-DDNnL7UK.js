import{d as K,e as S,r as p,c as v,F as W,g as s,h,j as n,a as e,C as j,M as R,k as le,t as i,A as H,E as w,o as g,q,b as X,Q as Y,n as Z,_ as ee,f as ne,p as de,G as ie}from"./index-BTucjZQh.js";import{_ as ue}from"./GallerySelectDialog.vue_vue_type_script_setup_true_lang-CrzPKmtJ.js";import{g as ce,c as ge,a as xe}from"./billing-DsHNwwW1.js";import{r as J}from"./ArrowDownTrayIcon-B03b1yfF.js";import"./billing-Ciq-WFQK.js";const me={class:"space-y-6"},pe={class:"flex justify-start space-x-3"},ve={class:"bg-gray-50 dark:bg-dark-card border-2 border-dashed border-gray-300 dark:border-dark-border rounded-lg min-h-[400px] p-6"},ke={key:0},fe={class:"grid grid-cols-6 gap-4"},he={class:"relative"},be=["src","alt"],we=["onClick"],ye={class:"mt-1 text-xs text-gray-600 dark:text-dark-text-secondary truncate"},_e={key:0,class:"flex justify-center mt-6"},Ce={key:1,class:"flex flex-col items-center justify-center h-full text-center"},$e={class:"flex justify-between items-center"},je={class:"text-sm text-gray-500 dark:text-dark-text-secondary"},Ve={key:0},ze={key:0,class:"text-green-600 dark:text-green-400"},Me={key:1,class:"text-blue-600 dark:text-blue-400"},Te={key:1},Be={class:"flex space-x-3"},Q="smart-crop",Se=K({__name:"CreateCropDialog",props:{modelValue:{type:Boolean}},emits:["update:modelValue","success"],setup(E,{emit:V}){const z=E,M=V,b=S({get:()=>z.modelValue,set:c=>M("update:modelValue",c)}),_=p(!1),m=p([]),u=p([]),y=p(1),C=p(18),D=S(()=>{const c=(y.value-1)*C.value,o=c+C.value;return u.value.slice(c,o)}),I=c=>{const o=new FileReader;o.onload=k=>{var B;const l=Date.now()+Math.random();u.value.push({id:l,name:c.name,url:(B=k.target)==null?void 0:B.result,file:c.raw})},o.readAsDataURL(c.raw)},T=c=>{const o=u.value.findIndex(k=>k.name===c.name);o>-1&&u.value.splice(o,1)},A=c=>{const o=(y.value-1)*C.value+c;u.value.splice(o,1),D.value.length===0&&y.value>1&&y.value--},N=c=>{y.value=c},F=c=>{const o=c.map(k=>({id:k.id||Date.now()+Math.random(),name:k.name,url:k.url,file:k.file}));u.value.push(...o),_.value=!1},P=ce(Q),x=S(()=>u.value.length===0?0:ge(Q,u.value.length)),r=S(()=>xe(Q,u.value.length)),$=S(()=>{if(u.value.length===0)return"提交任务";const c=x.value;return c===0?"提交任务（免费）":`提交任务（¥${c.toFixed(2)}）`}),U=()=>{if(u.value.length===0){w.warning("请先选择要裁图的图片");return}w.success(`已提交裁图任务，将处理 ${u.value.length} 张图片`),M("success"),b.value=!1,L()},L=()=>{u.value=[],m.value=[],y.value=1};return(c,o)=>{const k=h("el-button"),l=h("el-upload"),B=h("el-pagination"),O=h("el-dialog");return g(),v(W,null,[s(O,{modelValue:b.value,"onUpdate:modelValue":o[3]||(o[3]=a=>b.value=a),title:"新建裁图任务",width:"900px","align-center":"",onClose:L},{footer:n(()=>[e("div",$e,[e("div",je,[u.value.length>0?(g(),v("div",Ve,[e("div",null,"将处理 "+i(u.value.length)+" 张图片",1),r.value.hasFreeQuota&&r.value.freeItems>0?(g(),v("div",ze," 免费额度："+i(r.value.freeItems)+" 张，付费："+i(r.value.chargeableItems)+" 张 ",1)):R("",!0),H(P)?(g(),v("div",Me," 单价：¥"+i(H(P).unitPrice.toFixed(2))+"/张 ",1)):R("",!0)])):(g(),v("div",Te,"请先选择图片"))]),e("div",Be,[s(k,{onClick:o[2]||(o[2]=a=>b.value=!1)},{default:n(()=>o[9]||(o[9]=[j("取消")])),_:1,__:[9]}),s(k,{type:"primary",onClick:U,disabled:u.value.length===0},{default:n(()=>[j(i($.value),1)]),_:1},8,["disabled"])])])]),default:n(()=>[e("div",me,[e("div",pe,[s(l,{ref:"uploadRef","file-list":m.value,"on-change":I,"on-remove":T,"auto-upload":!1,multiple:"",accept:"image/*","show-file-list":!1},{default:n(()=>[s(k,{type:"primary",size:"large"},{default:n(()=>o[5]||(o[5]=[e("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"})],-1),j(" 上传图片 ")])),_:1,__:[5]})]),_:1},8,["file-list"]),s(k,{onClick:o[0]||(o[0]=a=>_.value=!0),size:"large"},{default:n(()=>o[6]||(o[6]=[e("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"})],-1),j(" 图库选择 ")])),_:1,__:[6]})]),e("div",ve,[u.value.length>0?(g(),v("div",ke,[e("div",fe,[(g(!0),v(W,null,le(D.value,(a,t)=>(g(),v("div",{key:a.id||t,class:"relative group"},[e("div",he,[e("img",{src:a.url,alt:a.name,class:"w-full h-20 object-cover rounded-lg border border-gray-200 dark:border-dark-border"},null,8,be),e("button",{onClick:f=>A(t),class:"absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200"},o[7]||(o[7]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]),8,we)]),e("div",ye,i(a.name),1)]))),128))]),u.value.length>C.value?(g(),v("div",_e,[s(B,{"current-page":y.value,"onUpdate:currentPage":o[1]||(o[1]=a=>y.value=a),"page-size":C.value,total:u.value.length,layout:"prev, pager, next",onCurrentChange:N},null,8,["current-page","page-size","total"])])):R("",!0)])):(g(),v("div",Ce,o[8]||(o[8]=[e("svg",{class:"w-16 h-16 text-gray-400 mb-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1),e("p",{class:"text-lg text-gray-500 dark:text-dark-text-secondary mb-2"},"暂无图片",-1),e("p",{class:"text-sm text-gray-400 dark:text-dark-text-secondary"},"请点击上方按钮选择图片",-1)])))])])]),_:1},8,["modelValue"]),s(ue,{modelValue:_.value,"onUpdate:modelValue":o[4]||(o[4]=a=>_.value=a),"theme-color":"yellow",onSelect:F},null,8,["modelValue"])],64)}}}),De={class:"flex items-center justify-between p-6 border-b border-gray-100 dark:border-dark-border"},Ie={class:"flex items-center space-x-3"},Pe={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},Ue={key:0,class:"p-6 grid grid-cols-2 md:grid-cols-4 gap-4"},Le={class:"bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 p-4 rounded-xl border border-green-200 dark:border-green-800"},Re={class:"flex items-center space-x-2"},He={class:"text-sm font-bold text-green-900 dark:text-green-100"},Ae={class:"bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 p-4 rounded-xl border border-blue-200 dark:border-blue-800"},Ne={class:"flex items-center space-x-2"},Fe={class:"text-sm font-bold text-blue-900 dark:text-blue-100"},Oe={class:"bg-gradient-to-br from-emerald-50 to-emerald-100 dark:from-emerald-900/20 dark:to-emerald-800/20 p-4 rounded-xl border border-emerald-200 dark:border-emerald-800"},Ge={class:"flex items-center space-x-2"},Ee={class:"text-sm font-bold text-emerald-900 dark:text-emerald-100"},Qe={class:"bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 p-4 rounded-xl border border-purple-200 dark:border-purple-800"},We={class:"flex items-center space-x-2"},qe={class:"text-sm font-bold text-purple-900 dark:text-purple-100"},Je={class:"px-6 pb-6"},Ke={class:"bg-white dark:bg-dark-surface rounded-xl border border-gray-200 dark:border-dark-border overflow-hidden"},Xe={key:0,class:"flex justify-center"},Ye={key:1,class:"text-gray-400 text-xs"},Ze={key:0,class:"flex justify-center"},et={key:1,class:"text-gray-400 text-xs"},tt={key:0,class:"text-sm font-medium text-gray-900 dark:text-dark-text"},rt={key:1,class:"text-gray-400 text-xs"},at=["onClick"],st={class:"flex justify-center p-4 border-t border-gray-200 dark:border-dark-border"},ot={class:"flex items-center justify-between p-6 border-t border-gray-100 dark:border-dark-border bg-gray-50 dark:bg-dark-card/50"},lt={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},nt={class:"flex items-center space-x-3"},dt=K({__name:"ViewDetailsDialog",props:{modelValue:{type:Boolean},task:{}},emits:["update:modelValue"],setup(E,{emit:V}){const z=E,M=V,b=S({get:()=>z.modelValue,set:x=>M("update:modelValue",x)}),_=p(!1),m=p(1),u=p(10),y=S(()=>C.value.length),C=p([{fileName:"image1.jpg",status:"success",thumbnail:"https://via.placeholder.com/100x100/4ade80/ffffff?text=原图1",resultImage:"https://via.placeholder.com/100x100/22c55e/ffffff?text=结果1",downloadUrl:"#"},{fileName:"image2.jpg",status:"success",thumbnail:"https://via.placeholder.com/100x100/06b6d4/ffffff?text=原图2",resultImage:"https://via.placeholder.com/100x100/0ea5e9/ffffff?text=结果2",downloadUrl:"#"},{fileName:"image3.jpg",status:"failed",thumbnail:"https://via.placeholder.com/100x100/ef4444/ffffff?text=原图3",resultImage:"",errorMessage:"图片格式不支持"},{fileName:"image4.jpg",status:"success",thumbnail:"https://via.placeholder.com/100x100/8b5cf6/ffffff?text=原图4",resultImage:"https://via.placeholder.com/100x100/7c3aed/ffffff?text=结果4",downloadUrl:"#"},{fileName:"image5.jpg",status:"success",thumbnail:"https://via.placeholder.com/100x100/f59e0b/ffffff?text=原图5",resultImage:"https://via.placeholder.com/100x100/d97706/ffffff?text=结果5",downloadUrl:"#"}]),D=x=>({completed:"已完成",processing:"处理中",failed:"失败",pending:"等待中"})[x]||"未知",I=x=>({success:"成功",failed:"失败"})[x]||"未知",T=()=>{b.value=!1},A=x=>{w.success(`正在下载 ${x.fileName}`)},N=()=>{w.success("正在导出详情...")},F=x=>{u.value=x,m.value=1},P=x=>{m.value=x};return(x,r)=>{const $=h("el-table-column"),U=h("el-image"),L=h("el-table"),c=h("el-pagination"),o=h("el-dialog"),k=Y("loading");return g(),q(o,{modelValue:b.value,"onUpdate:modelValue":r[2]||(r[2]=l=>b.value=l),width:"1200px","before-close":T,"show-close":!1,class:"modern-dialog"},{header:n(()=>{var l;return[e("div",De,[e("div",Ie,[r[4]||(r[4]=e("div",{class:"w-10 h-10 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})])],-1)),e("div",null,[r[3]||(r[3]=e("h3",{class:"text-xl font-bold text-gray-900 dark:text-dark-text"},"裁图详情",-1)),e("p",Pe,"任务ID: "+i(((l=x.task)==null?void 0:l.id)||""),1)])]),e("button",{onClick:T,class:"p-2 text-gray-400 hover:text-gray-600 dark:text-dark-text-secondary dark:hover:text-dark-text rounded-lg hover:bg-gray-100 dark:hover:bg-dark-card transition-all duration-200"},r[5]||(r[5]=[e("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))])]}),footer:n(()=>[e("div",ot,[e("div",lt," 共 "+i(y.value)+" 条裁图结果 ",1),e("div",nt,[e("button",{onClick:T,class:"px-6 py-2.5 text-gray-700 dark:text-dark-text font-medium rounded-lg border border-gray-300 dark:border-dark-border hover:bg-gray-50 dark:hover:bg-dark-border transition-all duration-200"}," 关闭 "),e("button",{onClick:N,class:"inline-flex items-center px-6 py-2.5 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-medium rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"},[s(H(J),{class:"w-5 h-5 mr-2"}),r[15]||(r[15]=j(" 导出详情 "))])])])]),default:n(()=>[x.task?(g(),v("div",Ue,[e("div",Le,[e("div",Re,[r[7]||(r[7]=e("div",{class:"w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1)),e("div",null,[r[6]||(r[6]=e("p",{class:"text-xs text-green-600 dark:text-green-400 font-medium"},"任务状态",-1)),e("p",He,i(D(x.task.status)),1)])])]),e("div",Ae,[e("div",Ne,[r[9]||(r[9]=e("div",{class:"w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})])],-1)),e("div",null,[r[8]||(r[8]=e("p",{class:"text-xs text-blue-600 dark:text-blue-400 font-medium"},"目标数量",-1)),e("p",Fe,i(x.task.targetCount),1)])])]),e("div",Oe,[e("div",Ge,[r[11]||(r[11]=e("div",{class:"w-8 h-8 bg-emerald-500 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1)),e("div",null,[r[10]||(r[10]=e("p",{class:"text-xs text-emerald-600 dark:text-emerald-400 font-medium"},"成功数量",-1)),e("p",Ee,i(x.task.successCount),1)])])]),e("div",Qe,[e("div",We,[r[13]||(r[13]=e("div",{class:"w-8 h-8 bg-purple-500 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})])],-1)),e("div",null,[r[12]||(r[12]=e("p",{class:"text-xs text-purple-600 dark:text-purple-400 font-medium"},"操作人",-1)),e("p",qe,i(x.task.operator),1)])])])])):R("",!0),e("div",Je,[r[14]||(r[14]=e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-dark-text mb-4"},"裁图结果",-1)),e("div",Ke,[X((g(),q(L,{data:C.value,style:{width:"100%"},"max-height":"400",class:"modern-table"},{default:n(()=>[s($,{prop:"index",label:"序号",width:"80",align:"center"}),s($,{label:"预览",width:"100",align:"center"},{default:n(l=>[l.row.status==="success"?(g(),v("div",Xe,[s(U,{src:l.row.thumbnail,"preview-src-list":[l.row.thumbnail],fit:"cover",class:"w-16 h-16 rounded-lg border border-gray-200 dark:border-dark-border","preview-teleported":!0},null,8,["src","preview-src-list"])])):(g(),v("div",Ye,"无图片"))]),_:1}),s($,{label:"结果图",width:"100",align:"center"},{default:n(l=>[l.row.status==="success"?(g(),v("div",Ze,[s(U,{src:l.row.resultImage,"preview-src-list":[l.row.resultImage],fit:"cover",class:"w-16 h-16 rounded-lg border border-gray-200 dark:border-dark-border","preview-teleported":!0},null,8,["src","preview-src-list"])])):(g(),v("div",et,"无图片"))]),_:1}),s($,{label:"文件名","min-width":"200"},{default:n(l=>[l.row.fileName?(g(),v("div",tt,i(l.row.fileName),1)):(g(),v("div",rt,"无文件名"))]),_:1}),s($,{prop:"status",label:"状态",width:"100",align:"center"},{default:n(l=>[e("span",{class:Z(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",l.row.status==="success"?"bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300":"bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300"])},i(I(l.row.status)),3)]),_:1}),s($,{label:"操作",width:"120",align:"center"},{default:n(l=>[l.row.status==="success"?(g(),v("button",{key:0,onClick:B=>A(l.row),class:"text-green-600 dark:text-green-400 hover:text-green-700 dark:hover:text-green-300 text-sm font-medium"}," 下载结果 ",8,at)):R("",!0)]),_:1})]),_:1},8,["data"])),[[k,_.value]]),e("div",st,[s(c,{"current-page":m.value,"onUpdate:currentPage":r[0]||(r[0]=l=>m.value=l),"page-size":u.value,"onUpdate:pageSize":r[1]||(r[1]=l=>u.value=l),"page-sizes":[10,20,50],total:y.value,layout:"total, sizes, prev, pager, next",onSizeChange:F,onCurrentChange:P},null,8,["current-page","page-size","total"])])])])]),_:1},8,["modelValue"])}}}),it=ee(dt,[["__scopeId","data-v-f858d7d4"]]),ut={class:"space-y-6"},ct={class:"grid grid-cols-1 md:grid-cols-4 gap-6"},gt={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},xt={class:"flex items-center justify-between"},mt={class:"text-2xl font-bold text-gray-900 dark:text-dark-text"},pt={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},vt={class:"flex items-center justify-between"},kt={class:"text-2xl font-bold text-green-600 dark:text-green-400"},ft={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},ht={class:"flex items-center justify-between"},bt={class:"text-2xl font-bold text-yellow-600 dark:text-yellow-400"},wt={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},yt={class:"flex items-center justify-between"},_t={class:"text-2xl font-bold text-blue-600 dark:text-blue-400"},Ct={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border"},$t={class:"flex justify-between items-center"},jt={class:"flex items-center space-x-3"},Vt={key:0,class:"flex items-center space-x-2 ml-4 pl-4 border-l border-gray-200 dark:border-dark-border"},zt={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},Mt={class:"bg-white dark:bg-dark-surface rounded-xl shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border overflow-hidden"},Tt={class:"overflow-x-auto"},Bt={class:"font-mono text-sm text-gray-600 dark:text-dark-text-secondary"},St={class:"flex items-center space-x-2"},Dt={class:"font-medium text-gray-900 dark:text-dark-text"},It={class:"font-medium text-green-600 dark:text-green-400"},Pt={class:"flex items-center space-x-2"},Ut={class:"w-6 h-6 bg-gradient-to-br from-green-400 to-green-500 rounded-full flex items-center justify-center"},Lt={class:"text-white text-xs font-medium"},Rt={class:"text-sm text-gray-900 dark:text-dark-text"},Ht={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},At={class:"flex items-center space-x-2"},Nt=["onClick"],Ft={class:"flex justify-between items-center px-6 py-4 border-t border-gray-100 dark:border-dark-border bg-gray-50 dark:bg-dark-card/50"},Ot={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},Gt=K({__name:"index",setup(E){const V=p(!1),z=p(!1),M=p(null),b=p([]),_=p(!1),m=p({currentPage:1,pageSize:20,total:0}),u=p(1248),y=p(94.2),C=p(12),D=p(86),I=p([{id:"CROP001",targetCount:50,successCount:48,status:"completed",operator:"Admin",createTime:"2024-01-15 14:30:25"},{id:"CROP002",targetCount:30,successCount:25,status:"processing",operator:"User1",createTime:"2024-01-15 13:45:12"},{id:"CROP003",targetCount:20,successCount:0,status:"failed",operator:"User2",createTime:"2024-01-15 12:20:08"}]),T=a=>{const t={completed:"bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400",processing:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400",failed:"bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400",pending:"bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400"};return t[a]||t.pending},A=a=>({completed:"已完成",processing:"处理中",failed:"失败",pending:"等待中"})[a]||"未知",N=a=>{M.value=a,z.value=!0},F=()=>{w.success("正在导出表格数据...")},P=a=>{b.value=a},x=()=>{if(b.value.length===0){w.warning("请先选择要导出的任务");return}w.success(`正在导出 ${b.value.length} 个任务的数据...`)},r=()=>{w.success("数据已刷新")},$=a=>{const{action:t,row:f}=a;switch(t){case"titleGenerate":U(f);break;case"batchListing":L(f);break;case"oneClickCutout":c(f);break;case"superSplit":o(f);break;case"copyrightDetection":k(f);break;default:w.warning("未知操作")}},U=a=>{w.success(`正在为裁图任务 ${a.id} 创建标题生成任务...`)},L=a=>{w.success(`正在为裁图任务 ${a.id} 创建批量刊登任务...`)},c=a=>{w.success(`正在为裁图任务 ${a.id} 创建一键抠图任务...`)},o=a=>{w.success(`正在为裁图任务 ${a.id} 创建超级裂变任务...`)},k=a=>{w.success(`正在为裁图任务 ${a.id} 创建侵权检测任务...`)},l=a=>{m.value.pageSize=a,m.value.currentPage=1,O()},B=a=>{m.value.currentPage=a,O()},O=()=>{_.value=!0,setTimeout(()=>{const a=[{id:"CROP001",targetCount:50,successCount:48,status:"completed",operator:"Admin",createTime:"2024-01-15 14:30:25"},{id:"CROP002",targetCount:30,successCount:25,status:"processing",operator:"User1",createTime:"2024-01-15 13:20:15"},{id:"CROP003",targetCount:75,successCount:70,status:"completed",operator:"User2",createTime:"2024-01-15 12:10:30"},{id:"CROP004",targetCount:20,successCount:0,status:"failed",operator:"User3",createTime:"2024-01-15 11:45:20"},{id:"CROP005",targetCount:60,successCount:55,status:"completed",operator:"Admin",createTime:"2024-01-15 10:30:45"}],t=(m.value.currentPage-1)*m.value.pageSize,f=t+m.value.pageSize;I.value=a.slice(t,f),m.value.total=a.length,_.value=!1},500)};return ne(()=>{O()}),(a,t)=>{const f=h("el-table-column"),G=h("el-dropdown-item"),te=h("el-dropdown-menu"),re=h("el-dropdown"),ae=h("el-table"),se=h("el-pagination"),oe=Y("loading");return g(),v(W,null,[e("div",ut,[t[26]||(t[26]=de('<div class="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-2xl p-6 border border-green-100 dark:border-green-800" data-v-e45cb2d4><div class="flex items-center space-x-3" data-v-e45cb2d4><div class="w-10 h-10 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center" data-v-e45cb2d4><svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-e45cb2d4><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m0 0V1a1 1 0 011-1h2a1 1 0 011 1v18a1 1 0 01-1 1H4a1 1 0 01-1-1V1a1 1 0 011-1h2a1 1 0 011 1v3m0 0h8m-8 0H4a1 1 0 00-1 1v3a1 1 0 001 1h3m0 0h10m0 0h3a1 1 0 001-1V5a1 1 0 00-1-1h-3m-3 0v8a1 1 0 01-1 1H8a1 1 0 01-1-1V4" data-v-e45cb2d4></path></svg></div><div data-v-e45cb2d4><h1 class="text-2xl font-bold text-gray-900 dark:text-dark-text" data-v-e45cb2d4>智能裁图</h1><p class="mt-1 text-sm text-gray-600 dark:text-dark-text-secondary" data-v-e45cb2d4>AI智能图片裁剪和优化工具</p></div></div></div>',1)),e("div",ct,[e("div",gt,[e("div",xt,[e("div",null,[t[5]||(t[5]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"总裁图数",-1)),e("p",mt,i(u.value),1)]),t[6]||(t[6]=e("div",{class:"w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-green-600 dark:text-green-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})])],-1))])]),e("div",pt,[e("div",vt,[e("div",null,[t[7]||(t[7]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"成功率",-1)),e("p",kt,i(y.value)+"%",1)]),t[8]||(t[8]=e("div",{class:"w-12 h-12 bg-emerald-100 dark:bg-emerald-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-emerald-600 dark:text-emerald-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1))])]),e("div",ft,[e("div",ht,[e("div",null,[t[9]||(t[9]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"处理中",-1)),e("p",bt,i(C.value),1)]),t[10]||(t[10]=e("div",{class:"w-12 h-12 bg-yellow-100 dark:bg-yellow-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-yellow-600 dark:text-yellow-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1))])]),e("div",wt,[e("div",yt,[e("div",null,[t[11]||(t[11]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"今日裁图",-1)),e("p",_t,i(D.value),1)]),t[12]||(t[12]=e("div",{class:"w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-blue-600 dark:text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})])],-1))])])]),e("div",Ct,[e("div",$t,[e("div",jt,[e("button",{onClick:t[0]||(t[0]=d=>V.value=!0),class:"inline-flex items-center px-4 py-2.5 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-medium rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"},[s(H(ie),{class:"w-5 h-5 mr-2"}),t[13]||(t[13]=j(" 新建裁图 "))]),e("button",{onClick:F,class:"inline-flex items-center px-4 py-2.5 bg-white dark:bg-dark-card text-gray-700 dark:text-dark-text font-medium rounded-lg border border-gray-300 dark:border-dark-border hover:bg-gray-50 dark:hover:bg-dark-border shadow-sm hover:shadow-md transition-all duration-200"},[s(H(J),{class:"w-5 h-5 mr-2"}),t[14]||(t[14]=j(" 导出表格 "))]),b.value.length>0?(g(),v("div",Vt,[e("span",zt," 已选择 "+i(b.value.length)+" 项 ",1),e("button",{onClick:x,class:"inline-flex items-center px-3 py-1.5 bg-green-500 hover:bg-green-600 text-white text-sm font-medium rounded-lg transition-all duration-200"},[s(H(J),{class:"w-4 h-4 mr-1"}),t[15]||(t[15]=j(" 批量导出 "))])])):R("",!0)])])]),e("div",Mt,[t[25]||(t[25]=e("div",{class:"px-6 py-4 border-b border-gray-100 dark:border-dark-border"},[e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-dark-text"},"裁图任务列表"),e("p",{class:"text-sm text-gray-600 dark:text-dark-text-secondary mt-1"},"管理和监控您的所有裁图任务")],-1)),e("div",Tt,[X((g(),q(ae,{data:I.value,style:{width:"100%"},onSelectionChange:P,class:"modern-table","header-cell-style":{backgroundColor:"var(--el-bg-color-page)",color:"var(--el-text-color-primary)",fontWeight:"600",borderBottom:"1px solid var(--el-border-color-light)"},"row-style":{backgroundColor:"transparent"}},{default:n(()=>[s(f,{type:"selection",width:"55"}),s(f,{prop:"id",label:"裁图ID",width:"120"},{default:n(d=>[e("span",Bt,i(d.row.id),1)]),_:1}),s(f,{label:"裁图数量",width:"150"},{default:n(d=>[e("div",St,[t[16]||(t[16]=e("span",{class:"text-sm text-gray-600 dark:text-dark-text-secondary"},"目标:",-1)),e("span",Dt,i(d.row.targetCount),1),t[17]||(t[17]=e("span",{class:"text-gray-400"},"|",-1)),t[18]||(t[18]=e("span",{class:"text-sm text-gray-600 dark:text-dark-text-secondary"},"成功:",-1)),e("span",It,i(d.row.successCount),1)])]),_:1}),s(f,{prop:"status",label:"裁切状态",width:"120"},{default:n(d=>[e("span",{class:Z([T(d.row.status),"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"])},i(A(d.row.status)),3)]),_:1}),s(f,{prop:"operator",label:"操作人",width:"100"},{default:n(d=>[e("div",Pt,[e("div",Ut,[e("span",Lt,i(d.row.operator.charAt(0)),1)]),e("span",Rt,i(d.row.operator),1)])]),_:1}),s(f,{prop:"createTime",label:"创建时间",width:"180"},{default:n(d=>[e("div",Ht,i(d.row.createTime),1)]),_:1}),s(f,{label:"操作",width:"180"},{default:n(d=>[e("div",At,[e("button",{onClick:Et=>N(d.row),class:"inline-flex items-center px-3 py-1.5 text-sm font-medium text-green-600 dark:text-green-400 hover:text-green-700 dark:hover:text-green-300 bg-green-50 dark:bg-green-900/20 hover:bg-green-100 dark:hover:bg-green-900/30 rounded-lg transition-all duration-200"}," 查看详情 ",8,Nt),s(re,{onCommand:$,trigger:"click"},{dropdown:n(()=>[s(te,null,{default:n(()=>[s(G,{command:{action:"titleGenerate",row:d.row}},{default:n(()=>t[19]||(t[19]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-blue-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})]),e("span",null,"标题生成")],-1)])),_:2,__:[19]},1032,["command"]),s(G,{command:{action:"batchListing",row:d.row}},{default:n(()=>t[20]||(t[20]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-purple-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"})]),e("span",null,"批量刊登")],-1)])),_:2,__:[20]},1032,["command"]),s(G,{command:{action:"oneClickCutout",row:d.row}},{default:n(()=>t[21]||(t[21]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-pink-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})]),e("span",null,"一键抠图")],-1)])),_:2,__:[21]},1032,["command"]),s(G,{command:{action:"superSplit",row:d.row}},{default:n(()=>t[22]||(t[22]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-orange-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 10V3L4 14h7v7l9-11h-7z"})]),e("span",null,"超级裂变")],-1)])),_:2,__:[22]},1032,["command"]),s(G,{command:{action:"copyrightDetection",row:d.row}},{default:n(()=>t[23]||(t[23]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-blue-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"})]),e("span",null,"侵权检测")],-1)])),_:2,__:[23]},1032,["command"])]),_:2},1024)]),default:n(()=>[t[24]||(t[24]=e("button",{class:"inline-flex items-center px-2 py-1.5 text-sm font-medium text-gray-600 dark:text-dark-text-secondary hover:text-gray-700 dark:hover:text-dark-text bg-gray-50 dark:bg-dark-card hover:bg-gray-100 dark:hover:bg-dark-border rounded-lg transition-all duration-200"},[j(" 更多 "),e("svg",{class:"w-4 h-4 ml-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 9l-7 7-7-7"})])],-1))]),_:2,__:[24]},1024)])]),_:1})]),_:1},8,["data"])),[[oe,_.value]])]),e("div",Ft,[e("div",Ot," 共 "+i(m.value.total)+" 条记录 ",1),s(se,{"current-page":m.value.currentPage,"onUpdate:currentPage":t[1]||(t[1]=d=>m.value.currentPage=d),"page-size":m.value.pageSize,"onUpdate:pageSize":t[2]||(t[2]=d=>m.value.pageSize=d),"page-sizes":[10,20,50,100],total:m.value.total,layout:"sizes, prev, pager, next, jumper",onSizeChange:l,onCurrentChange:B,class:"modern-pagination"},null,8,["current-page","page-size","total"])])])]),s(Se,{modelValue:V.value,"onUpdate:modelValue":t[3]||(t[3]=d=>V.value=d),onSuccess:r},null,8,["modelValue"]),s(it,{modelValue:z.value,"onUpdate:modelValue":t[4]||(t[4]=d=>z.value=d),task:M.value},null,8,["modelValue","task"])],64)}}}),Xt=ee(Gt,[["__scopeId","data-v-e45cb2d4"]]);export{Xt as default};
