import{d as X,e as D,r as k,c as m,F as Q,g as r,h as v,j as n,E as f,a as e,C as j,t as l,M as G,k as Y,A as I,o as p,q as J,b as Z,Q as ee,_ as te,f as ne,p as de,G as ie,n as ue}from"./index-BTucjZQh.js";import{_ as ce}from"./GallerySelectDialog.vue_vue_type_script_setup_true_lang-CrzPKmtJ.js";import{g as pe,c as ge,a as xe}from"./billing-DsHNwwW1.js";import{r as K}from"./ArrowDownTrayIcon-B03b1yfF.js";import"./billing-Ciq-WFQK.js";const me={class:"space-y-6"},ke={class:"flex justify-start space-x-3"},ve={class:"bg-gray-50 dark:bg-dark-card rounded-lg p-4"},he={class:"flex items-center space-x-4"},be={class:"flex items-center space-x-2"},fe={class:"text-xs text-gray-500 dark:text-dark-text-secondary"},we={key:0,class:"space-y-4"},_e={class:"flex items-center justify-between"},ye={class:"text-sm font-medium text-gray-900 dark:text-dark-text"},Ce={class:"text-xs text-gray-500 dark:text-dark-text-secondary"},$e={class:"border border-gray-200 dark:border-dark-border rounded-lg p-4 max-h-80 overflow-y-auto"},je={class:"grid grid-cols-6 gap-4"},Ve={class:"relative"},Me=["src","alt"],ze=["onClick"],Se={class:"absolute bottom-1 right-1 bg-purple-500 text-white text-xs px-1.5 py-0.5 rounded"},Te={class:"text-xs text-gray-600 dark:text-dark-text-secondary mt-1 truncate"},Be={key:0,class:"mt-4 flex justify-center"},Le={key:1,class:"text-center py-12 border-2 border-dashed border-gray-200 dark:border-dark-border rounded-lg"},De={class:"flex justify-between items-center"},Pe={class:"text-sm text-gray-500 dark:text-dark-text-secondary"},Ie={key:0},Re={key:0,class:"text-green-600 dark:text-green-400"},Ne={key:1,class:"text-blue-600 dark:text-blue-400"},Ae={key:1},Ue={class:"flex space-x-3"},W="super-split",Fe=X({__name:"CreateSplitDialog",props:{modelValue:{type:Boolean}},emits:["update:modelValue","success"],setup(E,{emit:z}){const S=E,V=z,y=D({get:()=>S.modelValue,set:g=>V("update:modelValue",g)}),w=k(!1),T=k([]),c=k([]),_=k(1),C=k(18),i=k(3),B=D(()=>{const g=(_.value-1)*C.value,o=g+C.value;return c.value.slice(g,o)}),R=g=>{const o=new FileReader;o.onload=b=>{var P;const F=Date.now()+Math.random();c.value.push({id:F,name:g.name,url:(P=b.target)==null?void 0:P.result,file:g.raw})},o.readAsDataURL(g.raw)},x=g=>{},a=g=>{c.value.splice(g,1),B.value.length===0&&_.value>1&&_.value--},$=g=>{const o=g.map(b=>({id:b.id,name:b.name,url:b.url}));c.value.push(...o),w.value=!1,f.success(`已添加 ${g.length} 张图片`)},L=pe(W),N=D(()=>c.value.length===0?0:ge(W,c.value.length)),M=D(()=>xe(W,c.value.length)),A=D(()=>{if(c.value.length===0)return"提交任务";const g=N.value;return g===0?"提交任务（免费）":`提交任务（¥${g.toFixed(2)}）`}),U=()=>{if(c.value.length===0){f.warning("请先选择图片");return}f.success(`正在创建裂变任务，共 ${c.value.length} 张图片，每张生成 ${i.value} 个裂变版本`),u(),V("success"),V("update:modelValue",!1)},u=()=>{c.value=[],T.value=[],_.value=1,i.value=3};return(g,o)=>{const b=v("el-button"),F=v("el-upload"),P=v("el-input-number"),q=v("el-pagination"),O=v("el-dialog");return p(),m(Q,null,[r(O,{modelValue:y.value,"onUpdate:modelValue":o[4]||(o[4]=s=>y.value=s),title:"新建裂变任务",width:"900px","align-center":"",onClose:u},{footer:n(()=>[e("div",De,[e("div",Pe,[c.value.length>0?(p(),m("div",Ie,[e("div",null,"将处理 "+l(c.value.length)+" 张图片，生成 "+l(c.value.length*i.value)+" 张裂变图片",1),M.value.hasFreeQuota&&M.value.freeItems>0?(p(),m("div",Re," 免费额度："+l(M.value.freeItems)+" 张，付费："+l(M.value.chargeableItems)+" 张 ",1)):G("",!0),I(L)?(p(),m("div",Ne," 单价：¥"+l(I(L).unitPrice.toFixed(2))+"/张 ",1)):G("",!0)])):(p(),m("div",Ae,"请先选择图片"))]),e("div",Ue,[r(b,{onClick:o[3]||(o[3]=s=>y.value=!1)},{default:n(()=>o[12]||(o[12]=[j("取消")])),_:1,__:[12]}),r(b,{type:"primary",onClick:U,disabled:c.value.length===0},{default:n(()=>[j(l(A.value),1)]),_:1},8,["disabled"])])])]),default:n(()=>[e("div",me,[e("div",ke,[r(F,{ref:"uploadRef","file-list":T.value,"on-change":R,"on-remove":x,"auto-upload":!1,multiple:"",accept:"image/*","show-file-list":!1},{default:n(()=>[r(b,{type:"primary",size:"large"},{default:n(()=>o[6]||(o[6]=[e("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"})],-1),j(" 上传图片 ")])),_:1,__:[6]})]),_:1},8,["file-list"]),r(b,{size:"large",onClick:o[0]||(o[0]=s=>w.value=!0)},{default:n(()=>o[7]||(o[7]=[e("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1),j(" 从图库选择 ")])),_:1,__:[7]})]),e("div",ve,[o[9]||(o[9]=e("h4",{class:"text-sm font-medium text-gray-900 dark:text-dark-text mb-3"},"裂变设置",-1)),e("div",he,[e("div",be,[o[8]||(o[8]=e("label",{class:"text-sm text-gray-600 dark:text-dark-text-secondary"},"裂变数量:",-1)),r(P,{modelValue:i.value,"onUpdate:modelValue":o[1]||(o[1]=s=>i.value=s),min:1,max:5,size:"small",style:{width:"120px"}},null,8,["modelValue"])]),e("div",fe," 每张原图将生成 "+l(i.value)+" 个不同的裂变版本 ",1)])]),c.value.length>0?(p(),m("div",we,[e("div",_e,[e("h4",ye," 已选择图片 ("+l(c.value.length)+") ",1),e("div",Ce," 预计生成 "+l(c.value.length*i.value)+" 张裂变图片 ",1)]),e("div",$e,[e("div",je,[(p(!0),m(Q,null,Y(B.value,(s,t)=>(p(),m("div",{key:s.id||t,class:"relative group"},[e("div",Ve,[e("img",{src:s.url,alt:s.name,class:"w-full h-20 object-cover rounded-lg border border-gray-200 dark:border-dark-border"},null,8,Me),e("button",{onClick:h=>a(t+(_.value-1)*C.value),class:"absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200 hover:bg-red-600"},o[10]||(o[10]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]),8,ze),e("div",Se," ×"+l(i.value),1)]),e("p",Te,l(s.name),1)]))),128))]),c.value.length>C.value?(p(),m("div",Be,[r(q,{"current-page":_.value,"onUpdate:currentPage":o[2]||(o[2]=s=>_.value=s),"page-size":C.value,total:c.value.length,layout:"prev, pager, next",small:""},null,8,["current-page","page-size","total"])])):G("",!0)])])):(p(),m("div",Le,o[11]||(o[11]=[e("svg",{class:"w-12 h-12 text-gray-400 dark:text-dark-text-secondary mx-auto mb-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1),e("p",{class:"text-gray-500 dark:text-dark-text-secondary"},"请上传图片或从图库选择",-1)])))])]),_:1},8,["modelValue"]),r(ce,{modelValue:w.value,"onUpdate:modelValue":o[5]||(o[5]=s=>w.value=s),"theme-color":"purple",onSelect:$},null,8,["modelValue"])],64)}}}),He={class:"flex items-center justify-between p-6 border-b border-gray-100 dark:border-dark-border"},Ge={class:"flex items-center space-x-3"},Ee={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},Qe={key:0,class:"p-6 grid grid-cols-2 md:grid-cols-4 gap-4"},qe={class:"bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 p-4 rounded-xl border border-purple-200 dark:border-purple-800"},Oe={class:"flex items-center space-x-2"},We={class:"text-sm font-bold text-purple-900 dark:text-purple-100"},Je={class:"bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 p-4 rounded-xl border border-blue-200 dark:border-blue-800"},Ke={class:"flex items-center space-x-2"},Xe={class:"text-sm font-bold text-blue-900 dark:text-blue-100"},Ye={class:"bg-gradient-to-br from-indigo-50 to-indigo-100 dark:from-indigo-900/20 dark:to-indigo-800/20 p-4 rounded-xl border border-indigo-200 dark:border-indigo-800"},Ze={class:"flex items-center space-x-2"},et={class:"text-sm font-bold text-indigo-900 dark:text-indigo-100"},tt={class:"bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 p-4 rounded-xl border border-green-200 dark:border-green-800"},st={class:"flex items-center space-x-2"},rt={class:"text-sm font-bold text-green-900 dark:text-green-100"},ot={class:"px-6 pb-6"},at={class:"bg-white dark:bg-dark-surface rounded-xl border border-gray-200 dark:border-dark-border overflow-hidden"},lt={key:0,class:"flex justify-center"},nt={key:1,class:"flex justify-center"},dt={key:0,class:"flex justify-center space-x-2"},it={class:"absolute -top-1 -right-1 w-4 h-4 bg-purple-500 text-white text-xs rounded-full flex items-center justify-center"},ut={key:1,class:"flex justify-center"},ct={class:"text-sm text-gray-500 dark:text-dark-text-secondary"},pt={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},gt={key:0,class:"flex justify-center"},xt=["onClick"],mt={key:1,class:"flex justify-center"},kt={class:"flex items-center justify-between p-6 border-t border-gray-100 dark:border-dark-border bg-gray-50 dark:bg-dark-card/50"},vt={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},ht={class:"flex items-center space-x-3"},bt=X({__name:"ViewDetailsDialog",props:{modelValue:{type:Boolean},task:{}},emits:["update:modelValue"],setup(E,{emit:z}){const S=E,V=z,y=D({get:()=>S.modelValue,set:x=>V("update:modelValue",x)}),w=k(!1),T=k(0),c=k([{index:1,originalImage:"https://picsum.photos/400/400?random=1",splitResults:[{url:"https://picsum.photos/400/400?random=11",name:"split_1_v1.jpg"},{url:"https://picsum.photos/400/400?random=12",name:"split_1_v2.jpg"},{url:"https://picsum.photos/400/400?random=13",name:"split_1_v3.jpg"}],fileName:"product_001.jpg",status:"success"},{index:2,originalImage:"https://picsum.photos/400/400?random=2",splitResults:[{url:"https://picsum.photos/400/400?random=21",name:"split_2_v1.jpg"},{url:"https://picsum.photos/400/400?random=22",name:"split_2_v2.jpg"},{url:"https://picsum.photos/400/400?random=23",name:"split_2_v3.jpg"},{url:"https://picsum.photos/400/400?random=24",name:"split_2_v4.jpg"},{url:"https://picsum.photos/400/400?random=25",name:"split_2_v5.jpg"}],fileName:"product_002.jpg",status:"success"},{index:3,originalImage:"https://picsum.photos/400/400?random=3",splitResults:[],fileName:"product_003.jpg",status:"failed"}]);T.value=c.value.length;const _=x=>({completed:"已完成",processing:"处理中",failed:"失败",pending:"等待中"})[x]||"未知",C=x=>({success:"成功",failed:"失败"})[x]||"未知",i=()=>{y.value=!1},B=x=>{f.success(`正在下载 ${x.fileName} 的所有裂变结果`)},R=()=>{f.success("正在导出详情...")};return(x,a)=>{const $=v("el-table-column"),L=v("el-image"),N=v("el-tag"),M=v("el-table"),A=v("el-dialog"),U=ee("loading");return p(),J(A,{modelValue:y.value,"onUpdate:modelValue":a[0]||(a[0]=u=>y.value=u),width:"1200px","before-close":i,"show-close":!1,class:"modern-dialog"},{header:n(()=>{var u;return[e("div",He,[e("div",Ge,[a[2]||(a[2]=e("div",{class:"w-10 h-10 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 10V3L4 14h7v7l9-11h-7z"})])],-1)),e("div",null,[a[1]||(a[1]=e("h3",{class:"text-xl font-bold text-gray-900 dark:text-dark-text"},"裂变详情",-1)),e("p",Ee,"任务ID: "+l(((u=x.task)==null?void 0:u.id)||""),1)])]),e("button",{onClick:i,class:"p-2 text-gray-400 hover:text-gray-600 dark:text-dark-text-secondary dark:hover:text-dark-text rounded-lg hover:bg-gray-100 dark:hover:bg-dark-card transition-all duration-200"},a[3]||(a[3]=[e("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))])]}),footer:n(()=>[e("div",kt,[e("div",vt," 共 "+l(T.value)+" 条裂变结果 ",1),e("div",ht,[e("button",{onClick:i,class:"px-6 py-2.5 text-gray-700 dark:text-dark-text font-medium rounded-lg border border-gray-300 dark:border-dark-border hover:bg-gray-50 dark:hover:bg-dark-border transition-all duration-200"}," 关闭 "),e("button",{onClick:R,class:"inline-flex items-center px-6 py-2.5 bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white font-medium rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"},[r(I(K),{class:"w-5 h-5 mr-2"}),a[15]||(a[15]=j(" 导出详情 "))])])])]),default:n(()=>[x.task?(p(),m("div",Qe,[e("div",qe,[e("div",Oe,[a[5]||(a[5]=e("div",{class:"w-8 h-8 bg-purple-500 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1)),e("div",null,[a[4]||(a[4]=e("p",{class:"text-xs text-purple-600 dark:text-purple-400 font-medium"},"任务状态",-1)),e("p",We,l(_(x.task.status)),1)])])]),e("div",Je,[e("div",Ke,[a[7]||(a[7]=e("div",{class:"w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})])],-1)),e("div",null,[a[6]||(a[6]=e("p",{class:"text-xs text-blue-600 dark:text-blue-400 font-medium"},"原图数量",-1)),e("p",Xe,l(x.task.targetCount),1)])])]),e("div",Ye,[e("div",Ze,[a[9]||(a[9]=e("div",{class:"w-8 h-8 bg-indigo-500 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 10V3L4 14h7v7l9-11h-7z"})])],-1)),e("div",null,[a[8]||(a[8]=e("p",{class:"text-xs text-indigo-600 dark:text-indigo-400 font-medium"},"裂变数量",-1)),e("p",et,l(x.task.splitCount),1)])])]),e("div",tt,[e("div",st,[a[11]||(a[11]=e("div",{class:"w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 13l4 4L19 7"})])],-1)),e("div",null,[a[10]||(a[10]=e("p",{class:"text-xs text-green-600 dark:text-green-400 font-medium"},"成功生成",-1)),e("p",rt,l(x.task.successCount),1)])])])])):G("",!0),e("div",ot,[a[14]||(a[14]=e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-dark-text mb-4"},"裂变结果",-1)),e("div",at,[Z((p(),J(M,{data:c.value,style:{width:"100%"},"max-height":"400",class:"modern-table"},{default:n(()=>[r($,{prop:"index",label:"序号",width:"80",align:"center"}),r($,{label:"原图",width:"100",align:"center"},{default:n(u=>[u.row.status==="success"?(p(),m("div",lt,[r(L,{src:u.row.originalImage,"preview-src-list":[u.row.originalImage],fit:"cover",class:"w-16 h-16 rounded-lg border border-gray-200 dark:border-dark-border","preview-teleported":!0},null,8,["src","preview-src-list"])])):(p(),m("div",nt,a[12]||(a[12]=[e("div",{class:"w-16 h-16 bg-gray-100 dark:bg-dark-card rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})])],-1)])))]),_:1}),r($,{label:"裂变结果",width:"400",align:"center"},{default:n(u=>[u.row.status==="success"?(p(),m("div",dt,[(p(!0),m(Q,null,Y(u.row.splitResults,(g,o)=>(p(),m("div",{key:o,class:"relative"},[r(L,{src:g.url,"preview-src-list":u.row.splitResults.map(b=>b.url),"initial-index":o,fit:"cover",class:"w-16 h-16 rounded-lg border border-gray-200 dark:border-dark-border","preview-teleported":!0},null,8,["src","preview-src-list","initial-index"]),e("div",it,l(o+1),1)]))),128))])):(p(),m("div",ut,[e("span",ct,l(C(u.row.status)),1)]))]),_:1}),r($,{prop:"fileName",label:"文件名",width:"200",align:"center"},{default:n(u=>[e("span",pt,l(u.row.fileName),1)]),_:1}),r($,{prop:"status",label:"状态",width:"100",align:"center"},{default:n(u=>[r(N,{type:u.row.status==="success"?"success":"danger",size:"small"},{default:n(()=>[j(l(C(u.row.status)),1)]),_:2},1032,["type"])]),_:1}),r($,{label:"操作",width:"120",align:"center"},{default:n(u=>[u.row.status==="success"?(p(),m("div",gt,[e("button",{onClick:g=>B(u.row),class:"inline-flex items-center px-3 py-1.5 text-sm font-medium text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/30 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-900/50 transition-all duration-200"}," 下载 ",8,xt)])):(p(),m("div",mt,a[13]||(a[13]=[e("span",{class:"text-sm text-gray-400"},"-",-1)])))]),_:1})]),_:1},8,["data"])),[[U,w.value]])])])]),_:1},8,["modelValue"])}}}),ft=te(bt,[["__scopeId","data-v-3a9451d2"]]),wt={class:"space-y-6"},_t={class:"grid grid-cols-1 md:grid-cols-4 gap-6"},yt={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},Ct={class:"flex items-center justify-between"},$t={class:"text-2xl font-bold text-gray-900 dark:text-dark-text"},jt={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},Vt={class:"flex items-center justify-between"},Mt={class:"text-2xl font-bold text-green-600 dark:text-green-400"},zt={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},St={class:"flex items-center justify-between"},Tt={class:"text-2xl font-bold text-blue-600 dark:text-blue-400"},Bt={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},Lt={class:"flex items-center justify-between"},Dt={class:"text-2xl font-bold text-orange-600 dark:text-orange-400"},Pt={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border"},It={class:"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0"},Rt={class:"flex items-center space-x-3"},Nt={key:0,class:"flex items-center space-x-2 ml-4 pl-4 border-l border-gray-200 dark:border-dark-border"},At={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},Ut={class:"bg-white dark:bg-dark-surface rounded-xl shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border overflow-hidden"},Ft={class:"overflow-x-auto"},Ht={class:"font-mono text-sm text-gray-600 dark:text-dark-text-secondary"},Gt={class:"flex items-center space-x-2"},Et={class:"font-medium text-gray-900 dark:text-dark-text"},Qt={class:"font-medium text-purple-600 dark:text-purple-400"},qt={class:"text-sm font-medium text-green-600 dark:text-green-400"},Ot={class:"flex items-center space-x-2"},Wt={class:"w-6 h-6 bg-gradient-to-br from-purple-400 to-purple-500 rounded-full flex items-center justify-center"},Jt={class:"text-white text-xs font-medium"},Kt={class:"text-sm text-gray-900 dark:text-dark-text"},Xt={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},Yt={class:"flex items-center space-x-2"},Zt=["onClick"],es={class:"flex justify-between items-center px-6 py-4 border-t border-gray-100 dark:border-dark-border bg-gray-50 dark:bg-dark-card/50"},ts={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},ss=X({__name:"index",setup(E){const z=k(!1),S=k(!1),V=k(!1),y=k(null),w=k([]),T=k(1248),c=k(94.2),_=k(156),C=k(8),i=k({currentPage:1,pageSize:20,total:0}),B=k([{id:"SP001",targetCount:50,splitCount:3,status:"completed",successCount:150,operator:"张三",createTime:"2024-01-15 14:30:25"},{id:"SP002",targetCount:30,splitCount:5,status:"processing",successCount:120,operator:"李四",createTime:"2024-01-15 13:45:12"},{id:"SP003",targetCount:80,splitCount:2,status:"completed",successCount:160,operator:"王五",createTime:"2024-01-15 12:20:08"},{id:"SP004",targetCount:25,splitCount:4,status:"failed",successCount:0,operator:"赵六",createTime:"2024-01-15 11:15:33"},{id:"SP005",targetCount:60,splitCount:3,status:"pending",successCount:0,operator:"钱七",createTime:"2024-01-15 10:30:45"},{id:"SP006",targetCount:40,splitCount:5,status:"completed",successCount:200,operator:"孙八",createTime:"2024-01-15 09:45:22"},{id:"SP007",targetCount:35,splitCount:2,status:"processing",successCount:45,operator:"周九",createTime:"2024-01-15 08:20:15"},{id:"SP008",targetCount:70,splitCount:4,status:"completed",successCount:280,operator:"吴十",createTime:"2024-01-14 16:30:40"},{id:"SP009",targetCount:20,splitCount:3,status:"completed",successCount:60,operator:"郑一",createTime:"2024-01-14 15:15:28"},{id:"SP010",targetCount:55,splitCount:5,status:"processing",successCount:180,operator:"王二",createTime:"2024-01-14 14:45:55"}]),R=D(()=>{const s=(i.value.currentPage-1)*i.value.pageSize,t=s+i.value.pageSize;return B.value.slice(s,t)});ne(()=>{x()});const x=()=>{z.value=!0,setTimeout(()=>{i.value.total=B.value.length,z.value=!1},500)},a=s=>{const t={completed:"bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300",processing:"bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300",failed:"bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300",pending:"bg-gray-100 dark:bg-gray-900/30 text-gray-800 dark:text-gray-300"};return t[s]||t.pending},$=s=>({completed:"已完成",processing:"处理中",failed:"失败",pending:"等待中"})[s]||"未知",L=s=>{w.value=s},N=s=>{y.value=s,V.value=!0},M=s=>{const{action:t,row:h}=s;switch(t){case"titleGenerate":g(h);break;case"batchListing":o(h);break;case"smartCrop":b(h);break;case"oneClickCutout":F(h);break;case"copyrightDetection":P(h);break}},A=()=>{f.success("导出表格功能开发中...")},U=()=>{f.success(`正在批量导出 ${w.value.length} 个任务...`)},u=()=>{f.success("裂变任务创建成功！"),x()},g=s=>{f.success(`正在为裂变任务 ${s.id} 创建标题生成任务...`)},o=s=>{f.success(`正在为裂变任务 ${s.id} 创建批量刊登任务...`)},b=s=>{f.success(`正在为裂变任务 ${s.id} 创建智能裁图任务...`)},F=s=>{f.success(`正在为裂变任务 ${s.id} 创建一键抠图任务...`)},P=s=>{f.success(`正在为裂变任务 ${s.id} 创建侵权检测任务...`)},q=s=>{i.value.pageSize=s,i.value.currentPage=1,x()},O=s=>{i.value.currentPage=s,x()};return(s,t)=>{const h=v("el-table-column"),H=v("el-dropdown-item"),se=v("el-dropdown-menu"),re=v("el-dropdown"),oe=v("el-table"),ae=v("el-pagination"),le=ee("loading");return p(),m(Q,null,[e("div",wt,[t[26]||(t[26]=de('<div class="bg-gradient-to-r from-purple-50 to-indigo-50 dark:from-purple-900/20 dark:to-indigo-900/20 rounded-2xl p-6 border border-purple-100 dark:border-purple-800" data-v-a408a1e4><div class="flex items-center space-x-3" data-v-a408a1e4><div class="w-10 h-10 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center" data-v-a408a1e4><svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-a408a1e4><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" data-v-a408a1e4></path></svg></div><div data-v-a408a1e4><h1 class="text-2xl font-bold text-gray-900 dark:text-dark-text" data-v-a408a1e4>超级裂变</h1><p class="mt-1 text-sm text-gray-600 dark:text-dark-text-secondary" data-v-a408a1e4>AI智能图片裂变和多样化生成工具</p></div></div></div>',1)),e("div",_t,[e("div",yt,[e("div",Ct,[e("div",null,[t[5]||(t[5]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"总裂变数",-1)),e("p",$t,l(T.value),1)]),t[6]||(t[6]=e("div",{class:"w-12 h-12 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-purple-600 dark:text-purple-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 10V3L4 14h7v7l9-11h-7z"})])],-1))])]),e("div",jt,[e("div",Vt,[e("div",null,[t[7]||(t[7]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"成功率",-1)),e("p",Mt,l(c.value)+"%",1)]),t[8]||(t[8]=e("div",{class:"w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-green-600 dark:text-green-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1))])]),e("div",zt,[e("div",St,[e("div",null,[t[9]||(t[9]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"今日裂变",-1)),e("p",Tt,l(_.value),1)]),t[10]||(t[10]=e("div",{class:"w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-blue-600 dark:text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})])],-1))])]),e("div",Bt,[e("div",Lt,[e("div",null,[t[11]||(t[11]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"处理中",-1)),e("p",Dt,l(C.value),1)]),t[12]||(t[12]=e("div",{class:"w-12 h-12 bg-orange-100 dark:bg-orange-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-orange-600 dark:text-orange-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1))])])]),e("div",Pt,[e("div",It,[e("div",Rt,[e("button",{onClick:t[0]||(t[0]=d=>S.value=!0),class:"inline-flex items-center px-4 py-2.5 bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white font-medium rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"},[r(I(ie),{class:"w-5 h-5 mr-2"}),t[13]||(t[13]=j(" 新建裂变 "))]),e("button",{onClick:A,class:"inline-flex items-center px-4 py-2.5 bg-white dark:bg-dark-card text-gray-700 dark:text-dark-text font-medium rounded-lg border border-gray-300 dark:border-dark-border hover:bg-gray-50 dark:hover:bg-dark-border shadow-sm hover:shadow-md transition-all duration-200"},[r(I(K),{class:"w-5 h-5 mr-2"}),t[14]||(t[14]=j(" 导出表格 "))]),w.value.length>0?(p(),m("div",Nt,[e("span",At," 已选择 "+l(w.value.length)+" 项 ",1),e("button",{onClick:U,class:"inline-flex items-center px-3 py-1.5 bg-purple-500 hover:bg-purple-600 text-white text-sm font-medium rounded-lg transition-all duration-200"},[r(I(K),{class:"w-4 h-4 mr-1"}),t[15]||(t[15]=j(" 批量导出 "))])])):G("",!0)])])]),e("div",Ut,[t[25]||(t[25]=e("div",{class:"px-6 py-4 border-b border-gray-100 dark:border-dark-border"},[e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-dark-text"},"裂变任务列表"),e("p",{class:"text-sm text-gray-600 dark:text-dark-text-secondary mt-1"},"管理和监控您的所有裂变任务")],-1)),e("div",Ft,[Z((p(),J(oe,{data:R.value,style:{width:"100%"},onSelectionChange:L,class:"modern-table","header-cell-style":{backgroundColor:"var(--el-bg-color-page)",color:"var(--el-text-color-primary)",fontWeight:"600",borderBottom:"1px solid var(--el-border-color-light)"},"row-style":{backgroundColor:"transparent"}},{default:n(()=>[r(h,{type:"selection",width:"55"}),r(h,{prop:"id",label:"任务ID",width:"120"},{default:n(d=>[e("span",Ht,l(d.row.id),1)]),_:1}),r(h,{label:"裂变数量",width:"150"},{default:n(d=>[e("div",Gt,[t[16]||(t[16]=e("span",{class:"text-sm text-gray-600 dark:text-dark-text-secondary"},"目标:",-1)),e("span",Et,l(d.row.targetCount),1),t[17]||(t[17]=e("span",{class:"text-gray-400"},"|",-1)),t[18]||(t[18]=e("span",{class:"text-sm text-gray-600 dark:text-dark-text-secondary"},"裂变:",-1)),e("span",Qt,l(d.row.splitCount),1)])]),_:1}),r(h,{prop:"status",label:"裂变状态",width:"120"},{default:n(d=>[e("span",{class:ue([a(d.row.status),"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"])},l($(d.row.status)),3)]),_:1}),r(h,{prop:"successCount",label:"成功数量",width:"100"},{default:n(d=>[e("span",qt,l(d.row.successCount),1)]),_:1}),r(h,{prop:"operator",label:"操作人",width:"100"},{default:n(d=>[e("div",Ot,[e("div",Wt,[e("span",Jt,l(d.row.operator.charAt(0)),1)]),e("span",Kt,l(d.row.operator),1)])]),_:1}),r(h,{prop:"createTime",label:"创建时间",width:"180"},{default:n(d=>[e("div",Xt,l(d.row.createTime),1)]),_:1}),r(h,{label:"操作",width:"180"},{default:n(d=>[e("div",Yt,[e("button",{onClick:rs=>N(d.row),class:"inline-flex items-center px-3 py-1.5 text-sm font-medium text-purple-600 dark:text-purple-400 hover:text-purple-700 dark:hover:text-purple-300 bg-purple-50 dark:bg-purple-900/20 hover:bg-purple-100 dark:hover:bg-purple-900/30 rounded-lg transition-all duration-200"}," 查看详情 ",8,Zt),r(re,{onCommand:M,trigger:"click"},{dropdown:n(()=>[r(se,null,{default:n(()=>[r(H,{command:{action:"titleGenerate",row:d.row}},{default:n(()=>t[19]||(t[19]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-blue-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})]),e("span",null,"标题生成")],-1)])),_:2,__:[19]},1032,["command"]),r(H,{command:{action:"batchListing",row:d.row}},{default:n(()=>t[20]||(t[20]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-green-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"})]),e("span",null,"批量刊登")],-1)])),_:2,__:[20]},1032,["command"]),r(H,{command:{action:"smartCrop",row:d.row}},{default:n(()=>t[21]||(t[21]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-yellow-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})]),e("span",null,"智能裁图")],-1)])),_:2,__:[21]},1032,["command"]),r(H,{command:{action:"oneClickCutout",row:d.row}},{default:n(()=>t[22]||(t[22]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-pink-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m-9 0h10m-10 0a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V6a2 2 0 00-2-2"})]),e("span",null,"一键抠图")],-1)])),_:2,__:[22]},1032,["command"]),r(H,{command:{action:"copyrightDetection",row:d.row}},{default:n(()=>t[23]||(t[23]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-blue-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"})]),e("span",null,"侵权检测")],-1)])),_:2,__:[23]},1032,["command"])]),_:2},1024)]),default:n(()=>[t[24]||(t[24]=e("button",{class:"inline-flex items-center px-2 py-1.5 text-sm font-medium text-gray-600 dark:text-dark-text-secondary hover:text-gray-700 dark:hover:text-dark-text bg-gray-50 dark:bg-dark-card hover:bg-gray-100 dark:hover:bg-dark-border rounded-lg transition-all duration-200"},[j(" 更多 "),e("svg",{class:"w-4 h-4 ml-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 9l-7 7-7-7"})])],-1))]),_:2,__:[24]},1024)])]),_:1})]),_:1},8,["data"])),[[le,z.value]])]),e("div",es,[e("div",ts," 共 "+l(i.value.total)+" 条记录 ",1),r(ae,{"current-page":i.value.currentPage,"onUpdate:currentPage":t[1]||(t[1]=d=>i.value.currentPage=d),"page-size":i.value.pageSize,"onUpdate:pageSize":t[2]||(t[2]=d=>i.value.pageSize=d),"page-sizes":[10,20,50,100],total:i.value.total,layout:"sizes, prev, pager, next, jumper",onSizeChange:q,onCurrentChange:O,class:"modern-pagination"},null,8,["current-page","page-size","total"])])])]),r(Fe,{modelValue:S.value,"onUpdate:modelValue":t[3]||(t[3]=d=>S.value=d),onSuccess:u},null,8,["modelValue"]),r(ft,{modelValue:V.value,"onUpdate:modelValue":t[4]||(t[4]=d=>V.value=d),task:y.value},null,8,["modelValue","task"])],64)}}}),is=te(ss,[["__scopeId","data-v-a408a1e4"]]);export{is as default};
