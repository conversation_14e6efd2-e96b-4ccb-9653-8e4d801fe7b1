import{d as E,r as m,K as T,e as Q,f as G,c as C,a as e,g as s,A as p,t as r,y as H,j as o,h as u,W as J,b as L,Q as O,q as X,n as V,M as Y,C as Z,o as v}from"./index-BTucjZQh.js";import{r as ee}from"./CreditCardIcon-4VBkbhbU.js";import{r as te}from"./ChartBarIcon-bkXzkP-U.js";import{r as ae}from"./ClockIcon-Du0uA8cG.js";import{r as se}from"./MagnifyingGlassIcon-TSA1g2un.js";const le={class:"p-8"},re={class:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8"},oe={class:"bg-white dark:bg-dark-card rounded-lg p-6 shadow-sm"},de={class:"flex items-center"},ne={class:"w-12 h-12 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center"},ie={class:"ml-4"},ce={class:"text-2xl font-bold text-gray-900 dark:text-dark-text"},ue={class:"bg-white dark:bg-dark-card rounded-lg p-6 shadow-sm"},xe={class:"flex items-center"},me={class:"w-12 h-12 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center"},pe={class:"ml-4"},ge={class:"text-2xl font-bold text-gray-900 dark:text-dark-text"},be={class:"bg-white dark:bg-dark-card rounded-lg p-6 shadow-sm"},ke={class:"flex items-center"},ye={class:"w-12 h-12 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center"},ve={class:"ml-4"},_e={class:"text-2xl font-bold text-gray-900 dark:text-dark-text"},fe={class:"bg-white dark:bg-dark-card rounded-lg p-6 shadow-sm"},he={class:"flex items-center"},we={class:"w-12 h-12 bg-amber-100 dark:bg-amber-900/20 rounded-lg flex items-center justify-center"},Te={class:"ml-4"},Ce={class:"text-2xl font-bold text-gray-900 dark:text-dark-text"},Ve={class:"bg-white dark:bg-dark-card rounded-lg p-6 shadow-sm mb-6"},ze={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},Se={class:"bg-white dark:bg-dark-card rounded-lg shadow-sm overflow-hidden"},De={class:"overflow-x-auto"},Ue={class:"font-mono text-sm"},je={class:"text-sm text-gray-900 dark:text-dark-text"},Me={class:"text-sm text-gray-900 dark:text-dark-text"},Pe={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},Fe={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},$e=["onClick"],Be={class:"px-6 py-4 border-t border-gray-200 dark:border-dark-border"},Ne={key:0,class:"space-y-4"},Re={class:"grid grid-cols-2 gap-4"},Ae={class:"mt-1 text-sm font-mono text-gray-900 dark:text-dark-text"},Ie={class:"mt-1 text-sm text-gray-900 dark:text-dark-text"},Ke={class:"mt-1 text-sm text-gray-900 dark:text-dark-text"},We={class:"mt-1 text-sm font-semibold text-gray-900 dark:text-dark-text"},qe={class:"mt-1 text-sm text-gray-900 dark:text-dark-text"},Ee={class:"mt-1 text-sm text-gray-900 dark:text-dark-text"},Qe={class:"mt-1 text-sm text-gray-900 dark:text-dark-text"},Ge={class:"flex justify-end"},et=E({__name:"index",setup(He){const k=m(!1),g=m(!1),c=m(null),z=m(2456.78),S=m(456.78),D=m(1234),U=m(156),d=T({type:"",appType:"",dateRange:null,keyword:""}),n=T({currentPage:1,pageSize:10,total:0}),_=m([]),j=Q(()=>{const l=(n.currentPage-1)*n.pageSize,t=l+n.pageSize;return _.value.slice(l,t)}),f=l=>({recharge:"充值",consume:"消费",refund:"退款"})[l]||l,M=l=>({recharge:"bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-400",consume:"bg-red-100 dark:bg-red-900/20 text-red-800 dark:text-red-400",refund:"bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-400"})[l]||"",h=l=>({"product-collection":"商品采集","smart-crop":"智能裁图","one-click-cutout":"一键抠图","super-split":"超级裂变","title-generator":"标题生成","batch-listing":"批量刊登",recharge:"账号充值"})[l]||l,y=()=>{n.currentPage=1,b()},P=()=>{d.type="",d.appType="",d.dateRange=null,d.keyword="",y()},F=l=>{n.pageSize=l,b()},$=l=>{n.currentPage=l,b()},B=l=>{c.value=l,g.value=!0},b=()=>{k.value=!0,setTimeout(()=>{const l=[{id:"T20240115001",type:"consume",appType:"product-collection",amount:5,balance:1229.56,description:"商品采集 - Amazon平台采集50个商品",createTime:"2024-01-15 14:30:00"},{id:"T20240115002",type:"recharge",appType:"recharge",amount:500,balance:1234.56,description:"账号充值 - 支付宝支付",createTime:"2024-01-15 14:25:00"},{id:"T20240114001",type:"consume",appType:"smart-crop",amount:3,balance:734.56,description:"智能裁图 - 处理30张图片",createTime:"2024-01-14 16:45:00"},{id:"T20240114002",type:"consume",appType:"one-click-cutout",amount:2.5,balance:737.56,description:"一键抠图 - 处理25张图片",createTime:"2024-01-14 15:20:00"},{id:"T20240113001",type:"consume",appType:"title-generator",amount:1,balance:740.06,description:"标题生成 - 生成10个商品标题",createTime:"2024-01-13 11:30:00"}];_.value=l,n.total=l.length,k.value=!1},500)};return G(()=>{b()}),(l,t)=>{const i=u("el-option"),w=u("el-select"),N=u("el-date-picker"),R=u("el-input"),x=u("el-table-column"),A=u("el-table"),I=u("el-pagination"),K=u("el-button"),W=u("el-dialog"),q=O("loading");return v(),C("div",le,[t[24]||(t[24]=e("div",{class:"mb-8"},[e("h2",{class:"text-2xl font-bold text-gray-900 dark:text-dark-text"},"交易记录"),e("p",{class:"mt-2 text-sm text-gray-600 dark:text-dark-text-secondary"},"查看您的交易记录和使用统计")],-1)),e("div",re,[e("div",oe,[e("div",de,[e("div",ne,[s(p(ee),{class:"w-6 h-6 text-blue-600 dark:text-blue-400"})]),e("div",ie,[t[8]||(t[8]=e("p",{class:"text-sm text-gray-600 dark:text-dark-text-secondary"},"总消费",-1)),e("p",ce,"¥"+r(z.value.toFixed(2)),1)])])]),e("div",ue,[e("div",xe,[e("div",me,[s(p(te),{class:"w-6 h-6 text-green-600 dark:text-green-400"})]),e("div",pe,[t[9]||(t[9]=e("p",{class:"text-sm text-gray-600 dark:text-dark-text-secondary"},"本月消费",-1)),e("p",ge,"¥"+r(S.value.toFixed(2)),1)])])]),e("div",be,[e("div",ke,[e("div",ye,[s(p(H),{class:"w-6 h-6 text-purple-600 dark:text-purple-400"})]),e("div",ve,[t[10]||(t[10]=e("p",{class:"text-sm text-gray-600 dark:text-dark-text-secondary"},"使用次数",-1)),e("p",_e,r(D.value),1)])])]),e("div",fe,[e("div",he,[e("div",we,[s(p(ae),{class:"w-6 h-6 text-amber-600 dark:text-amber-400"})]),e("div",Te,[t[11]||(t[11]=e("p",{class:"text-sm text-gray-600 dark:text-dark-text-secondary"},"本月使用",-1)),e("p",Ce,r(U.value),1)])])])]),e("div",Ve,[e("div",ze,[e("div",null,[t[12]||(t[12]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text-secondary mb-2"}," 交易类型 ",-1)),s(w,{modelValue:d.type,"onUpdate:modelValue":t[0]||(t[0]=a=>d.type=a),placeholder:"全部类型",clearable:"",class:"w-full"},{default:o(()=>[s(i,{label:"全部类型",value:""}),s(i,{label:"充值",value:"recharge"}),s(i,{label:"消费",value:"consume"}),s(i,{label:"退款",value:"refund"})]),_:1},8,["modelValue"])]),e("div",null,[t[13]||(t[13]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text-secondary mb-2"}," 应用类型 ",-1)),s(w,{modelValue:d.appType,"onUpdate:modelValue":t[1]||(t[1]=a=>d.appType=a),placeholder:"全部应用",clearable:"",class:"w-full"},{default:o(()=>[s(i,{label:"全部应用",value:""}),s(i,{label:"商品采集",value:"product-collection"}),s(i,{label:"智能裁图",value:"smart-crop"}),s(i,{label:"一键抠图",value:"one-click-cutout"}),s(i,{label:"超级裂变",value:"super-split"}),s(i,{label:"标题生成",value:"title-generator"}),s(i,{label:"批量刊登",value:"batch-listing"})]),_:1},8,["modelValue"])]),e("div",null,[t[14]||(t[14]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text-secondary mb-2"}," 时间范围 ",-1)),s(N,{modelValue:d.dateRange,"onUpdate:modelValue":t[2]||(t[2]=a=>d.dateRange=a),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",class:"w-full"},null,8,["modelValue"])]),e("div",null,[t[15]||(t[15]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text-secondary mb-2"}," 搜索 ",-1)),s(R,{modelValue:d.keyword,"onUpdate:modelValue":t[3]||(t[3]=a=>d.keyword=a),placeholder:"搜索交易ID或备注",clearable:"",onKeyup:J(y,["enter"])},{suffix:o(()=>[s(p(se),{class:"w-4 h-4 text-gray-400"})]),_:1},8,["modelValue"])])]),e("div",{class:"flex justify-end mt-4 space-x-3"},[e("button",{onClick:P,class:"px-4 py-2 text-sm font-medium text-gray-600 dark:text-dark-text-secondary hover:text-gray-900 dark:hover:text-dark-text bg-gray-50 dark:bg-dark-border hover:bg-gray-100 dark:hover:bg-dark-card rounded-lg transition-all duration-200"}," 重置 "),e("button",{onClick:y,class:"px-4 py-2 text-sm font-medium text-white bg-amber-500 hover:bg-amber-600 rounded-lg transition-colors"}," 搜索 ")])]),e("div",Se,[e("div",De,[L((v(),X(A,{data:j.value,style:{width:"100%"},class:"modern-table","header-cell-style":{backgroundColor:"var(--el-bg-color-page)",color:"var(--el-text-color-primary)",fontWeight:"600",borderBottom:"1px solid var(--el-border-color-light)"},"row-style":{backgroundColor:"transparent"}},{default:o(()=>[s(x,{label:"交易ID",width:"120"},{default:o(a=>[e("span",Ue,r(a.row.id),1)]),_:1}),s(x,{label:"类型",width:"100"},{default:o(a=>[e("span",{class:V(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",M(a.row.type)])},r(f(a.row.type)),3)]),_:1}),s(x,{label:"应用",width:"120"},{default:o(a=>[e("span",je,r(h(a.row.appType)),1)]),_:1}),s(x,{label:"金额",width:"100"},{default:o(a=>[e("span",{class:V(["font-semibold",[a.row.type==="consume"?"text-red-600 dark:text-red-400":"text-green-600 dark:text-green-400"]])},r(a.row.type==="consume"?"-":"+")+"¥"+r(a.row.amount.toFixed(2)),3)]),_:1}),s(x,{label:"余额",width:"100"},{default:o(a=>[e("span",Me," ¥"+r(a.row.balance.toFixed(2)),1)]),_:1}),s(x,{label:"备注","min-width":"200"},{default:o(a=>[e("span",Pe,r(a.row.description),1)]),_:1}),s(x,{label:"时间",width:"160"},{default:o(a=>[e("span",Fe,r(a.row.createTime),1)]),_:1}),s(x,{label:"操作",width:"100"},{default:o(a=>[e("button",{onClick:Je=>B(a.row),class:"text-sm text-amber-600 dark:text-amber-400 hover:text-amber-700 dark:hover:text-amber-300"}," 详情 ",8,$e)]),_:1})]),_:1},8,["data"])),[[q,k.value]])]),e("div",Be,[s(I,{"current-page":n.currentPage,"onUpdate:currentPage":t[4]||(t[4]=a=>n.currentPage=a),"page-size":n.pageSize,"onUpdate:pageSize":t[5]||(t[5]=a=>n.pageSize=a),"page-sizes":[10,20,50,100],total:n.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:F,onCurrentChange:$},null,8,["current-page","page-size","total"])])]),s(W,{modelValue:g.value,"onUpdate:modelValue":t[7]||(t[7]=a=>g.value=a),title:"交易详情",width:"500px"},{footer:o(()=>[e("div",Ge,[s(K,{onClick:t[6]||(t[6]=a=>g.value=!1)},{default:o(()=>t[23]||(t[23]=[Z("关闭")])),_:1,__:[23]})])]),default:o(()=>[c.value?(v(),C("div",Ne,[e("div",Re,[e("div",null,[t[16]||(t[16]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text-secondary"},"交易ID",-1)),e("p",Ae,r(c.value.id),1)]),e("div",null,[t[17]||(t[17]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text-secondary"},"交易类型",-1)),e("p",Ie,r(f(c.value.type)),1)]),e("div",null,[t[18]||(t[18]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text-secondary"},"应用类型",-1)),e("p",Ke,r(h(c.value.appType)),1)]),e("div",null,[t[19]||(t[19]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text-secondary"},"交易金额",-1)),e("p",We,r(c.value.type==="consume"?"-":"+")+"¥"+r(c.value.amount.toFixed(2)),1)]),e("div",null,[t[20]||(t[20]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text-secondary"},"交易后余额",-1)),e("p",qe,"¥"+r(c.value.balance.toFixed(2)),1)]),e("div",null,[t[21]||(t[21]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text-secondary"},"交易时间",-1)),e("p",Ee,r(c.value.createTime),1)])]),e("div",null,[t[22]||(t[22]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text-secondary"},"备注说明",-1)),e("p",Qe,r(c.value.description),1)])])):Y("",!0)]),_:1},8,["modelValue"])])}}});export{et as default};
