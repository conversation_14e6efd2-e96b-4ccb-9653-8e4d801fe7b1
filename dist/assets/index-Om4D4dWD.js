import{c as r,a as e,o as l,r as D,e as B,d as se,q as F,h as S,j as c,M as w,g as a,A as O,k as G,t as i,C as V,F as H,s as oe,y as Z,S as ke,D as be,_ as xe,L as Ue,E as j,K as Ne,b as pe,v as $e,n as ee,w as je,T as Le,H as De,J as We,f as Ae,p as Ie,R as Re,P as Pe,U as Fe,Q as Oe}from"./index-BTucjZQh.js";import{r as Ce}from"./MagnifyingGlassIcon-TSA1g2un.js";import{r as fe}from"./DocumentTextIcon-DpKW2ldL.js";import{_ as <PERSON>}from"./GallerySelectDialog.vue_vue_type_script_setup_true_lang-CrzPKmtJ.js";function Ee(W,P){return l(),r("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M13.5 4.5 21 12m0 0-7.5 7.5M21 12H3"})])}function Me(W,P){return l(),r("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M5.25 5.653c0-.856.917-1.398 1.667-.986l11.54 6.347a1.125 1.125 0 0 1 0 1.972l-11.54 6.347a1.125 1.125 0 0 1-1.667-.986V5.653Z"})])}function ye(W,P){return l(),r("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m7.848 8.25 1.536.887M7.848 8.25a3 3 0 1 1-5.196-3 3 3 0 0 1 5.196 3Zm1.536.887a2.165 2.165 0 0 1 1.083 1.839c.005.351.054.695.14 1.024M9.384 9.137l2.077 1.199M7.848 15.75l1.536-.887m-1.536.887a3 3 0 1 1-5.196 3 3 3 0 0 1 5.196-3Zm1.536-.887a2.165 2.165 0 0 0 1.083-1.838c.005-.352.054-.695.14-1.025m-1.223 2.863 2.077-1.199m0-3.328a4.323 4.323 0 0 1 2.068-1.379l5.325-1.628a4.5 4.5 0 0 1 2.48-.044l.803.215-7.794 4.5m-2.882-1.664A4.33 4.33 0 0 0 10.607 12m3.736 0 7.794 4.5-.802.215a4.5 4.5 0 0 1-2.48-.043l-5.326-1.629a4.324 4.324 0 0 1-2.068-1.379M14.343 12l-2.882 1.664"})])}function he(W,P){return l(),r("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M9.813 15.904 9 18.75l-.813-2.846a4.5 4.5 0 0 0-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 0 0 3.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 0 0 3.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 0 0-3.09 3.09ZM18.259 8.715 18 9.75l-.259-1.035a3.375 3.375 0 0 0-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 0 0 2.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 0 0 2.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 0 0-2.456 2.456ZM16.894 20.567 16.5 21.75l-.394-1.183a2.25 2.25 0 0 0-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 0 0 1.423-1.423l.394-1.183.394 1.183a2.25 2.25 0 0 0 1.423 1.423l1.183.394-1.183.394a2.25 2.25 0 0 0-1.423 1.423Z"})])}function Se(W,P){return l(),r("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M5.25 7.5A2.25 2.25 0 0 1 7.5 5.25h9a2.25 2.25 0 0 1 2.25 2.25v9a2.25 2.25 0 0 1-2.25 2.25h-9a2.25 2.25 0 0 1-2.25-2.25v-9Z"})])}const me=D([]),Ve=D([]),ge=D(!1),ne=[{id:"WF001",name:"商品采集+智能裁图+批量刊登",apps:[{id:"app1",name:"商品采集",type:"product-collection",settings:{mode:"auto",dataSource:"search",timeout:30,onError:"stop"}},{id:"app2",name:"智能裁图",type:"smart-crop",settings:{mode:"auto",dataSource:"previous",timeout:20,onError:"skip"}},{id:"app3",name:"批量刊登",type:"batch-listing",settings:{mode:"manual",dataSource:"previous",timeout:60,onError:"stop"}}],usageCount:156,status:"enabled",creator:"张三",createTime:"2024-01-15 10:30:00"},{id:"WF002",name:"一键抠图+超级裂变",apps:[{id:"app1",name:"一键抠图",type:"one-click-cutout",settings:{mode:"auto",dataSource:"gallery",timeout:25,onError:"retry"}},{id:"app2",name:"超级裂变",type:"super-split",settings:{mode:"auto",dataSource:"previous",timeout:30,onError:"stop"}}],usageCount:89,status:"enabled",creator:"李四",createTime:"2024-01-14 14:20:00"},{id:"WF003",name:"标题生成+POD合成+批量刊登",apps:[{id:"app1",name:"标题生成",type:"title-generator",settings:{mode:"manual",dataSource:"gallery",timeout:15,onError:"stop"}},{id:"app2",name:"POD合成",type:"pod-compose",settings:{mode:"auto",dataSource:"previous",timeout:45,onError:"retry"}},{id:"app3",name:"批量刊登",type:"batch-listing",settings:{mode:"manual",dataSource:"previous",timeout:60,onError:"stop"}}],usageCount:234,status:"disabled",creator:"王五",createTime:"2024-01-13 09:15:00"},{id:"WF004",name:"智能裁图+一键抠图",apps:[{id:"app1",name:"智能裁图",type:"smart-crop",settings:{mode:"auto",dataSource:"gallery",timeout:20,onError:"skip"}},{id:"app2",name:"一键抠图",type:"one-click-cutout",settings:{mode:"auto",dataSource:"previous",timeout:25,onError:"retry"}}],usageCount:67,status:"enabled",creator:"赵六",createTime:"2024-01-12 16:45:00"},{id:"WF005",name:"完整电商流程",apps:[{id:"app1",name:"商品采集",type:"product-collection",settings:{mode:"auto",dataSource:"search",timeout:30,onError:"stop"}},{id:"app2",name:"智能裁图",type:"smart-crop",settings:{mode:"auto",dataSource:"previous",timeout:20,onError:"skip"}},{id:"app3",name:"一键抠图",type:"one-click-cutout",settings:{mode:"auto",dataSource:"previous",timeout:25,onError:"retry"}},{id:"app4",name:"标题生成",type:"title-generator",settings:{mode:"manual",dataSource:"previous",timeout:15,onError:"stop"}},{id:"app5",name:"批量刊登",type:"batch-listing",settings:{mode:"manual",dataSource:"previous",timeout:60,onError:"stop"}}],usageCount:423,status:"enabled",creator:"孙七",createTime:"2024-01-11 11:20:00"}],qe=()=>{me.value=[...ne]},Ze=()=>me.value,Ge=async W=>{ge.value=!0;try{await new Promise(N=>setTimeout(N,1e3));const P={id:`WF${String(me.value.length+1).padStart(3,"0")}`,name:W.name,apps:W.apps,usageCount:0,status:"enabled",creator:"当前用户",createTime:new Date().toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"})};return me.value.unshift(P),P}finally{ge.value=!1}},Xe=[{id:"EXE001",workflowId:"WF001",workflowName:"商品采集+智能裁图+批量刊登",workflow:ne[0],status:"completed",stepResults:[{appId:"app1",appName:"商品采集",status:"completed",startTime:"2024-01-15 10:30:00",endTime:"2024-01-15 10:35:00",duration:"5分钟",inputCount:0,outputCount:50},{appId:"app2",appName:"智能裁图",status:"completed",startTime:"2024-01-15 10:35:00",endTime:"2024-01-15 10:40:00",duration:"5分钟",inputCount:50,outputCount:50},{appId:"app3",appName:"批量刊登",status:"completed",startTime:"2024-01-15 10:40:00",endTime:"2024-01-15 10:45:00",duration:"5分钟",inputCount:50,outputCount:48}],startTime:"2024-01-15 10:30:00",endTime:"2024-01-15 10:45:00",duration:"15分钟",executor:"张三"},{id:"EXE002",workflowId:"WF002",workflowName:"一键抠图+超级裂变",workflow:ne[1],status:"running",stepResults:[{appId:"app1",appName:"一键抠图",status:"completed",startTime:"2024-01-15 14:20:00",endTime:"2024-01-15 14:25:00",duration:"5分钟",inputCount:30,outputCount:30},{appId:"app2",appName:"超级裂变",status:"running",startTime:"2024-01-15 14:25:00",inputCount:30,outputCount:0}],startTime:"2024-01-15 14:20:00",duration:"8分钟",executor:"李四"},{id:"EXE003",workflowId:"WF001",workflowName:"商品采集+智能裁图+批量刊登",workflow:ne[0],status:"failed",stepResults:[{appId:"app1",appName:"商品采集",status:"completed",startTime:"2024-01-15 09:00:00",endTime:"2024-01-15 09:05:00",duration:"5分钟",inputCount:0,outputCount:25},{appId:"app2",appName:"智能裁图",status:"failed",startTime:"2024-01-15 09:05:00",endTime:"2024-01-15 09:07:00",duration:"2分钟",inputCount:25,outputCount:0,errorMessage:"图片格式不支持"}],startTime:"2024-01-15 09:00:00",endTime:"2024-01-15 09:07:00",duration:"7分钟",executor:"王五"},{id:"EXE004",workflowId:"WF005",workflowName:"完整电商流程",workflow:ne[4],status:"completed",stepResults:[{appId:"app1",appName:"商品采集",status:"completed",startTime:"2024-01-14 16:00:00",endTime:"2024-01-14 16:10:00",duration:"10分钟",inputCount:0,outputCount:100},{appId:"app2",appName:"智能裁图",status:"completed",startTime:"2024-01-14 16:10:00",endTime:"2024-01-14 16:20:00",duration:"10分钟",inputCount:100,outputCount:100},{appId:"app3",appName:"一键抠图",status:"completed",startTime:"2024-01-14 16:20:00",endTime:"2024-01-14 16:30:00",duration:"10分钟",inputCount:100,outputCount:95},{appId:"app4",appName:"标题生成",status:"completed",startTime:"2024-01-14 16:30:00",endTime:"2024-01-14 16:35:00",duration:"5分钟",inputCount:95,outputCount:95},{appId:"app5",appName:"批量刊登",status:"completed",startTime:"2024-01-14 16:35:00",endTime:"2024-01-14 16:45:00",duration:"10分钟",inputCount:95,outputCount:92}],startTime:"2024-01-14 16:00:00",endTime:"2024-01-14 16:45:00",duration:"45分钟",executor:"孙七"},{id:"EXE005",workflowId:"WF003",workflowName:"标题生成+POD合成+批量刊登",workflow:ne[2],status:"pending",stepResults:[{appId:"app1",appName:"标题生成",status:"pending"},{appId:"app2",appName:"POD合成",status:"pending"},{appId:"app3",appName:"批量刊登",status:"pending"}],startTime:"2024-01-15 15:00:00",executor:"赵六"}],Je=async()=>{ge.value=!0;try{return await new Promise(W=>setTimeout(W,500)),Ve.value=[...Xe],Ve.value}finally{ge.value=!1}};B(()=>me.value),B(()=>Ve.value),B(()=>ge.value);const Ke={class:"mb-4"},Qe={class:"grid grid-cols-1 md:grid-cols-2 gap-6 max-h-[500px] overflow-y-auto"},Ye=["onClick"],et={class:"flex justify-between items-start mb-3"},tt={class:"text-lg font-semibold text-gray-900 dark:text-dark-text"},at={class:"text-sm text-gray-600 dark:text-dark-text-secondary mt-1"},lt={class:"mb-3"},ot={class:"flex items-center space-x-2 overflow-x-auto pb-2"},st={class:"flex items-center space-x-1 flex-shrink-0"},rt={class:"w-6 h-6 bg-green-500 rounded-full flex items-center justify-center"},dt={class:"flex items-center space-x-1 bg-blue-100 dark:bg-blue-900/30 rounded px-2 py-1 flex-shrink-0"},nt={class:"text-xs text-blue-700 dark:text-blue-300"},it={class:"flex items-center space-x-1 flex-shrink-0"},ut={class:"w-6 h-6 bg-red-500 rounded-full flex items-center justify-center"},ct={class:"flex justify-between items-center text-sm text-gray-500 dark:text-dark-text-secondary"},pt={key:0,class:"text-center py-12"},mt={class:"flex justify-end space-x-3"},gt=se({__name:"WorkflowTemplateDialog",props:{modelValue:{type:Boolean}},emits:["update:modelValue","select","create-blank"],setup(W,{emit:P}){const N=W,E=P,u=B({get:()=>N.modelValue,set:A=>E("update:modelValue",A)}),U=D(""),g=B(()=>Ze()),t=B(()=>U.value?g.value.filter(A=>A.name.toLowerCase().includes(U.value.toLowerCase())||A.apps.some(M=>M.name.toLowerCase().includes(U.value.toLowerCase()))):g.value),n=A=>({"product-collection":be,"smart-crop":ye,"one-click-cutout":he,"super-split":Z,"title-generator":fe,"batch-listing":ke,"pod-compose":Z})[A]||Z,C=A=>{E("select",A),p()},L=()=>{E("create-blank"),p()},p=()=>{U.value="",u.value=!1};return(A,M)=>{const d=S("el-input"),f=S("el-tag"),_=S("el-button"),v=S("el-dialog");return l(),F(v,{modelValue:u.value,"onUpdate:modelValue":M[1]||(M[1]=h=>u.value=h),title:"工作流模板",width:"1000px","close-on-click-modal":!1,class:"template-dialog"},{footer:c(()=>[e("div",mt,[a(_,{onClick:p,size:"large"},{default:c(()=>M[5]||(M[5]=[V(" 取消 ")])),_:1,__:[5]}),a(_,{onClick:L,type:"primary",size:"large",plain:""},{default:c(()=>M[6]||(M[6]=[V(" 创建空白工作流 ")])),_:1,__:[6]})])]),default:c(()=>[e("div",Ke,[a(d,{modelValue:U.value,"onUpdate:modelValue":M[0]||(M[0]=h=>U.value=h),placeholder:"搜索模板...",size:"large",clearable:""},{prefix:c(()=>[a(O(Ce),{class:"w-5 h-5 text-gray-400"})]),_:1},8,["modelValue"])]),e("div",Qe,[(l(!0),r(H,null,G(t.value,h=>(l(),r("div",{key:h.id,class:"bg-gray-50 dark:bg-dark-card rounded-lg p-4 border border-gray-200 dark:border-dark-border hover:border-blue-300 dark:hover:border-blue-600 cursor-pointer transition-all duration-200",onClick:k=>C(h)},[e("div",et,[e("div",null,[e("h3",tt,i(h.name),1),e("p",at,i(h.apps.length)+" 个应用 · 使用 "+i(h.usageCount)+" 次 ",1)]),a(f,{type:h.status==="enabled"?"success":"danger",size:"small"},{default:c(()=>[V(i(h.status==="enabled"?"可用":"禁用"),1)]),_:2},1032,["type"])]),e("div",lt,[e("div",ot,[e("div",st,[e("div",rt,[a(O(Me),{class:"w-3 h-3 text-white"})]),M[2]||(M[2]=e("span",{class:"text-xs text-gray-600 dark:text-dark-text-secondary"},"开始",-1))]),(l(!0),r(H,null,G(h.apps,(k,X)=>(l(),r(H,{key:X},[a(O(Ee),{class:"w-3 h-3 text-gray-400 flex-shrink-0"}),e("div",dt,[(l(),F(oe(n(k.type)),{class:"w-3 h-3 text-blue-600 dark:text-blue-400"})),e("span",nt,i(k.name),1)])],64))),128)),a(O(Ee),{class:"w-3 h-3 text-gray-400 flex-shrink-0"}),e("div",it,[e("div",ut,[a(O(Se),{class:"w-3 h-3 text-white"})]),M[3]||(M[3]=e("span",{class:"text-xs text-gray-600 dark:text-dark-text-secondary"},"结束",-1))])])]),e("div",ct,[e("span",null,"创建者："+i(h.creator),1),e("span",null,i(h.createTime),1)])],8,Ye))),128))]),t.value.length===0?(l(),r("div",pt,[a(O(Z),{class:"w-16 h-16 mx-auto text-gray-400 mb-4"}),M[4]||(M[4]=e("p",{class:"text-gray-500 dark:text-dark-text-secondary"},"没有找到匹配的模板",-1))])):w("",!0)]),_:1},8,["modelValue"])}}}),xt=xe(gt,[["__scopeId","data-v-56b174c9"]]),vt={class:"space-y-4"},kt={key:0,class:"space-y-3"},bt={key:1,class:"space-y-3"},ft={key:0,class:"grid grid-cols-2 gap-2"},yt={key:2,class:"space-y-3"},ht={key:0},wt={key:3,class:"space-y-3"},_t={key:4,class:"space-y-3"},$t={key:5,class:"space-y-3"},Vt={key:0},Ct={key:6,class:"space-y-3"},Mt=se({__name:"AppSpecificSettings",props:{appType:{},modelValue:{}},emits:["update:modelValue"],setup(W,{emit:P}){const N=W,E=P,u=B({get:()=>N.modelValue,set:g=>E("update:modelValue",g)});Ue(()=>N.appType,g=>{const n={...U(g),...N.modelValue};E("update:modelValue",n)},{immediate:!0});function U(g){return{"product-collection":{platform:"amazon",collectCount:50},"smart-crop":{cropSize:"800x800",customWidth:800,customHeight:800,quality:"standard"},"one-click-cutout":{cutoutMode:"precise",backgroundMode:"transparent",backgroundColor:"#ffffff"},"super-split":{splitCount:3,splitMode:"color"},"title-generator":{titleTemplate:"general",titleLength:"medium",keywordDensity:5},"batch-listing":{targetPlatform:["amazon"],listingMode:"immediate",scheduledTime:null},"pod-compose":{composeMode:"auto",outputFormat:"png",resolution:"300"}}[g]||{}}return(g,t)=>{const n=S("el-option"),C=S("el-select"),L=S("el-input-number"),p=S("el-color-picker"),A=S("el-slider"),M=S("el-date-picker");return l(),r("div",vt,[g.appType==="product-collection"?(l(),r("div",kt,[t[22]||(t[22]=e("div",{class:"text-sm font-medium text-gray-700 dark:text-dark-text border-b border-gray-200 dark:border-dark-border pb-2"}," 商品采集设置 ",-1)),e("div",null,[t[20]||(t[20]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text mb-1"},"采集平台",-1)),a(C,{modelValue:u.value.platform,"onUpdate:modelValue":t[0]||(t[0]=d=>u.value.platform=d),size:"small",class:"w-full",placeholder:"选择平台"},{default:c(()=>[a(n,{label:"Amazon",value:"amazon"}),a(n,{label:"Temu",value:"temu"}),a(n,{label:"Shein",value:"shein"})]),_:1},8,["modelValue"])]),e("div",null,[t[21]||(t[21]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text mb-1"},"采集数量",-1)),a(L,{modelValue:u.value.collectCount,"onUpdate:modelValue":t[1]||(t[1]=d=>u.value.collectCount=d),min:1,max:1e3,size:"small",class:"w-full"},null,8,["modelValue"])])])):g.appType==="smart-crop"?(l(),r("div",bt,[t[27]||(t[27]=e("div",{class:"text-sm font-medium text-gray-700 dark:text-dark-text border-b border-gray-200 dark:border-dark-border pb-2"}," 智能裁图设置 ",-1)),e("div",null,[t[23]||(t[23]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text mb-1"},"裁图尺寸",-1)),a(C,{modelValue:u.value.cropSize,"onUpdate:modelValue":t[2]||(t[2]=d=>u.value.cropSize=d),size:"small",class:"w-full",placeholder:"选择尺寸"},{default:c(()=>[a(n,{label:"800x800",value:"800x800"}),a(n,{label:"1024x1024",value:"1024x1024"}),a(n,{label:"自定义",value:"custom"})]),_:1},8,["modelValue"])]),u.value.cropSize==="custom"?(l(),r("div",ft,[e("div",null,[t[24]||(t[24]=e("label",{class:"block text-xs text-gray-600 dark:text-dark-text-secondary mb-1"},"宽度",-1)),a(L,{modelValue:u.value.customWidth,"onUpdate:modelValue":t[3]||(t[3]=d=>u.value.customWidth=d),min:100,max:2e3,size:"small",class:"w-full"},null,8,["modelValue"])]),e("div",null,[t[25]||(t[25]=e("label",{class:"block text-xs text-gray-600 dark:text-dark-text-secondary mb-1"},"高度",-1)),a(L,{modelValue:u.value.customHeight,"onUpdate:modelValue":t[4]||(t[4]=d=>u.value.customHeight=d),min:100,max:2e3,size:"small",class:"w-full"},null,8,["modelValue"])])])):w("",!0),e("div",null,[t[26]||(t[26]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text mb-1"},"裁图质量",-1)),a(C,{modelValue:u.value.quality,"onUpdate:modelValue":t[5]||(t[5]=d=>u.value.quality=d),size:"small",class:"w-full"},{default:c(()=>[a(n,{label:"高质量",value:"high"}),a(n,{label:"标准",value:"standard"}),a(n,{label:"快速",value:"fast"})]),_:1},8,["modelValue"])])])):g.appType==="one-click-cutout"?(l(),r("div",yt,[t[31]||(t[31]=e("div",{class:"text-sm font-medium text-gray-700 dark:text-dark-text border-b border-gray-200 dark:border-dark-border pb-2"}," 一键抠图设置 ",-1)),e("div",null,[t[28]||(t[28]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text mb-1"},"抠图模式",-1)),a(C,{modelValue:u.value.cutoutMode,"onUpdate:modelValue":t[6]||(t[6]=d=>u.value.cutoutMode=d),size:"small",class:"w-full"},{default:c(()=>[a(n,{label:"精确模式",value:"precise"}),a(n,{label:"快速模式",value:"fast"}),a(n,{label:"批量模式",value:"batch"})]),_:1},8,["modelValue"])]),e("div",null,[t[29]||(t[29]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text mb-1"},"背景处理",-1)),a(C,{modelValue:u.value.backgroundMode,"onUpdate:modelValue":t[7]||(t[7]=d=>u.value.backgroundMode=d),size:"small",class:"w-full"},{default:c(()=>[a(n,{label:"透明背景",value:"transparent"}),a(n,{label:"白色背景",value:"white"}),a(n,{label:"自定义颜色",value:"custom"})]),_:1},8,["modelValue"])]),u.value.backgroundMode==="custom"?(l(),r("div",ht,[t[30]||(t[30]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text mb-1"},"背景颜色",-1)),a(p,{modelValue:u.value.backgroundColor,"onUpdate:modelValue":t[8]||(t[8]=d=>u.value.backgroundColor=d),size:"small"},null,8,["modelValue"])])):w("",!0)])):g.appType==="super-split"?(l(),r("div",wt,[t[34]||(t[34]=e("div",{class:"text-sm font-medium text-gray-700 dark:text-dark-text border-b border-gray-200 dark:border-dark-border pb-2"}," 超级裂变设置 ",-1)),e("div",null,[t[32]||(t[32]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text mb-1"},"裂变数量",-1)),a(L,{modelValue:u.value.splitCount,"onUpdate:modelValue":t[9]||(t[9]=d=>u.value.splitCount=d),min:1,max:5,size:"small",class:"w-full"},null,8,["modelValue"])]),e("div",null,[t[33]||(t[33]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text mb-1"},"裂变模式",-1)),a(C,{modelValue:u.value.splitMode,"onUpdate:modelValue":t[10]||(t[10]=d=>u.value.splitMode=d),size:"small",class:"w-full"},{default:c(()=>[a(n,{label:"颜色变换",value:"color"}),a(n,{label:"风格变换",value:"style"}),a(n,{label:"混合模式",value:"mixed"})]),_:1},8,["modelValue"])])])):g.appType==="title-generator"?(l(),r("div",_t,[t[38]||(t[38]=e("div",{class:"text-sm font-medium text-gray-700 dark:text-dark-text border-b border-gray-200 dark:border-dark-border pb-2"}," 标题生成设置 ",-1)),e("div",null,[t[35]||(t[35]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text mb-1"},"生成模板",-1)),a(C,{modelValue:u.value.titleTemplate,"onUpdate:modelValue":t[11]||(t[11]=d=>u.value.titleTemplate=d),size:"small",class:"w-full"},{default:c(()=>[a(n,{label:"Amazon模板",value:"amazon"}),a(n,{label:"Temu模板",value:"temu"}),a(n,{label:"通用模板",value:"general"})]),_:1},8,["modelValue"])]),e("div",null,[t[36]||(t[36]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text mb-1"},"标题长度",-1)),a(C,{modelValue:u.value.titleLength,"onUpdate:modelValue":t[12]||(t[12]=d=>u.value.titleLength=d),size:"small",class:"w-full"},{default:c(()=>[a(n,{label:"短标题 (50字符)",value:"short"}),a(n,{label:"中等标题 (100字符)",value:"medium"}),a(n,{label:"长标题 (200字符)",value:"long"})]),_:1},8,["modelValue"])]),e("div",null,[t[37]||(t[37]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text mb-1"},"关键词密度",-1)),a(A,{modelValue:u.value.keywordDensity,"onUpdate:modelValue":t[13]||(t[13]=d=>u.value.keywordDensity=d),min:1,max:10,step:1,"show-stops":"",size:"small"},null,8,["modelValue"])])])):g.appType==="batch-listing"?(l(),r("div",$t,[t[42]||(t[42]=e("div",{class:"text-sm font-medium text-gray-700 dark:text-dark-text border-b border-gray-200 dark:border-dark-border pb-2"}," 批量刊登设置 ",-1)),e("div",null,[t[39]||(t[39]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text mb-1"},"目标平台",-1)),a(C,{modelValue:u.value.targetPlatform,"onUpdate:modelValue":t[14]||(t[14]=d=>u.value.targetPlatform=d),size:"small",class:"w-full",multiple:""},{default:c(()=>[a(n,{label:"Amazon",value:"amazon"}),a(n,{label:"eBay",value:"ebay"}),a(n,{label:"Shopify",value:"shopify"})]),_:1},8,["modelValue"])]),e("div",null,[t[40]||(t[40]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text mb-1"},"刊登模式",-1)),a(C,{modelValue:u.value.listingMode,"onUpdate:modelValue":t[15]||(t[15]=d=>u.value.listingMode=d),size:"small",class:"w-full"},{default:c(()=>[a(n,{label:"立即刊登",value:"immediate"}),a(n,{label:"定时刊登",value:"scheduled"}),a(n,{label:"草稿模式",value:"draft"})]),_:1},8,["modelValue"])]),u.value.listingMode==="scheduled"?(l(),r("div",Vt,[t[41]||(t[41]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text mb-1"},"刊登时间",-1)),a(M,{modelValue:u.value.scheduledTime,"onUpdate:modelValue":t[16]||(t[16]=d=>u.value.scheduledTime=d),type:"datetime",size:"small",class:"w-full",placeholder:"选择刊登时间"},null,8,["modelValue"])])):w("",!0)])):g.appType==="pod-compose"?(l(),r("div",Ct,[t[46]||(t[46]=e("div",{class:"text-sm font-medium text-gray-700 dark:text-dark-text border-b border-gray-200 dark:border-dark-border pb-2"}," POD合成设置 ",-1)),e("div",null,[t[43]||(t[43]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text mb-1"},"合成模式",-1)),a(C,{modelValue:u.value.composeMode,"onUpdate:modelValue":t[17]||(t[17]=d=>u.value.composeMode=d),size:"small",class:"w-full"},{default:c(()=>[a(n,{label:"自动合成",value:"auto"}),a(n,{label:"手动调整",value:"manual"}),a(n,{label:"批量合成",value:"batch"})]),_:1},8,["modelValue"])]),e("div",null,[t[44]||(t[44]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text mb-1"},"输出格式",-1)),a(C,{modelValue:u.value.outputFormat,"onUpdate:modelValue":t[18]||(t[18]=d=>u.value.outputFormat=d),size:"small",class:"w-full"},{default:c(()=>[a(n,{label:"PNG",value:"png"}),a(n,{label:"JPG",value:"jpg"}),a(n,{label:"PDF",value:"pdf"})]),_:1},8,["modelValue"])]),e("div",null,[t[45]||(t[45]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text mb-1"},"分辨率",-1)),a(C,{modelValue:u.value.resolution,"onUpdate:modelValue":t[19]||(t[19]=d=>u.value.resolution=d),size:"small",class:"w-full"},{default:c(()=>[a(n,{label:"300 DPI (高质量)",value:"300"}),a(n,{label:"150 DPI (标准)",value:"150"}),a(n,{label:"72 DPI (网络)",value:"72"})]),_:1},8,["modelValue"])])])):w("",!0)])}}}),St={class:"bg-gray-50 dark:bg-dark-card rounded-lg p-4 border border-gray-200 dark:border-dark-border"},Tt={key:0},zt={key:0},jt={class:"flex items-center justify-between mb-3"},Dt={key:0,class:"mt-2 text-sm text-blue-600 dark:text-blue-400"},It={key:1},Pt={class:"flex items-center justify-between mb-3"},Et={key:0,class:"mt-2 text-sm text-blue-600 dark:text-blue-400"},Ut={key:1},Bt={key:0},Nt={class:"flex items-center justify-between mb-3"},Lt={class:"flex space-x-2"},Wt={key:0,class:"space-y-2"},At={class:"text-sm text-blue-600 dark:text-blue-400"},Rt={class:"grid grid-cols-6 gap-2"},Ft=["src","alt"],Ot={key:0,class:"absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center text-white text-xs"},Ht={key:1,class:"text-center py-4 text-gray-500 dark:text-dark-text-secondary text-sm"},qt={key:1},Zt={class:"flex items-center justify-between mb-3"},Gt={key:0,class:"mt-3"},Xt={class:"text-sm text-blue-600 dark:text-blue-400 mb-2"},Jt={class:"grid grid-cols-6 gap-2"},Kt=["src","alt"],Qt={key:0,class:"absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center text-white text-xs"},Yt={key:2},ea={key:0},ta={class:"flex items-center justify-between mb-3"},aa={class:"flex space-x-2"},la={key:0,class:"text-sm text-blue-600 dark:text-blue-400"},oa={key:1,class:"text-center py-4 text-gray-500 dark:text-dark-text-secondary text-sm"},sa={key:1},ra={class:"flex items-center justify-between mb-3"},da={class:"flex space-x-2"},na={key:0,class:"text-sm text-blue-600 dark:text-blue-400"},ia={key:1,class:"text-center py-4 text-gray-500 dark:text-dark-text-secondary text-sm"},ua={key:3},ca={key:0},pa={class:"flex items-center justify-between mb-3"},ma={class:"flex space-x-2"},ga={key:0,class:"text-sm text-blue-600 dark:text-blue-400"},xa={key:1,class:"text-center py-4 text-gray-500 dark:text-dark-text-secondary text-sm"},va={key:1},ka={class:"text-center py-4"},ba={class:"text-sm text-blue-600 dark:text-blue-400"},fa={key:4,class:"text-center py-4"},ya=se({__name:"DataSelectionPanel",props:{appType:{},dataSource:{},modelValue:{}},emits:["update:modelValue"],setup(W,{emit:P}){const N={template:"<div></div>",emits:["update:modelValue","select"]},E={template:"<div></div>",emits:["update:modelValue","select"]},u=W,U=P,g=D({}),t=D(!1),n=D(!1),C=D(!1),L=D(),p=D([]),A=B(()=>["smart-crop","one-click-cutout","super-split","title-generator"].includes(u.appType)),M=B(()=>g.value.selectedImages||[]),d=B(()=>g.value.selectedProducts||[]),f=B(()=>g.value.links?g.value.links.split(`
`).filter(T=>T.trim()).length:0),_=B(()=>{if(u.dataSource==="previous")return!1;switch(u.appType){case"product-collection":return u.dataSource==="manual"?f.value>0:!!g.value.keyword;case"smart-crop":case"one-click-cutout":case"super-split":case"title-generator":return u.dataSource==="gallery"?M.value.length>0:p.value.length>0;case"batch-listing":case"pod-compose":return d.value.length>0;default:return!1}}),v=B(()=>156);Ue(()=>u.modelValue,T=>{g.value={...T}},{immediate:!0,deep:!0});const h=()=>{U("update:modelValue",{...g.value})},k=()=>{g.value={},p.value=[],h(),j.success("已清空选择")},X=()=>{t.value=!0},K=T=>{g.value.selectedImages=T,h(),j.success(`已选择 ${T.length} 张图片`)},te=()=>{n.value=!0,j.info("POD商品选择功能开发中...")},I=T=>{g.value.selectedProducts=T,h(),j.success(`已选择 ${T.length} 个POD商品`)},z=()=>{C.value=!0,j.info("白品选择功能开发中...")},ie=T=>{g.value.selectedProducts=T,h(),j.success(`已选择 ${T.length} 个白品`)},ue=(T,m)=>{p.value=m,g.value.uploadedFiles=m,h()},re=(T,m)=>{p.value=m,g.value.uploadedFiles=m,h()};return(T,m)=>{const q=S("el-button"),ae=S("el-input"),ce=S("el-upload");return l(),r("div",St,[T.appType==="product-collection"?(l(),r("div",Tt,[T.dataSource==="manual"?(l(),r("div",zt,[e("div",jt,[m[6]||(m[6]=e("label",{class:"text-sm font-medium text-gray-700 dark:text-dark-text"},"商品链接",-1)),_.value?(l(),F(q,{key:0,onClick:k,size:"small",type:"danger",plain:""},{default:c(()=>m[5]||(m[5]=[V(" 清空 ")])),_:1,__:[5]})):w("",!0)]),a(ae,{modelValue:g.value.links,"onUpdate:modelValue":m[0]||(m[0]=R=>g.value.links=R),type:"textarea",rows:3,placeholder:"请输入商品链接，每行一个",onInput:h},null,8,["modelValue"]),f.value>0?(l(),r("div",Dt," 已输入 "+i(f.value)+" 个链接 ",1)):w("",!0)])):T.dataSource==="search"?(l(),r("div",It,[e("div",Pt,[m[8]||(m[8]=e("label",{class:"text-sm font-medium text-gray-700 dark:text-dark-text"},"搜索关键词",-1)),_.value?(l(),F(q,{key:0,onClick:k,size:"small",type:"danger",plain:""},{default:c(()=>m[7]||(m[7]=[V(" 清空 ")])),_:1,__:[7]})):w("",!0)]),a(ae,{modelValue:g.value.keyword,"onUpdate:modelValue":m[1]||(m[1]=R=>g.value.keyword=R),placeholder:"请输入搜索关键词",onInput:h},null,8,["modelValue"]),g.value.keyword?(l(),r("div",Et," 关键词："+i(g.value.keyword),1)):w("",!0)])):w("",!0)])):A.value?(l(),r("div",Ut,[T.dataSource==="gallery"?(l(),r("div",Bt,[e("div",Nt,[m[11]||(m[11]=e("label",{class:"text-sm font-medium text-gray-700 dark:text-dark-text"},"选择图片",-1)),e("div",Lt,[a(q,{onClick:X,size:"small",type:"primary"},{default:c(()=>m[9]||(m[9]=[V(" 选择图片 ")])),_:1,__:[9]}),_.value?(l(),F(q,{key:0,onClick:k,size:"small",type:"danger",plain:""},{default:c(()=>m[10]||(m[10]=[V(" 清空 ")])),_:1,__:[10]})):w("",!0)])]),M.value.length>0?(l(),r("div",Wt,[e("div",At," 已选择 "+i(M.value.length)+" 张图片 ",1),e("div",Rt,[(l(!0),r(H,null,G(M.value.slice(0,6),(R,x)=>(l(),r("div",{key:R.id,class:"relative aspect-square bg-gray-100 dark:bg-dark-surface rounded-lg overflow-hidden"},[e("img",{src:R.url,alt:R.name,class:"w-full h-full object-cover"},null,8,Ft),x===5&&M.value.length>6?(l(),r("div",Ot," +"+i(M.value.length-6),1)):w("",!0)]))),128))])])):(l(),r("div",Ht,' 点击"选择图片"从图库中选择图片 '))])):T.dataSource==="upload"?(l(),r("div",qt,[e("div",Zt,[m[13]||(m[13]=e("label",{class:"text-sm font-medium text-gray-700 dark:text-dark-text"},"上传图片",-1)),_.value?(l(),F(q,{key:0,onClick:k,size:"small",type:"danger",plain:""},{default:c(()=>m[12]||(m[12]=[V(" 清空 ")])),_:1,__:[12]})):w("",!0)]),a(ce,{ref_key:"uploadRef",ref:L,"file-list":p.value,"on-change":ue,"on-remove":re,"auto-upload":!1,multiple:"",accept:"image/*","show-file-list":!1,drag:""},{default:c(()=>m[14]||(m[14]=[e("div",{class:"text-center py-6"},[e("svg",{class:"w-8 h-8 mx-auto mb-2 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"})]),e("p",{class:"text-sm text-gray-600 dark:text-dark-text-secondary"},[V(" 拖拽图片到此处或 "),e("span",{class:"text-blue-600"},"点击上传")])],-1)])),_:1,__:[14]},8,["file-list"]),p.value.length>0?(l(),r("div",Gt,[e("div",Xt," 已上传 "+i(p.value.length)+" 张图片 ",1),e("div",Jt,[(l(!0),r(H,null,G(p.value.slice(0,6),(R,x)=>(l(),r("div",{key:R.uid,class:"relative aspect-square bg-gray-100 dark:bg-dark-surface rounded-lg overflow-hidden"},[e("img",{src:R.url,alt:R.name,class:"w-full h-full object-cover"},null,8,Kt),x===5&&p.value.length>6?(l(),r("div",Qt," +"+i(p.value.length-6),1)):w("",!0)]))),128))])])):w("",!0)])):w("",!0)])):T.appType==="batch-listing"?(l(),r("div",Yt,[T.dataSource==="pod-products"?(l(),r("div",ea,[e("div",ta,[m[17]||(m[17]=e("label",{class:"text-sm font-medium text-gray-700 dark:text-dark-text"},"POD商品",-1)),e("div",aa,[a(q,{onClick:te,size:"small",type:"primary"},{default:c(()=>m[15]||(m[15]=[V(" 选择商品 ")])),_:1,__:[15]}),_.value?(l(),F(q,{key:0,onClick:k,size:"small",type:"danger",plain:""},{default:c(()=>m[16]||(m[16]=[V(" 清空 ")])),_:1,__:[16]})):w("",!0)])]),d.value.length>0?(l(),r("div",la," 已选择 "+i(d.value.length)+" 个POD商品 ",1)):(l(),r("div",oa,' 点击"选择商品"选择POD商品 '))])):T.dataSource==="white-products"?(l(),r("div",sa,[e("div",ra,[m[20]||(m[20]=e("label",{class:"text-sm font-medium text-gray-700 dark:text-dark-text"},"白品",-1)),e("div",da,[a(q,{onClick:z,size:"small",type:"primary"},{default:c(()=>m[18]||(m[18]=[V(" 选择白品 ")])),_:1,__:[18]}),_.value?(l(),F(q,{key:0,onClick:k,size:"small",type:"danger",plain:""},{default:c(()=>m[19]||(m[19]=[V(" 清空 ")])),_:1,__:[19]})):w("",!0)])]),d.value.length>0?(l(),r("div",na," 已选择 "+i(d.value.length)+" 个白品 ",1)):(l(),r("div",ia,' 点击"选择白品"选择白品商品 '))])):w("",!0)])):T.appType==="pod-compose"?(l(),r("div",ua,[T.dataSource==="white-products"?(l(),r("div",ca,[e("div",pa,[m[23]||(m[23]=e("label",{class:"text-sm font-medium text-gray-700 dark:text-dark-text"},"白品",-1)),e("div",ma,[a(q,{onClick:z,size:"small",type:"primary"},{default:c(()=>m[21]||(m[21]=[V(" 选择白品 ")])),_:1,__:[21]}),_.value?(l(),F(q,{key:0,onClick:k,size:"small",type:"danger",plain:""},{default:c(()=>m[22]||(m[22]=[V(" 清空 ")])),_:1,__:[22]})):w("",!0)])]),d.value.length>0?(l(),r("div",ga," 已选择 "+i(d.value.length)+" 个白品 ",1)):(l(),r("div",xa,' 点击"选择白品"选择白品商品 '))])):T.dataSource==="all-white-products"?(l(),r("div",va,[e("div",ka,[e("div",ba," 将使用全部白品（约 "+i(v.value)+" 个） ",1)])])):w("",!0)])):w("",!0),T.dataSource==="previous"?(l(),r("div",fa,m[24]||(m[24]=[e("div",{class:"text-sm text-gray-600 dark:text-dark-text-secondary"}," 将使用上一个应用的输出结果作为输入 ",-1)]))):w("",!0),a(He,{modelValue:t.value,"onUpdate:modelValue":m[2]||(m[2]=R=>t.value=R),"theme-color":"blue",onSelect:K},null,8,["modelValue"]),a(N,{modelValue:n.value,"onUpdate:modelValue":m[3]||(m[3]=R=>n.value=R),onSelect:I},null,8,["modelValue"]),a(E,{modelValue:C.value,"onUpdate:modelValue":m[4]||(m[4]=R=>C.value=R),onSelect:ie},null,8,["modelValue"])])}}}),ha={class:"flex items-center justify-between w-full"},wa={class:"flex items-center space-x-2"},_a={class:"flex h-[700px] -mx-6 -mt-4"},$a={class:"w-72 bg-gradient-to-b from-gray-50 to-gray-100 dark:from-dark-surface dark:to-dark-card border-r border-gray-200 dark:border-dark-border p-6"},Va={class:"mb-6"},Ca={class:"flex items-center justify-between mb-4"},Ma={class:"text-xs text-gray-500 dark:text-dark-text-secondary bg-white dark:bg-dark-surface px-2 py-1 rounded-full"},Sa={class:"relative"},Ta={class:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"},za={class:"space-y-3 max-h-[580px] overflow-y-auto custom-scrollbar"},ja=["onDragstart"],Da={class:"w-12 h-12 bg-gradient-to-br from-blue-100 to-blue-200 dark:from-blue-900/30 dark:to-blue-800/30 rounded-xl flex items-center justify-center mr-4 group-hover:scale-110 transition-transform duration-300"},Ia={class:"flex-1 min-w-0"},Pa={class:"text-sm font-semibold text-gray-900 dark:text-dark-text truncate mb-1"},Ea={class:"text-xs text-gray-500 dark:text-dark-text-secondary truncate"},Ua={key:0,class:"text-center py-12"},Ba={class:"flex-1 px-6 bg-white dark:bg-dark-card"},Na={class:"mb-6"},La={class:"flex items-center justify-between mb-4"},Wa={class:"flex items-center space-x-2"},Aa={class:"text-xs text-gray-500 dark:text-dark-text-secondary bg-gray-100 dark:bg-dark-surface px-2 py-1 rounded-full"},Ra={class:"relative"},Fa={class:"relative z-10 flex flex-col items-center space-y-8"},Oa={class:"flex flex-col items-center space-y-3"},Ha={class:"w-20 h-20 bg-gradient-to-br from-green-400 to-green-600 rounded-2xl flex items-center justify-center shadow-xl transform hover:scale-105 transition-transform duration-300"},qa={key:0,class:"flex flex-col items-center space-y-8 w-full"},Za={class:"w-full max-w-4xl"},Ga=["onClick"],Xa={class:"flex flex-col items-center space-y-3 p-4"},Ja={class:"relative"},Ka={class:"w-16 h-16 bg-gradient-to-br from-blue-400 to-purple-600 rounded-2xl flex items-center justify-center shadow-xl"},Qa={class:"absolute -top-2 -left-2 w-6 h-6 bg-gradient-to-br from-orange-400 to-red-500 rounded-full flex items-center justify-center text-xs font-bold text-white shadow-lg"},Ya=["onClick"],el=["onClick"],tl={class:"text-center"},al={class:"text-xs font-semibold text-gray-700 dark:text-dark-text bg-white dark:bg-dark-surface px-2 py-1 rounded-full shadow-md max-w-20 truncate block"},ll={class:"flex flex-col items-center space-y-3"},ol={class:"w-20 h-20 bg-gradient-to-br from-red-400 to-red-600 rounded-2xl flex items-center justify-center shadow-xl transform hover:scale-105 transition-transform duration-300"},sl={key:0,class:"absolute inset-0 flex items-center justify-center z-20"},rl={class:"text-center text-gray-500 dark:text-dark-text-secondary"},dl={class:"w-24 h-24 mx-auto mb-6 bg-gradient-to-br from-blue-100 to-purple-100 dark:from-blue-900/30 dark:to-purple-900/30 rounded-2xl flex items-center justify-center"},nl={class:"w-96 bg-gradient-to-b from-gray-50 to-gray-100 dark:from-dark-surface dark:to-dark-card border-l border-gray-200 dark:border-dark-border p-6"},il={key:0,class:"space-y-6"},ul={class:"bg-white dark:bg-dark-surface rounded-2xl p-6 shadow-lg border border-gray-200 dark:border-dark-border"},cl={class:"flex items-center space-x-4 mb-6"},pl={class:"w-14 h-14 bg-gradient-to-br from-blue-100 to-blue-200 dark:from-blue-900/30 dark:to-blue-800/30 rounded-2xl flex items-center justify-center"},ml={class:"flex-1"},gl={class:"font-bold text-gray-900 dark:text-dark-text text-lg"},xl={class:"text-sm text-gray-500 dark:text-dark-text-secondary mt-1"},vl={class:"flex items-center mt-2"},kl={class:"text-xs bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 px-2 py-1 rounded-full font-medium"},bl={class:"space-y-4"},fl={class:"block text-sm font-medium text-gray-700 dark:text-dark-text mb-2"},yl={key:0,class:"mt-4"},hl={key:1,class:"text-center text-gray-500 dark:text-dark-text-secondary py-8"},wl={class:"flex justify-end space-x-3"},_l=se({__name:"CreateWorkflowDialog",props:{modelValue:{type:Boolean}},emits:["update:modelValue","success"],setup(W,{emit:P}){const N=W,E=P,u=B({get:()=>N.modelValue,set:x=>E("update:modelValue",x)}),U=D(!1),g=D(""),t=D(null),n=D(!1),C=D(!1),L=Ne({name:""}),p=D([]),A=[{id:"product-collection",name:"商品采集",type:"product-collection",description:"采集电商平台商品信息"},{id:"smart-crop",name:"智能裁图",type:"smart-crop",description:"智能裁剪商品图片"},{id:"one-click-cutout",name:"一键抠图",type:"one-click-cutout",description:"自动抠图去背景"},{id:"super-split",name:"超级裂变",type:"super-split",description:"图片批量裂变处理"},{id:"title-generator",name:"标题生成",type:"title-generator",description:"智能生成商品标题"},{id:"batch-listing",name:"批量刊登",type:"batch-listing",description:"批量刊登商品到平台"},{id:"pod-compose",name:"POD合成",type:"pod-compose",description:"POD商品合成处理"}],M=B(()=>A.filter(x=>x.name.toLowerCase().includes(g.value.toLowerCase())||x.description.toLowerCase().includes(g.value.toLowerCase()))),d=B(()=>L.name.trim()&&p.value.length>0),f=x=>({"product-collection":be,"smart-crop":ye,"one-click-cutout":he,"super-split":Z,"title-generator":fe,"batch-listing":ke,"pod-compose":Z})[x]||Z,_=()=>{j.info("跳转到应用市场功能开发中...")},v=x=>{L.name=`${x.name} - 副本`,p.value=x.apps.map(o=>{const Q=A.find(de=>de.type===o.type);return{id:`app_${Date.now()}_${Math.random().toString(36).substring(2,9)}`,name:o.name,type:o.type,description:(Q==null?void 0:Q.description)||"",settings:{...o.settings,dataSource:o.settings.dataSource||ae(o.type)},datasetConfig:{}}}),n.value=!1,j.success(`已从模板"${x.name}"加载配置`)},h=(x,o)=>{x.dataTransfer&&(x.dataTransfer.setData("application/json",JSON.stringify(o)),x.dataTransfer.effectAllowed="copy")},k=x=>{x.preventDefault(),C.value=!0,x.dataTransfer&&(x.dataTransfer.dropEffect="copy")},X=x=>{x.preventDefault(),C.value=!1},K=x=>{if(x.preventDefault(),C.value=!1,x.dataTransfer)try{const o=JSON.parse(x.dataTransfer.getData("application/json"));te(o),j.success(`已添加应用：${o.name}`)}catch(o){console.error("Failed to parse dropped data:",o),j.error("添加应用失败")}},te=x=>{const o={...x,id:`${x.id}_${Date.now()}`,settings:{mode:"auto",dataSource:ae(x.type),timeout:30,onError:"stop"},datasetConfig:{}};p.value.push(o),t.value=p.value.length-1},I=x=>{p.value.splice(x,1),t.value===x?t.value=null:t.value!==null&&t.value>x&&t.value--},z=()=>{t.value=null},ie=()=>{p.value.length>0&&(p.value=[],t.value=null,j.success("工作流已清空"))},ue=async()=>{if(d.value){U.value=!0;try{await Ge({name:L.name,apps:p.value}),j.success("工作流创建成功！"),E("success"),re()}catch{j.error("创建失败，请重试")}finally{U.value=!1}}},re=()=>{L.name="",p.value=[],t.value=null,g.value="",n.value=!1,u.value=!1},T=x=>({"product-collection":"采集来源","smart-crop":"图片来源","one-click-cutout":"图片来源","super-split":"图片来源","title-generator":"图片来源","batch-listing":"商品来源","pod-compose":"白品来源"})[x]||"数据来源",m=x=>({"product-collection":"选择采集来源","smart-crop":"选择图片来源","one-click-cutout":"选择图片来源","super-split":"选择图片来源","title-generator":"选择图片来源","batch-listing":"选择商品来源","pod-compose":"选择白品来源"})[x]||"选择数据来源",q=x=>({"product-collection":[{label:"使用上一步结果",value:"previous"},{label:"手动输入链接",value:"manual"},{label:"关键词搜索",value:"search"}],"smart-crop":[{label:"使用上一步结果",value:"previous"},{label:"从图库选择",value:"gallery"},{label:"上传图片",value:"upload"}],"one-click-cutout":[{label:"使用上一步结果",value:"previous"},{label:"从图库选择",value:"gallery"},{label:"上传图片",value:"upload"}],"super-split":[{label:"使用上一步结果",value:"previous"},{label:"从图库选择",value:"gallery"},{label:"上传图片",value:"upload"}],"title-generator":[{label:"使用上一步结果",value:"previous"},{label:"从图库选择",value:"gallery"},{label:"上传图片",value:"upload"}],"batch-listing":[{label:"使用上一步结果",value:"previous"},{label:"选择POD商品",value:"pod-products"},{label:"选择白品",value:"white-products"}],"pod-compose":[{label:"使用上一步结果",value:"previous"},{label:"选择白品",value:"white-products"},{label:"全部白品",value:"all-white-products"}]})[x]||[{label:"使用上一步结果",value:"previous"}],ae=x=>({"product-collection":"search","smart-crop":"gallery","one-click-cutout":"gallery","super-split":"gallery","title-generator":"gallery","batch-listing":"pod-products","pod-compose":"white-products"})[x]||"previous",ce=x=>{p.value[x].datasetConfig={}},R=x=>!(!x||!x.settings.dataSource||x.settings.dataSource==="previous");return(x,o)=>{const Q=S("el-button"),de=S("el-radio"),b=S("el-radio-group"),s=S("el-option"),J=S("el-select"),le=S("el-input-number"),we=S("el-dialog");return l(),F(we,{modelValue:u.value,"onUpdate:modelValue":o[13]||(o[13]=$=>u.value=$),width:"1400px","close-on-click-modal":!1,"close-on-press-escape":!1,class:"workflow-dialog","align-center":""},{header:c(()=>[e("div",ha,[o[16]||(o[16]=e("div",{class:"flex items-center space-x-3"},[e("div",{class:"w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 10V3L4 14h7v7l9-11h-7z"})])]),e("div",null,[e("h2",{class:"text-xl font-bold text-gray-900 dark:text-dark-text"},"新建工作流"),e("p",{class:"text-sm text-gray-500 dark:text-dark-text-secondary"},"拖拽应用构建自动化工作流程")])],-1)),e("div",wa,[a(Q,{onClick:o[0]||(o[0]=$=>n.value=!0),size:"small",type:"info",plain:"",class:"transform hover:scale-105 transition-transform duration-200"},{default:c(()=>o[14]||(o[14]=[e("svg",{class:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"})],-1),V(" 选择模板 ")])),_:1,__:[14]}),a(Q,{onClick:_,type:"primary",size:"small",plain:"",class:"transform hover:scale-105 transition-transform duration-200"},{default:c(()=>o[15]||(o[15]=[e("svg",{class:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"})],-1),V(" 应用市场 ")])),_:1,__:[15]})])])]),footer:c(()=>[e("div",wl,[a(Q,{onClick:re,size:"large"},{default:c(()=>o[38]||(o[38]=[V("取消")])),_:1,__:[38]}),a(Q,{onClick:ue,type:"primary",size:"large",loading:U.value,disabled:!d.value},{default:c(()=>[V(i(U.value?"创建中...":"确定创建"),1)]),_:1},8,["loading","disabled"])])]),default:c(()=>[e("div",_a,[e("div",$a,[e("div",Va,[e("div",Ca,[o[17]||(o[17]=e("h3",{class:"text-lg font-bold text-gray-900 dark:text-dark-text flex items-center"},[e("svg",{class:"w-5 h-5 mr-2 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"})]),V(" 可用应用 ")],-1)),e("div",Ma,i(M.value.length)+" 个 ",1)]),e("div",Sa,[e("div",Ta,[a(O(Ce),{class:"w-4 h-4 text-gray-400"})]),pe(e("input",{"onUpdate:modelValue":o[1]||(o[1]=$=>g.value=$),placeholder:"搜索应用...",class:"w-full pl-10 pr-4 py-2.5 bg-white dark:bg-dark-surface border border-gray-200 dark:border-dark-border rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 text-sm"},null,512),[[$e,g.value]])])]),e("div",za,[(l(!0),r(H,null,G(M.value,$=>(l(),r("div",{key:$.id,class:"group relative flex items-center p-4 bg-white dark:bg-dark-surface rounded-xl cursor-pointer hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-all duration-300 transform hover:scale-105 hover:shadow-lg border border-gray-100 dark:border-dark-border hover:border-blue-200 dark:hover:border-blue-700",draggable:"true",onDragstart:Y=>h(Y,$)},[e("div",Da,[(l(),F(oe(f($.type)),{class:"w-6 h-6 text-blue-600 dark:text-blue-400"}))]),e("div",Ia,[e("div",Pa,i($.name),1),e("div",Ea,i($.description),1)]),o[18]||(o[18]=e("div",{class:"flex items-center space-x-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300"},[e("div",{class:"text-xs text-blue-600 dark:text-blue-400 font-medium"},"拖拽"),e("svg",{class:"w-4 h-4 text-blue-600 dark:text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 16l-4-4m0 0l4-4m-4 4h18"})])],-1)),o[19]||(o[19]=e("div",{class:"absolute left-0 top-0 bottom-0 w-1 bg-blue-500 rounded-r-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"},null,-1))],40,ja))),128)),M.value.length===0?(l(),r("div",Ua,o[20]||(o[20]=[e("svg",{class:"w-12 h-12 mx-auto text-gray-400 mb-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9.172 16.172a4 4 0 015.656 0M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})],-1),e("p",{class:"text-sm text-gray-500 dark:text-dark-text-secondary"},"未找到匹配的应用",-1)]))):w("",!0)])]),e("div",Ba,[e("div",Na,[e("div",La,[o[21]||(o[21]=e("h3",{class:"text-lg font-bold text-gray-900 dark:text-dark-text flex items-center"},[e("svg",{class:"w-5 h-5 mr-2 text-purple-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"})]),V(" 工作流设计 ")],-1)),e("div",Wa,[e("div",Aa,i(p.value.length)+" 个应用 ",1),e("button",{onClick:ie,class:"text-xs text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 bg-red-50 dark:bg-red-900/20 hover:bg-red-100 dark:hover:bg-red-900/30 px-2 py-1 rounded-full transition-all duration-200"}," 清空 ")])]),e("div",Ra,[pe(e("input",{"onUpdate:modelValue":o[2]||(o[2]=$=>L.name=$),placeholder:"请输入工作流名称...",class:"w-full px-4 py-3 bg-gray-50 dark:bg-dark-surface border border-gray-200 dark:border-dark-border rounded-xl focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200 text-gray-900 dark:text-dark-text placeholder-gray-500"},null,512),[[$e,L.name]]),o[22]||(o[22]=e("div",{class:"absolute inset-y-0 right-0 pr-3 flex items-center"},[e("svg",{class:"w-5 h-5 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"})])],-1))])]),e("div",{class:ee(["bg-gradient-to-br from-gray-50 to-gray-100 dark:from-dark-surface dark:to-dark-border rounded-2xl p-8 min-h-[580px] border-2 border-dashed border-gray-300 dark:border-dark-border relative overflow-hidden transition-all duration-300",{"border-blue-400 bg-blue-50 dark:bg-blue-900/10 scale-105":C.value}]),onDrop:K,onDragover:k,onDragleave:X},[o[30]||(o[30]=e("div",{class:"absolute inset-0 opacity-5"},[e("div",{class:"absolute top-10 left-10 w-20 h-20 bg-blue-500 rounded-full"}),e("div",{class:"absolute top-32 right-16 w-16 h-16 bg-purple-500 rounded-full"}),e("div",{class:"absolute bottom-20 left-20 w-12 h-12 bg-green-500 rounded-full"}),e("div",{class:"absolute bottom-32 right-32 w-24 h-24 bg-orange-500 rounded-full"})],-1)),e("div",Fa,[e("div",Oa,[e("div",Ha,[a(O(Me),{class:"w-10 h-10 text-white"})]),o[23]||(o[23]=e("span",{class:"text-sm font-bold text-gray-700 dark:text-dark-text bg-white dark:bg-dark-surface px-3 py-1 rounded-full shadow-md"},"开始",-1))]),p.value.length>0?(l(),r("div",qa,[o[24]||(o[24]=e("div",{class:"flex flex-col items-center"},[e("div",{class:"w-1 h-12 bg-gradient-to-b from-green-400 to-blue-400 rounded-full"}),e("div",{class:"w-3 h-3 bg-blue-400 rounded-full animate-pulse"})],-1)),e("div",Za,[a(O(We),{modelValue:p.value,"onUpdate:modelValue":o[3]||(o[3]=$=>p.value=$),onEnd:z,"item-key":"id",animation:300,handle:".drag-handle","ghost-class":"sortable-ghost","chosen-class":"sortable-chosen","drag-class":"sortable-drag",class:"grid grid-cols-5 gap-6 justify-items-center"},{item:c(({element:$,index:Y})=>[e("div",{class:ee(["relative group cursor-pointer drag-handle transform transition-all duration-300 hover:scale-110",{"ring-4 ring-purple-400 ring-opacity-50 scale-110":t.value===Y,"hover:shadow-2xl":t.value!==Y}]),onClick:ve=>t.value=Y},[e("div",Xa,[e("div",Ja,[e("div",Ka,[(l(),F(oe(f($.type)),{class:"w-8 h-8 text-white"}))]),e("div",Qa,i(Y+1),1),e("button",{onClick:je(ve=>I(Y),["stop"]),class:"absolute -top-2 -right-2 w-6 h-6 bg-red-500 hover:bg-red-600 rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-200 shadow-lg transform hover:scale-110"},[a(O(Le),{class:"w-3 h-3 text-white"})],8,Ya),e("button",{onClick:je(ve=>t.value=Y,["stop"]),class:"absolute -bottom-2 -right-2 w-6 h-6 bg-blue-500 hover:bg-blue-600 rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-200 shadow-lg transform hover:scale-110"},[a(O(De),{class:"w-3 h-3 text-white"})],8,el)]),e("div",tl,[e("span",al,i($.name),1)])])],10,Ga)]),_:1},8,["modelValue"])]),o[25]||(o[25]=e("div",{class:"flex flex-col items-center"},[e("div",{class:"w-3 h-3 bg-purple-400 rounded-full animate-pulse"}),e("div",{class:"w-1 h-12 bg-gradient-to-b from-purple-400 to-red-400 rounded-full"})],-1))])):w("",!0),e("div",ll,[e("div",ol,[a(O(Se),{class:"w-10 h-10 text-white"})]),o[26]||(o[26]=e("span",{class:"text-sm font-bold text-gray-700 dark:text-dark-text bg-white dark:bg-dark-surface px-3 py-1 rounded-full shadow-md"},"结束",-1))])]),p.value.length===0?(l(),r("div",sl,[e("div",rl,[e("div",dl,[a(O(Z),{class:"w-12 h-12 text-blue-400 dark:text-blue-500"})]),o[27]||(o[27]=e("h4",{class:"text-lg font-semibold text-gray-700 dark:text-dark-text mb-2"},"开始构建工作流",-1)),o[28]||(o[28]=e("p",{class:"text-sm text-gray-500 dark:text-dark-text-secondary mb-4"},"拖拽左侧应用到此处开始构建工作流",-1)),o[29]||(o[29]=e("div",{class:"flex items-center justify-center space-x-2 text-xs text-gray-400 dark:text-dark-text-secondary"},[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 16l-4-4m0 0l4-4m-4 4h18"})]),e("span",null,"拖拽应用"),e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"})]),e("span",null,"配置设置"),e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"})]),e("span",null,"运行工作流")],-1))])])):w("",!0)],34)]),e("div",nl,[o[37]||(o[37]=e("div",{class:"mb-6"},[e("h3",{class:"text-lg font-bold text-gray-900 dark:text-dark-text flex items-center"},[e("svg",{class:"w-5 h-5 mr-2 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"}),e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"})]),V(" 应用设置 ")])],-1)),t.value!==null&&p.value[t.value]?(l(),r("div",il,[e("div",ul,[e("div",cl,[e("div",pl,[(l(),F(oe(f(p.value[t.value].type)),{class:"w-7 h-7 text-blue-600 dark:text-blue-400"}))]),e("div",ml,[e("div",gl,i(p.value[t.value].name),1),e("div",xl,i(p.value[t.value].description),1),e("div",vl,[e("span",kl," 步骤 "+i(t.value+1),1)])])]),e("div",bl,[e("div",null,[o[33]||(o[33]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text mb-2"},"执行模式",-1)),a(b,{modelValue:p.value[t.value].settings.mode,"onUpdate:modelValue":o[4]||(o[4]=$=>p.value[t.value].settings.mode=$),size:"small"},{default:c(()=>[a(de,{label:"auto"},{default:c(()=>o[31]||(o[31]=[V("自动执行")])),_:1,__:[31]}),a(de,{label:"manual"},{default:c(()=>o[32]||(o[32]=[V("手动确认")])),_:1,__:[32]})]),_:1},8,["modelValue"])]),e("div",null,[e("label",fl,i(T(p.value[t.value].type)),1),a(J,{modelValue:p.value[t.value].settings.dataSource,"onUpdate:modelValue":o[5]||(o[5]=$=>p.value[t.value].settings.dataSource=$),placeholder:m(p.value[t.value].type),size:"small",class:"w-full",onChange:o[6]||(o[6]=$=>ce(t.value))},{default:c(()=>[(l(!0),r(H,null,G(q(p.value[t.value].type),$=>(l(),F(s,{key:$.value,label:$.label,value:$.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","placeholder"])]),R(p.value[t.value])?(l(),r("div",yl,[a(ya,{"app-type":p.value[t.value].type,"data-source":p.value[t.value].settings.dataSource,modelValue:p.value[t.value].datasetConfig,"onUpdate:modelValue":o[7]||(o[7]=$=>p.value[t.value].datasetConfig=$)},null,8,["app-type","data-source","modelValue"])])):w("",!0),e("div",null,[o[34]||(o[34]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text mb-1"},"超时时间（分钟）",-1)),a(le,{modelValue:p.value[t.value].settings.timeout,"onUpdate:modelValue":o[8]||(o[8]=$=>p.value[t.value].settings.timeout=$),min:1,max:60,size:"small",class:"w-full"},null,8,["modelValue"])]),e("div",null,[o[35]||(o[35]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text mb-1"},"失败处理",-1)),a(J,{modelValue:p.value[t.value].settings.onError,"onUpdate:modelValue":o[9]||(o[9]=$=>p.value[t.value].settings.onError=$),size:"small",class:"w-full"},{default:c(()=>[a(s,{label:"停止工作流",value:"stop"}),a(s,{label:"跳过继续",value:"skip"}),a(s,{label:"重试",value:"retry"})]),_:1},8,["modelValue"])]),p.value[t.value]?(l(),F(Mt,{key:1,"app-type":p.value[t.value].type,modelValue:p.value[t.value].settings,"onUpdate:modelValue":o[10]||(o[10]=$=>p.value[t.value].settings=$)},null,8,["app-type","modelValue"])):w("",!0)])])])):(l(),r("div",hl,[a(O(De),{class:"w-12 h-12 mx-auto mb-2 opacity-50"}),o[36]||(o[36]=e("p",{class:"text-sm"},"选择一个应用节点进行设置",-1))]))])]),a(xt,{modelValue:n.value,"onUpdate:modelValue":o[11]||(o[11]=$=>n.value=$),onSelect:v,onCreateBlank:o[12]||(o[12]=$=>n.value=!1)},null,8,["modelValue"])]),_:1},8,["modelValue"])}}}),$l=xe(_l,[["__scopeId","data-v-f59f598e"]]),Vl={key:0,class:"space-y-6"},Cl={class:"bg-gray-50 dark:bg-dark-card rounded-lg p-4"},Ml={class:"grid grid-cols-2 gap-4"},Sl={class:"text-sm text-gray-900 dark:text-dark-text"},Tl={class:"text-sm text-gray-900 dark:text-dark-text"},zl={class:"text-sm text-gray-900 dark:text-dark-text"},jl={class:"text-sm text-gray-900 dark:text-dark-text"},Dl={class:"text-sm text-gray-900 dark:text-dark-text"},Il={class:"bg-white dark:bg-dark-surface rounded-lg border border-gray-200 dark:border-dark-border"},Pl={class:"p-4"},El={class:"space-y-4"},Ul={class:"flex flex-col items-center"},Bl={class:"flex-1 min-w-0"},Nl={class:"flex items-center justify-between mb-2"},Ll={class:"flex items-center space-x-2"},Wl={class:"text-base font-medium text-gray-900 dark:text-dark-text"},Al={class:"grid grid-cols-2 gap-4 text-sm"},Rl={key:0},Fl={class:"text-gray-900 dark:text-dark-text"},Ol={key:1},Hl={class:"text-gray-900 dark:text-dark-text"},ql={key:2},Zl={class:"text-gray-900 dark:text-dark-text"},Gl={key:3},Xl={class:"text-gray-900 dark:text-dark-text"},Jl={key:0,class:"mt-2 p-2 bg-red-50 dark:bg-red-900/20 rounded border border-red-200 dark:border-red-800"},Kl={class:"text-sm text-red-700 dark:text-red-300"},Ql={class:"flex justify-end"},Yl=se({__name:"ExecutionDetailsDialog",props:{modelValue:{type:Boolean},execution:{}},emits:["update:modelValue"],setup(W,{emit:P}){const N=W,E=P,u=B({get:()=>N.modelValue,set:d=>E("update:modelValue",d)}),U=d=>({"product-collection":be,"smart-crop":ye,"one-click-cutout":he,"super-split":Z,"title-generator":fe,"batch-listing":ke,"pod-compose":Z})[d]||Z,g=d=>({商品采集:"product-collection",智能裁图:"smart-crop",一键抠图:"one-click-cutout",超级裂变:"super-split",标题生成:"title-generator",批量刊登:"batch-listing",POD合成:"pod-compose"})[d]||"unknown",t=d=>{switch(d){case"completed":return"success";case"failed":return"danger";case"running":return"warning";case"pending":return"info";default:return"info"}},n=d=>{switch(d){case"completed":return"已完成";case"failed":return"失败";case"running":return"执行中";case"pending":return"等待中";default:return"未知"}},C=d=>t(d),L=d=>n(d),p=d=>{switch(d){case"completed":return"bg-green-500 text-white";case"failed":return"bg-red-500 text-white";case"running":return"bg-blue-500 text-white";case"pending":return"bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300";default:return"bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300"}},A=d=>{switch(d){case"completed":return"bg-green-300 dark:bg-green-600";case"failed":return"bg-red-300 dark:bg-red-600";case"running":return"bg-blue-300 dark:bg-blue-600";default:return"bg-gray-300 dark:bg-gray-600"}},M=()=>{u.value=!1};return(d,f)=>{const _=S("el-tag"),v=S("el-button"),h=S("el-dialog");return l(),F(h,{modelValue:u.value,"onUpdate:modelValue":f[0]||(f[0]=k=>u.value=k),title:"执行详情",width:"800px","close-on-click-modal":!1,class:"execution-dialog"},{footer:c(()=>[e("div",Ql,[a(v,{onClick:M,size:"large"},{default:c(()=>f[14]||(f[14]=[V("关闭")])),_:1,__:[14]})])]),default:c(()=>[d.execution?(l(),r("div",Vl,[e("div",Cl,[f[7]||(f[7]=e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-dark-text mb-3"},"基本信息",-1)),e("div",Ml,[e("div",null,[f[1]||(f[1]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text mb-1"},"执行ID",-1)),e("p",Sl,i(d.execution.id),1)]),e("div",null,[f[2]||(f[2]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text mb-1"},"工作流名称",-1)),e("p",Tl,i(d.execution.workflowName),1)]),e("div",null,[f[3]||(f[3]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text mb-1"},"执行状态",-1)),a(_,{type:t(d.execution.status),size:"small"},{default:c(()=>[V(i(n(d.execution.status)),1)]),_:1},8,["type"])]),e("div",null,[f[4]||(f[4]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text mb-1"},"执行人",-1)),e("p",zl,i(d.execution.executor),1)]),e("div",null,[f[5]||(f[5]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text mb-1"},"开始时间",-1)),e("p",jl,i(d.execution.startTime),1)]),e("div",null,[f[6]||(f[6]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text mb-1"},"执行时长",-1)),e("p",Dl,i(d.execution.duration||"进行中"),1)])])]),e("div",Il,[f[13]||(f[13]=e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-dark-text p-4 border-b border-gray-200 dark:border-dark-border"}," 执行步骤 ",-1)),e("div",Pl,[e("div",El,[(l(!0),r(H,null,G(d.execution.stepResults,(k,X)=>(l(),r("div",{key:k.appId,class:"flex items-start space-x-4"},[e("div",Ul,[e("div",{class:ee(["w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium",p(k.status)])},i(X+1),3),X<d.execution.stepResults.length-1?(l(),r("div",{key:0,class:ee(["w-px h-12 mt-2",A(k.status)])},null,2)):w("",!0)]),e("div",Bl,[e("div",Nl,[e("div",Ll,[(l(),F(oe(U(g(k.appName))),{class:"w-5 h-5 text-blue-600 dark:text-blue-400"})),e("h4",Wl,i(k.appName),1),a(_,{type:C(k.status),size:"small"},{default:c(()=>[V(i(L(k.status)),1)]),_:2},1032,["type"])])]),e("div",Al,[k.startTime?(l(),r("div",Rl,[f[8]||(f[8]=e("span",{class:"text-gray-600 dark:text-dark-text-secondary"},"开始时间：",-1)),e("span",Fl,i(k.startTime),1)])):w("",!0),k.duration?(l(),r("div",Ol,[f[9]||(f[9]=e("span",{class:"text-gray-600 dark:text-dark-text-secondary"},"执行时长：",-1)),e("span",Hl,i(k.duration),1)])):w("",!0),k.inputCount!==void 0?(l(),r("div",ql,[f[10]||(f[10]=e("span",{class:"text-gray-600 dark:text-dark-text-secondary"},"输入数量：",-1)),e("span",Zl,i(k.inputCount),1)])):w("",!0),k.outputCount!==void 0?(l(),r("div",Gl,[f[11]||(f[11]=e("span",{class:"text-gray-600 dark:text-dark-text-secondary"},"输出数量：",-1)),e("span",Xl,i(k.outputCount),1)])):w("",!0)]),k.errorMessage?(l(),r("div",Jl,[e("p",Kl,[f[12]||(f[12]=e("strong",null,"错误信息：",-1)),V(i(k.errorMessage),1)])])):w("",!0)])]))),128))])])])])):w("",!0)]),_:1},8,["modelValue"])}}}),eo=xe(Yl,[["__scopeId","data-v-5b52e5e1"]]),to={key:0,class:"space-y-6"},ao={class:"bg-gray-50 dark:bg-dark-card rounded-lg p-4"},lo={class:"grid grid-cols-4 gap-4"},oo={class:"text-center"},so={class:"text-2xl font-bold text-blue-600 dark:text-blue-400"},ro={class:"text-center"},no={class:"text-2xl font-bold text-green-600 dark:text-green-400"},io={class:"text-center"},uo={class:"text-2xl font-bold text-orange-600 dark:text-orange-400"},co={class:"text-center"},po={class:"text-2xl font-bold text-purple-600 dark:text-purple-400"},mo={class:"bg-white dark:bg-dark-surface rounded-lg border border-gray-200 dark:border-dark-border"},go={class:"p-4"},xo={class:"space-y-4"},vo={class:"grid grid-cols-3 gap-4"},ko={class:"bg-blue-50 dark:bg-blue-900/20 rounded-lg p-3 text-center"},bo={class:"text-lg font-semibold text-blue-600 dark:text-blue-400"},fo={class:"bg-green-50 dark:bg-green-900/20 rounded-lg p-3 text-center"},yo={class:"text-lg font-semibold text-green-600 dark:text-green-400"},ho={class:"bg-orange-50 dark:bg-orange-900/20 rounded-lg p-3 text-center"},wo={class:"text-lg font-semibold text-orange-600 dark:text-orange-400"},_o={class:"bg-gray-50 dark:bg-dark-card rounded-lg p-4"},$o={key:0},Vo={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"},Co=["src","alt"],Mo={class:"text-sm font-medium text-gray-900 dark:text-dark-text truncate"},So={class:"text-sm text-green-600 dark:text-green-400 font-semibold"},To={key:1},zo={class:"grid grid-cols-2 md:grid-cols-4 gap-4"},jo=["src","alt"],Do={class:"text-xs text-gray-600 dark:text-dark-text-secondary text-center"},Io={key:2},Po={class:"space-y-2"},Eo={class:"text-sm text-gray-900 dark:text-dark-text"},Uo={class:"text-xs text-gray-500 dark:text-dark-text-secondary mt-1"},Bo={key:3},No={class:"space-y-2"},Lo={class:"text-sm font-medium text-gray-900 dark:text-dark-text"},Wo={class:"text-xs text-gray-500 dark:text-dark-text-secondary"},Ao={key:4},Ro={class:"flex justify-between"},Fo=se({__name:"ResultsDialog",props:{modelValue:{type:Boolean},execution:{}},emits:["update:modelValue"],setup(W,{emit:P}){const N=W,E=P,u=B({get:()=>N.modelValue,set:_=>E("update:modelValue",_)}),U=D(""),g=B(()=>(N.execution&&N.execution.stepResults.length>0&&(U.value=N.execution.stepResults[0].appId),N.execution)),t=()=>{if(!g.value)return 0;const _=g.value.stepResults[0];return(_==null?void 0:_.inputCount)||0},n=()=>{if(!g.value)return 0;const _=g.value.stepResults[g.value.stepResults.length-1];return(_==null?void 0:_.outputCount)||0},C=()=>{const _=t(),v=n();return _===0?0:Math.round(v/_*100)},L=_=>Array.from({length:Math.min(_,6)},(v,h)=>({id:`product_${h+1}`,title:`商品标题 ${h+1} - 高质量产品描述`,price:(Math.random()*100+10).toFixed(2),image:`https://picsum.photos/200/200?random=${h+1}`})),p=_=>Array.from({length:Math.min(_,8)},(v,h)=>({id:`image_${h+1}`,name:`处理后图片_${h+1}.jpg`,image:`https://picsum.photos/150/150?random=${h+10}`})),A=_=>{const v=["高品质时尚T恤 - 舒适透气 多色可选 男女通用款式","精美陶瓷马克杯 - 创意设计 办公室必备 礼品首选","多功能手机壳 - 防摔保护 时尚外观 适配多机型","舒适运动鞋 - 轻便透气 专业运动 日常休闲两用","优质帆布包 - 大容量设计 环保材质 时尚百搭"];return Array.from({length:Math.min(_,5)},(h,k)=>({id:`title_${k+1}`,title:v[k%v.length],length:v[k%v.length].length}))},M=_=>{const v=["Amazon","eBay","Shopify","Etsy"];return Array.from({length:Math.min(_,8)},(h,k)=>({id:`listing_${k+1}`,platform:v[k%v.length],productId:`PRD${String(k+1).padStart(6,"0")}`,status:Math.random()>.2?"success":"failed"}))},d=()=>{j.success("结果下载功能开发中...")},f=()=>{u.value=!1};return(_,v)=>{const h=S("el-tag"),k=S("el-tab-pane"),X=S("el-tabs"),K=S("el-button"),te=S("el-dialog");return l(),F(te,{modelValue:u.value,"onUpdate:modelValue":v[1]||(v[1]=I=>u.value=I),title:"处理结果",width:"1000px","close-on-click-modal":!1,class:"results-dialog"},{footer:c(()=>[e("div",Ro,[a(K,{onClick:d,type:"primary",plain:""},{default:c(()=>v[13]||(v[13]=[V(" 下载结果 ")])),_:1,__:[13]}),a(K,{onClick:f,size:"large"},{default:c(()=>v[14]||(v[14]=[V("关闭")])),_:1,__:[14]})])]),default:c(()=>[g.value?(l(),r("div",to,[e("div",ao,[v[6]||(v[6]=e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-dark-text mb-3"},"处理概览",-1)),e("div",lo,[e("div",oo,[e("div",so,i(t()),1),v[2]||(v[2]=e("div",{class:"text-sm text-gray-600 dark:text-dark-text-secondary"},"总输入",-1))]),e("div",ro,[e("div",no,i(n()),1),v[3]||(v[3]=e("div",{class:"text-sm text-gray-600 dark:text-dark-text-secondary"},"总输出",-1))]),e("div",io,[e("div",uo,i(C())+"%",1),v[4]||(v[4]=e("div",{class:"text-sm text-gray-600 dark:text-dark-text-secondary"},"成功率",-1))]),e("div",co,[e("div",po,i(g.value.duration||"0分钟"),1),v[5]||(v[5]=e("div",{class:"text-sm text-gray-600 dark:text-dark-text-secondary"},"总耗时",-1))])])]),e("div",mo,[v[12]||(v[12]=e("div",{class:"p-4 border-b border-gray-200 dark:border-dark-border"},[e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-dark-text"},"各步骤处理结果")],-1)),e("div",go,[a(X,{modelValue:U.value,"onUpdate:modelValue":v[0]||(v[0]=I=>U.value=I),type:"border-card"},{default:c(()=>[(l(!0),r(H,null,G(g.value.stepResults,I=>(l(),F(k,{key:I.appId,label:I.appName,name:I.appId},{default:c(()=>[e("div",xo,[e("div",vo,[e("div",ko,[e("div",bo,i(I.inputCount||0),1),v[7]||(v[7]=e("div",{class:"text-sm text-gray-600 dark:text-dark-text-secondary"},"输入数量",-1))]),e("div",fo,[e("div",yo,i(I.outputCount||0),1),v[8]||(v[8]=e("div",{class:"text-sm text-gray-600 dark:text-dark-text-secondary"},"输出数量",-1))]),e("div",ho,[e("div",wo,i(I.duration||"0分钟"),1),v[9]||(v[9]=e("div",{class:"text-sm text-gray-600 dark:text-dark-text-secondary"},"处理时长",-1))])]),e("div",_o,[v[11]||(v[11]=e("h4",{class:"text-base font-medium text-gray-900 dark:text-dark-text mb-3"},"处理结果数据",-1)),I.appName==="商品采集"?(l(),r("div",$o,[e("div",Vo,[(l(!0),r(H,null,G(L(I.outputCount||0),z=>(l(),r("div",{key:z.id,class:"bg-white dark:bg-dark-surface rounded-lg p-3 border border-gray-200 dark:border-dark-border"},[e("img",{src:z.image,alt:z.title,class:"w-full h-32 object-cover rounded mb-2"},null,8,Co),e("h5",Mo,i(z.title),1),e("p",So,"$"+i(z.price),1)]))),128))])])):I.appName==="智能裁图"||I.appName==="一键抠图"?(l(),r("div",To,[e("div",zo,[(l(!0),r(H,null,G(p(I.outputCount||0),z=>(l(),r("div",{key:z.id,class:"bg-white dark:bg-dark-surface rounded-lg p-2 border border-gray-200 dark:border-dark-border"},[e("img",{src:z.image,alt:z.name,class:"w-full h-24 object-cover rounded mb-1"},null,8,jo),e("p",Do,i(z.name),1)]))),128))])])):I.appName==="标题生成"?(l(),r("div",Io,[e("div",Po,[(l(!0),r(H,null,G(A(I.outputCount||0),z=>(l(),r("div",{key:z.id,class:"bg-white dark:bg-dark-surface rounded-lg p-3 border border-gray-200 dark:border-dark-border"},[e("p",Eo,i(z.title),1),e("p",Uo,"长度: "+i(z.length)+" 字符",1)]))),128))])])):I.appName==="批量刊登"?(l(),r("div",Bo,[e("div",No,[(l(!0),r(H,null,G(M(I.outputCount||0),z=>(l(),r("div",{key:z.id,class:"bg-white dark:bg-dark-surface rounded-lg p-3 border border-gray-200 dark:border-dark-border flex justify-between items-center"},[e("div",null,[e("p",Lo,i(z.platform),1),e("p",Wo,i(z.productId),1)]),a(h,{type:z.status==="success"?"success":"danger",size:"small"},{default:c(()=>[V(i(z.status==="success"?"成功":"失败"),1)]),_:2},1032,["type"])]))),128))])])):(l(),r("div",Ao,v[10]||(v[10]=[e("p",{class:"text-gray-500 dark:text-dark-text-secondary text-center py-4"}," 暂无结果数据展示 ",-1)])))])])]),_:2},1032,["label","name"]))),128))]),_:1},8,["modelValue"])])])])):w("",!0)]),_:1},8,["modelValue"])}}}),Oo=xe(Fo,[["__scopeId","data-v-392a9730"]]),Ho={class:"space-y-6"},qo={class:"grid grid-cols-1 md:grid-cols-4 gap-6"},Zo={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},Go={class:"flex items-center justify-between"},Xo={class:"text-2xl font-bold text-gray-900 dark:text-dark-text"},Jo={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},Ko={class:"flex items-center justify-between"},Qo={class:"text-2xl font-bold text-green-600 dark:text-green-400"},Yo={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},es={class:"flex items-center justify-between"},ts={class:"text-2xl font-bold text-purple-600 dark:text-purple-400"},as={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},ls={class:"flex items-center justify-between"},os={class:"text-2xl font-bold text-orange-600 dark:text-orange-400"},ss={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border"},rs={class:"flex justify-between items-center"},ds={class:"flex items-center space-x-3"},ns={key:0,class:"flex items-center space-x-2 ml-4 pl-4 border-l border-gray-200 dark:border-dark-border"},is={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},us={class:"bg-white dark:bg-dark-card rounded-2xl shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border p-6"},cs={class:"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6"},ps={class:"flex flex-col sm:flex-row gap-4 flex-1"},ms={class:"relative flex-1 max-w-md"},gs={class:"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none"},xs={class:"relative"},vs={class:"flex items-center space-x-2"},ks={class:"flex items-center justify-between lg:justify-end gap-4"},bs={class:"flex items-center space-x-4"},fs={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},ys={class:"font-semibold text-gray-900 dark:text-dark-text"},hs={key:0,class:"flex items-center gap-3"},ws={class:"text-sm font-medium text-blue-600 dark:text-blue-400"},_s={class:"bg-white dark:bg-dark-surface rounded-xl shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border overflow-hidden"},$s={class:"overflow-x-auto"},Vs={class:"flex items-center space-x-3"},Cs={class:"font-medium text-gray-900 dark:text-dark-text"},Ms={class:"text-sm text-gray-500 dark:text-dark-text-secondary"},Ss={class:"py-2"},Ts={class:"flex items-center space-x-2 max-w-full"},zs={class:"flex flex-col items-center space-y-1 flex-shrink-0"},js={class:"w-6 h-6 bg-green-500 rounded-full flex items-center justify-center"},Ds={class:"flex items-center space-x-1 min-w-0"},Is={class:"flex flex-col items-center space-y-1 flex-shrink-0"},Ps={class:"text-xs text-gray-600 dark:text-dark-text-secondary text-center max-w-12 truncate"},Es={class:"flex flex-col items-center space-y-1 flex-shrink-0"},Us={class:"text-xs text-gray-600 dark:text-dark-text-secondary"},Bs={class:"flex flex-col items-center space-y-1 flex-shrink-0"},Ns={class:"w-6 h-6 bg-red-500 rounded-full flex items-center justify-center"},Ls={class:"space-y-2"},Ws={key:0,class:"text-xs text-gray-500 dark:text-dark-text-secondary"},As={class:"space-y-1"},Rs={class:"text-sm font-medium text-gray-900 dark:text-dark-text"},Fs={class:"text-xs text-gray-500 dark:text-dark-text-secondary"},Os={class:"flex items-center space-x-2"},Hs=["onClick"],qs={class:"flex justify-between items-center px-6 py-4 border-t border-gray-100 dark:border-dark-border bg-gray-50 dark:bg-dark-card/50"},Zs={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},Gs=se({__name:"index",setup(W){const P=D(""),N=D(""),E=D([]),u=D(!1),U=D(!1),g=D(!1),t=D(null),n=D({currentPage:1,pageSize:20,total:0}),C=D([]),L=D(!1),p=B(()=>{let b=C.value;return P.value&&(b=b.filter(s=>s.workflowName.toLowerCase().includes(P.value.toLowerCase())||s.id.toLowerCase().includes(P.value.toLowerCase()))),N.value&&(b=b.filter(s=>s.status===N.value)),b}),A=B(()=>{const b=(n.value.currentPage-1)*n.value.pageSize,s=b+n.value.pageSize;return p.value.slice(b,s)}),M=B(()=>C.value.length),d=B(()=>{if(C.value.length===0)return 0;const b=C.value.filter(s=>s.status==="completed").length;return Math.round(b/C.value.length*100)}),f=B(()=>new Set(C.value.map(s=>s.workflowId)).size),_=B(()=>C.value.filter(s=>s.status==="completed"&&s.duration).length===0?"0分钟":"3.5分钟"),v=b=>({"product-collection":be,"smart-crop":ye,"one-click-cutout":he,"super-split":Z,"title-generator":fe,"batch-listing":ke,"pod-compose":Z})[b]||Z,h=b=>{switch(b){case"completed":return"已完成";case"failed":return"失败";case"running":return"执行中";case"pending":return"等待中";default:return"未知"}},k=async()=>{L.value=!0;try{const b=await Je();C.value=b,n.value.total=p.value.length}catch{j.error("加载执行历史失败")}finally{L.value=!1}},X=b=>{E.value=b},K=()=>{n.value.currentPage=1,n.value.total=p.value.length},te=b=>{t.value=b,U.value=!0},I=b=>{t.value=b,g.value=!0},z=b=>{switch(b){case"completed":return"bg-green-100 dark:bg-green-900/30";case"failed":return"bg-red-100 dark:bg-red-900/30";case"running":return"bg-blue-100 dark:bg-blue-900/30";case"pending":return"bg-gray-100 dark:bg-gray-900/30";default:return"bg-gray-100 dark:bg-gray-900/30"}},ie=b=>{switch(b){case"completed":return"svg";case"failed":return"svg";case"running":return"svg";default:return"svg"}},ue=b=>{switch(b){case"completed":return"text-green-600 dark:text-green-400";case"failed":return"text-red-600 dark:text-red-400";case"running":return"text-blue-600 dark:text-blue-400";case"pending":return"text-gray-600 dark:text-gray-400";default:return"text-gray-600 dark:text-gray-400"}},re=b=>{switch(b){case"completed":return"bg-green-500";case"failed":return"bg-red-500";case"running":return"bg-blue-500";case"pending":return"bg-gray-400";default:return"bg-gray-400"}},T=b=>{switch(b){case"completed":return"bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400";case"failed":return"bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400";case"running":return"bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400";case"pending":return"bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400";default:return"bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400"}},m=b=>{switch(b){case"completed":return"bg-green-400";case"failed":return"bg-red-400";case"running":return"bg-blue-400";case"pending":return"bg-gray-400";default:return"bg-gray-400"}},q=b=>{const{action:s,row:J}=b;switch(s){case"viewResults":I(J);break;case"rerun":j.info("重新执行功能开发中...");break;case"duplicate":j.info("复制工作流功能开发中...");break;case"export":j.info("导出结果功能开发中...");break;case"delete":j.info("删除记录功能开发中...");break}},ae=()=>{j.success("导出执行历史功能开发中...")},ce=()=>{j.success("数据已刷新！"),k()},R=b=>{switch(b){case"today":j.info("显示今日执行记录");break;case"week":j.info("显示本周执行记录");break;default:return}K()},x=()=>{if(E.value.length===0){j.warning("请先选择要导出的记录");return}j.success(`正在导出 ${E.value.length} 条执行记录...`)},o=()=>{if(E.value.length===0){j.warning("请先选择要删除的记录");return}j.warning(`批量删除功能开发中... (已选择 ${E.value.length} 项)`)},Q=b=>{n.value.pageSize=b,n.value.currentPage=1,k()},de=b=>{n.value.currentPage=b,k()};return Ae(()=>{qe(),k()}),(b,s)=>{const J=S("el-table-column"),le=S("el-dropdown-item"),we=S("el-dropdown-menu"),$=S("el-dropdown"),Y=S("el-table"),ve=S("el-pagination"),Be=Oe("loading");return l(),r("div",Ho,[s[38]||(s[38]=Ie('<div class="bg-gradient-to-r from-primary-50 to-blue-50 dark:from-primary-900/20 dark:to-blue-900/20 rounded-2xl p-6 border border-primary-100 dark:border-primary-800" data-v-4a347d4f><div class="flex items-center space-x-3" data-v-4a347d4f><div class="w-10 h-10 bg-gradient-to-br from-primary-500 to-primary-600 rounded-xl flex items-center justify-center" data-v-4a347d4f><svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-4a347d4f><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" data-v-4a347d4f></path></svg></div><div data-v-4a347d4f><h1 class="text-2xl font-bold text-gray-900 dark:text-dark-text" data-v-4a347d4f>工作流管理</h1><p class="mt-1 text-sm text-gray-600 dark:text-dark-text-secondary" data-v-4a347d4f>创建、管理和监控您的自动化工作流程</p></div></div></div>',1)),e("div",qo,[e("div",Zo,[e("div",Go,[e("div",null,[s[10]||(s[10]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"总执行次数",-1)),e("p",Xo,i(M.value),1)]),s[11]||(s[11]=e("div",{class:"w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-blue-600 dark:text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})])],-1))])]),e("div",Jo,[e("div",Ko,[e("div",null,[s[12]||(s[12]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"成功率",-1)),e("p",Qo,i(d.value)+"%",1)]),s[13]||(s[13]=e("div",{class:"w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-green-600 dark:text-green-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1))])]),e("div",Yo,[e("div",es,[e("div",null,[s[14]||(s[14]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"活跃工作流",-1)),e("p",ts,i(f.value),1)]),s[15]||(s[15]=e("div",{class:"w-12 h-12 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-purple-600 dark:text-purple-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 10V3L4 14h7v7l9-11h-7z"})])],-1))])]),e("div",as,[e("div",ls,[e("div",null,[s[16]||(s[16]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"平均耗时",-1)),e("p",os,i(_.value),1)]),s[17]||(s[17]=e("div",{class:"w-12 h-12 bg-orange-100 dark:bg-orange-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-orange-600 dark:text-orange-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1))])])]),e("div",ss,[e("div",rs,[e("div",ds,[e("button",{onClick:s[0]||(s[0]=y=>u.value=!0),class:"inline-flex items-center px-4 py-2.5 bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 text-white font-medium rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"},[a(O(Re),{class:"w-5 h-5 mr-2"}),s[18]||(s[18]=V(" 新建工作流 "))]),e("button",{onClick:ae,class:"inline-flex items-center px-4 py-2.5 bg-white dark:bg-dark-card text-gray-700 dark:text-dark-text font-medium rounded-lg border border-gray-300 dark:border-dark-border hover:bg-gray-50 dark:hover:bg-dark-border shadow-sm hover:shadow-md transition-all duration-200"},[a(O(Pe),{class:"w-5 h-5 mr-2"}),s[19]||(s[19]=V(" 导出数据 "))]),E.value.length>0?(l(),r("div",ns,[e("span",is," 已选择 "+i(E.value.length)+" 项 ",1),e("button",{onClick:x,class:"inline-flex items-center px-3 py-1.5 bg-blue-500 hover:bg-blue-600 text-white text-sm font-medium rounded-lg transition-all duration-200"},[a(O(Pe),{class:"w-4 h-4 mr-1"}),s[20]||(s[20]=V(" 批量导出 "))])])):w("",!0)])])]),e("div",us,[e("div",cs,[e("div",ps,[e("div",ms,[e("div",gs,[a(O(Ce),{class:"w-5 h-5 text-gray-400"})]),pe(e("input",{"onUpdate:modelValue":s[1]||(s[1]=y=>P.value=y),onInput:K,type:"text",placeholder:"搜索工作流名称或ID...",class:"w-full pl-12 pr-4 py-3 bg-gray-50 dark:bg-dark-surface border border-gray-200 dark:border-dark-border rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 text-gray-900 dark:text-dark-text placeholder-gray-500 hover:border-gray-300 dark:hover:border-dark-text-secondary"},null,544),[[$e,P.value]])]),e("div",xs,[pe(e("select",{"onUpdate:modelValue":s[2]||(s[2]=y=>N.value=y),onChange:K,class:"appearance-none px-4 py-3 pr-10 bg-gray-50 dark:bg-dark-surface border border-gray-200 dark:border-dark-border rounded-xl text-gray-900 dark:text-dark-text focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 hover:border-gray-300 dark:hover:border-dark-text-secondary cursor-pointer"},s[21]||(s[21]=[Ie('<option value="" data-v-4a347d4f>全部状态</option><option value="completed" data-v-4a347d4f>已完成</option><option value="running" data-v-4a347d4f>执行中</option><option value="failed" data-v-4a347d4f>失败</option><option value="pending" data-v-4a347d4f>等待中</option>',5)]),544),[[Fe,N.value]]),s[22]||(s[22]=e("div",{class:"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none"},[e("svg",{class:"w-5 h-5 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 9l-7 7-7-7"})])],-1))]),e("div",vs,[e("button",{onClick:s[3]||(s[3]=y=>R("today")),class:"px-3 py-2 text-sm font-medium text-gray-600 dark:text-dark-text-secondary hover:text-blue-600 dark:hover:text-blue-400 bg-gray-100 dark:bg-dark-surface hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg transition-all duration-200"}," 今日 "),e("button",{onClick:s[4]||(s[4]=y=>R("week")),class:"px-3 py-2 text-sm font-medium text-gray-600 dark:text-dark-text-secondary hover:text-blue-600 dark:hover:text-blue-400 bg-gray-100 dark:bg-dark-surface hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg transition-all duration-200"}," 本周 ")])]),e("div",ks,[e("div",bs,[e("div",fs,[s[23]||(s[23]=V(" 共 ")),e("span",ys,i(n.value.total),1),s[24]||(s[24]=V(" 条记录 "))]),E.value.length>0?(l(),r("div",hs,[e("span",ws," 已选择 "+i(E.value.length)+" 项 ",1),e("div",{class:"flex space-x-2"},[e("button",{onClick:x,class:"px-3 py-2 text-sm font-medium text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20 hover:bg-blue-100 dark:hover:bg-blue-900/30 rounded-lg transition-all duration-200 transform hover:scale-105"}," 批量导出 "),e("button",{onClick:o,class:"px-3 py-2 text-sm font-medium text-red-600 dark:text-red-400 bg-red-50 dark:bg-red-900/20 hover:bg-red-100 dark:hover:bg-red-900/30 rounded-lg transition-all duration-200 transform hover:scale-105"}," 批量删除 ")])])):w("",!0)])])])]),e("div",_s,[s[37]||(s[37]=e("div",{class:"px-6 py-4 border-b border-gray-100 dark:border-dark-border"},[e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-dark-text"},"执行历史"),e("p",{class:"text-sm text-gray-600 dark:text-dark-text-secondary mt-1"},"管理和监控您的工作流执行记录")],-1)),e("div",$s,[pe((l(),F(Y,{data:A.value,style:{width:"100%"},"header-cell-style":{backgroundColor:"var(--el-bg-color-page)",color:"var(--el-text-color-primary)",fontWeight:"600",borderBottom:"1px solid var(--el-border-color-light)"},"row-style":{backgroundColor:"transparent"},class:"modern-table",onSelectionChange:X},{default:c(()=>[a(J,{type:"selection",width:"55",align:"center"}),a(J,{label:"执行信息","min-width":"200"},{default:c(y=>[e("div",Vs,[e("div",{class:ee(["w-10 h-10 rounded-lg flex items-center justify-center",z(y.row.status)])},[(l(),F(oe(ie(y.row.status)),{class:ee(["w-5 h-5",ue(y.row.status)])},null,8,["class"]))],2),e("div",null,[e("div",Cs,i(y.row.workflowName),1),e("div",Ms,"ID: "+i(y.row.id),1)])])]),_:1}),a(J,{label:"工作流程","min-width":"280"},{default:c(({row:y})=>[e("div",Ss,[e("div",Ts,[e("div",zs,[e("div",js,[a(O(Me),{class:"w-3 h-3 text-white"})]),s[25]||(s[25]=e("span",{class:"text-xs text-gray-600 dark:text-dark-text-secondary"},"开始",-1))]),e("div",Ds,[(l(!0),r(H,null,G(y.workflow.apps.slice(0,3),(_e,Te)=>{var ze;return l(),r(H,{key:Te},[s[26]||(s[26]=e("div",{class:"w-4 h-px bg-gray-300 dark:bg-gray-600 flex-shrink-0 mb-[20px]"},null,-1)),e("div",Is,[e("div",{class:ee(["w-6 h-6 rounded-full flex items-center justify-center",re((ze=y.stepResults[Te])==null?void 0:ze.status)])},[(l(),F(oe(v(_e.type)),{class:"w-3 h-3 text-white"}))],2),e("span",Ps,i(_e.name),1)])],64)}),128)),y.workflow.apps.length>3?(l(),r(H,{key:0},[s[28]||(s[28]=e("div",{class:"w-4 h-px bg-gray-300 dark:bg-gray-600 flex-shrink-0 mb-[20px]"},null,-1)),e("div",Es,[s[27]||(s[27]=e("div",{class:"w-6 h-6 bg-gray-400 rounded-full flex items-center justify-center"},[e("span",{class:"text-xs text-white font-bold"},"...")],-1)),e("span",Us," +"+i(y.workflow.apps.length-3),1)])],64)):w("",!0)]),s[30]||(s[30]=e("div",{class:"w-4 h-px bg-gray-300 dark:bg-gray-600 flex-shrink-0 mb-[20px]"},null,-1)),e("div",Bs,[e("div",Ns,[a(O(Se),{class:"w-3 h-3 text-white"})]),s[29]||(s[29]=e("span",{class:"text-xs text-gray-600 dark:text-dark-text-secondary"},"结束",-1))])])])]),_:1}),a(J,{label:"执行状态",width:"150"},{default:c(y=>[e("div",Ls,[e("span",{class:ee(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",T(y.row.status)])},[e("span",{class:ee(["w-1.5 h-1.5 rounded-full mr-1.5",m(y.row.status)])},null,2),V(" "+i(h(y.row.status)),1)],2),y.row.duration?(l(),r("div",Ws," 耗时: "+i(y.row.duration),1)):w("",!0)])]),_:1}),a(J,{label:"执行信息",width:"180"},{default:c(y=>[e("div",As,[e("div",Rs,i(y.row.executor),1),e("div",Fs,i(y.row.startTime),1)])]),_:1}),a(J,{label:"操作",width:"180"},{default:c(y=>[e("div",Os,[e("button",{onClick:_e=>te(y.row),class:"inline-flex items-center px-3 py-1.5 text-sm font-medium text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 bg-primary-50 dark:bg-primary-900/20 hover:bg-primary-100 dark:hover:bg-primary-900/30 rounded-lg transition-all duration-200"}," 查看详情 ",8,Hs),a($,{onCommand:q,trigger:"click"},{dropdown:c(()=>[a(we,null,{default:c(()=>[a(le,{command:{action:"viewResults",row:y.row},disabled:y.row.status!=="completed"},{default:c(()=>s[31]||(s[31]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-green-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})]),e("span",null,"处理结果")],-1)])),_:2,__:[31]},1032,["command","disabled"]),a(le,{command:{action:"rerun",row:y.row}},{default:c(()=>s[32]||(s[32]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-blue-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})]),e("span",null,"重新执行")],-1)])),_:2,__:[32]},1032,["command"]),a(le,{command:{action:"duplicate",row:y.row}},{default:c(()=>s[33]||(s[33]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-purple-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"})]),e("span",null,"复制工作流")],-1)])),_:2,__:[33]},1032,["command"]),a(le,{command:{action:"export",row:y.row}},{default:c(()=>s[34]||(s[34]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-orange-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})]),e("span",null,"导出结果")],-1)])),_:2,__:[34]},1032,["command"]),a(le,{command:{action:"delete",row:y.row},divided:""},{default:c(()=>s[35]||(s[35]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-red-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})]),e("span",null,"删除记录")],-1)])),_:2,__:[35]},1032,["command"])]),_:2},1024)]),default:c(()=>[s[36]||(s[36]=e("button",{class:"inline-flex items-center px-2 py-1.5 text-sm font-medium text-gray-600 dark:text-dark-text-secondary hover:text-gray-700 dark:hover:text-dark-text bg-gray-50 dark:bg-dark-card hover:bg-gray-100 dark:hover:bg-dark-border rounded-lg transition-all duration-200"},[V(" 更多 "),e("svg",{class:"w-4 h-4 ml-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 9l-7 7-7-7"})])],-1))]),_:2,__:[36]},1024)])]),_:1})]),_:1},8,["data"])),[[Be,L.value]])]),e("div",qs,[e("div",Zs," 共 "+i(n.value.total)+" 条记录 ",1),a(ve,{"current-page":n.value.currentPage,"onUpdate:currentPage":s[5]||(s[5]=y=>n.value.currentPage=y),"page-size":n.value.pageSize,"onUpdate:pageSize":s[6]||(s[6]=y=>n.value.pageSize=y),"page-sizes":[10,20,50,100],total:n.value.total,layout:"sizes, prev, pager, next, jumper",onSizeChange:Q,onCurrentChange:de,class:"modern-pagination"},null,8,["current-page","page-size","total"])])]),a($l,{modelValue:u.value,"onUpdate:modelValue":s[7]||(s[7]=y=>u.value=y),onSuccess:ce},null,8,["modelValue"]),a(eo,{modelValue:U.value,"onUpdate:modelValue":s[8]||(s[8]=y=>U.value=y),execution:t.value,onViewResults:I},null,8,["modelValue","execution"]),a(Oo,{modelValue:g.value,"onUpdate:modelValue":s[9]||(s[9]=y=>g.value=y),execution:t.value},null,8,["modelValue","execution"])])}}}),Ys=xe(Gs,[["__scopeId","data-v-4a347d4f"]]);export{Ys as default};
