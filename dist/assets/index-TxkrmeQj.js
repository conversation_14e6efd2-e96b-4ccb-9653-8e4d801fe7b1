import{c,a,o as g,d as J,r as n,K as H,f as K,g as o,j as l,h as b,A as M,M as L,C as j,E as m}from"./index-84KJ6UvQ.js";function I(_,u){return g(),c("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M6.827 6.175A2.31 2.31 0 0 1 5.186 7.23c-.38.054-.757.112-1.134.175C2.999 7.58 2.25 8.507 2.25 9.574V18a2.25 2.25 0 0 0 2.25 2.25h15A2.25 2.25 0 0 0 21.75 18V9.574c0-1.067-.75-1.994-1.802-2.169a47.865 47.865 0 0 0-1.134-.175 2.31 2.31 0 0 1-1.64-1.055l-.822-1.316a2.192 2.192 0 0 0-1.736-1.039 48.774 48.774 0 0 0-5.232 0 2.192 2.192 0 0 0-1.736 1.039l-.821 1.316Z"}),a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.5 12.75a4.5 4.5 0 1 1-9 0 4.5 4.5 0 0 1 9 0ZM18.75 10.5h.008v.008h-.008V10.5Z"})])}function O(_,u){return g(),c("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M12 16.5V9.75m0 0 3 3m-3-3-3 3M6.75 19.5a4.5 4.5 0 0 1-1.41-8.775 5.25 5.25 0 0 1 10.233-2.33 3 3 0 0 1 3.758 3.848A3.752 3.752 0 0 1 18 19.5H6.75Z"})])}const Q={class:"p-8"},S={class:"max-w-2xl"},W={class:"bg-white dark:bg-dark-card rounded-lg p-6 shadow-sm"},X={class:"flex items-center space-x-6"},Y={class:"relative"},ee=["src"],ae={class:"bg-white dark:bg-dark-card rounded-lg p-6 shadow-sm"},te={class:"space-y-4"},re={class:"flex items-center space-x-3"},oe={key:1,class:"flex space-x-2"},se={class:"flex items-center space-x-3"},le={key:1,class:"flex space-x-2"},de={class:"bg-white dark:bg-dark-card rounded-lg p-6 shadow-sm"},ne={class:"space-y-4"},ie={class:"flex justify-end space-x-3 pt-6"},ue=["loading"],me={class:"space-y-4"},ce={class:"text-center py-8"},ge={key:0,class:"text-center"},pe=["src"],xe={class:"flex justify-end space-x-3"},be=J({__name:"index",setup(_){const u=n(),P=n(),k=n(!1),w=n(!1),x=n(!1),v=n(!1),f=n(!1),p=n(""),t=H({nickname:"",phone:"",email:"",avatar:"",currentPassword:"",newPassword:"",confirmPassword:""}),h=n(""),y=n(""),E={nickname:[{required:!0,message:"请输入昵称",trigger:"blur"},{min:2,max:20,message:"昵称长度在 2 到 20 个字符",trigger:"blur"}],phone:[{required:!0,message:"请输入手机号",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号",trigger:"blur"}],email:[{required:!0,message:"请输入邮箱",trigger:"blur"},{type:"email",message:"请输入正确的邮箱格式",trigger:"blur"}],newPassword:[{min:6,max:20,message:"密码长度在 6 到 20 个字符",trigger:"blur"}],confirmPassword:[{validator:(r,e,d)=>{e&&e!==t.newPassword?d(new Error("两次输入的密码不一致")):d()},trigger:"blur"}]},V=()=>{t.nickname="张三",t.phone="13800138000",t.email="<EMAIL>",t.avatar="/default-avatar.png",h.value=t.phone,y.value=t.email},C=()=>{f.value=!0,p.value=""},U=r=>{if(r.raw){if(r.raw.size>2*1024*1024){m.error("文件大小不能超过 2MB");return}const e=new FileReader;e.onload=d=>{var i;p.value=(i=d.target)==null?void 0:i.result},e.readAsDataURL(r.raw)}},B=async()=>{if(!p.value){m.warning("请选择要上传的头像");return}w.value=!0;try{await new Promise(r=>setTimeout(r,1e3)),t.avatar=p.value,f.value=!1,m.success("头像更新成功")}catch{m.error("头像上传失败")}finally{w.value=!1}},F=()=>{var r;p.value="",(r=P.value)==null||r.clearFiles()},N=async()=>{var r;try{await((r=u.value)==null?void 0:r.validateField("phone")),await new Promise(e=>setTimeout(e,500)),h.value=t.phone,x.value=!1,m.success("手机号修改成功")}catch{}},R=()=>{t.phone=h.value,x.value=!1},T=async()=>{var r;try{await((r=u.value)==null?void 0:r.validateField("email")),await new Promise(e=>setTimeout(e,500)),y.value=t.email,v.value=!1,m.success("邮箱修改成功")}catch{}},$=()=>{t.email=y.value,v.value=!1},G=async()=>{if(u.value)try{await u.value.validate(),k.value=!0,await new Promise(r=>setTimeout(r,1e3)),m.success("信息保存成功"),t.currentPassword="",t.newPassword="",t.confirmPassword=""}catch{m.error("保存失败，请检查输入信息")}finally{k.value=!1}},Z=()=>{var r;(r=u.value)==null||r.resetFields(),V(),x.value=!1,v.value=!1};return K(()=>{V()}),(r,e)=>{const d=b("el-input"),i=b("el-form-item"),q=b("el-form"),D=b("el-upload"),A=b("el-button"),z=b("el-dialog");return g(),c("div",Q,[e[18]||(e[18]=a("div",{class:"mb-8"},[a("h2",{class:"text-2xl font-bold text-gray-900 dark:text-dark-text"},"基本信息"),a("p",{class:"mt-2 text-sm text-gray-600 dark:text-dark-text-secondary"},"管理您的账号基本信息")],-1)),a("div",S,[o(q,{ref_key:"formRef",ref:u,model:t,rules:E,"label-width":"100px",class:"space-y-6"},{default:l(()=>[a("div",W,[e[11]||(e[11]=a("h3",{class:"text-lg font-medium text-gray-900 dark:text-dark-text mb-4"},"头像设置",-1)),a("div",X,[a("div",Y,[a("img",{src:t.avatar||"/default-avatar.png",alt:"头像",class:"w-20 h-20 rounded-full object-cover border-4 border-white dark:border-dark-border shadow-lg"},null,8,ee),a("button",{onClick:C,class:"absolute -bottom-2 -right-2 w-8 h-8 bg-amber-500 hover:bg-amber-600 text-white rounded-full flex items-center justify-center shadow-lg transition-colors"},[o(M(I),{class:"w-4 h-4"})])]),a("div",null,[e[10]||(e[10]=a("p",{class:"text-sm text-gray-600 dark:text-dark-text-secondary mb-2"}," 支持 JPG、PNG 格式，文件大小不超过 2MB ",-1)),a("button",{onClick:C,class:"px-4 py-2 text-sm font-medium text-amber-600 dark:text-amber-400 hover:text-amber-700 dark:hover:text-amber-300 bg-amber-50 dark:bg-amber-900/20 hover:bg-amber-100 dark:hover:bg-amber-900/30 rounded-lg transition-all duration-200"}," 更换头像 ")])])]),a("div",ae,[e[12]||(e[12]=a("h3",{class:"text-lg font-medium text-gray-900 dark:text-dark-text mb-4"},"基本信息",-1)),a("div",te,[o(i,{label:"昵称",prop:"nickname"},{default:l(()=>[o(d,{modelValue:t.nickname,"onUpdate:modelValue":e[0]||(e[0]=s=>t.nickname=s),placeholder:"请输入昵称",class:"w-full"},null,8,["modelValue"])]),_:1}),o(i,{label:"手机号",prop:"phone"},{default:l(()=>[a("div",re,[o(d,{modelValue:t.phone,"onUpdate:modelValue":e[1]||(e[1]=s=>t.phone=s),placeholder:"请输入手机号",disabled:!x.value,class:"flex-1"},null,8,["modelValue","disabled"]),x.value?(g(),c("div",oe,[a("button",{onClick:N,class:"px-4 py-2 text-sm font-medium text-white bg-amber-500 hover:bg-amber-600 rounded-lg transition-colors"}," 保存 "),a("button",{onClick:R,class:"px-4 py-2 text-sm font-medium text-gray-600 dark:text-dark-text-secondary hover:text-gray-900 dark:hover:text-dark-text bg-gray-50 dark:bg-dark-border hover:bg-gray-100 dark:hover:bg-dark-card rounded-lg transition-all duration-200"}," 取消 ")])):(g(),c("button",{key:0,onClick:e[2]||(e[2]=s=>x.value=!0),class:"px-4 py-2 text-sm font-medium text-gray-600 dark:text-dark-text-secondary hover:text-gray-900 dark:hover:text-dark-text bg-gray-50 dark:bg-dark-border hover:bg-gray-100 dark:hover:bg-dark-card rounded-lg transition-all duration-200"}," 修改 "))])]),_:1}),o(i,{label:"邮箱",prop:"email"},{default:l(()=>[a("div",se,[o(d,{modelValue:t.email,"onUpdate:modelValue":e[3]||(e[3]=s=>t.email=s),placeholder:"请输入邮箱",disabled:!v.value,class:"flex-1"},null,8,["modelValue","disabled"]),v.value?(g(),c("div",le,[a("button",{onClick:T,class:"px-4 py-2 text-sm font-medium text-white bg-amber-500 hover:bg-amber-600 rounded-lg transition-colors"}," 保存 "),a("button",{onClick:$,class:"px-4 py-2 text-sm font-medium text-gray-600 dark:text-dark-text-secondary hover:text-gray-900 dark:hover:text-dark-text bg-gray-50 dark:bg-dark-border hover:bg-gray-100 dark:hover:bg-dark-card rounded-lg transition-all duration-200"}," 取消 ")])):(g(),c("button",{key:0,onClick:e[4]||(e[4]=s=>v.value=!0),class:"px-4 py-2 text-sm font-medium text-gray-600 dark:text-dark-text-secondary hover:text-gray-900 dark:hover:text-dark-text bg-gray-50 dark:bg-dark-border hover:bg-gray-100 dark:hover:bg-dark-card rounded-lg transition-all duration-200"}," 修改 "))])]),_:1})])]),a("div",de,[e[13]||(e[13]=a("h3",{class:"text-lg font-medium text-gray-900 dark:text-dark-text mb-4"},"密码修改",-1)),a("div",ne,[o(i,{label:"当前密码",prop:"currentPassword"},{default:l(()=>[o(d,{modelValue:t.currentPassword,"onUpdate:modelValue":e[5]||(e[5]=s=>t.currentPassword=s),type:"password",placeholder:"请输入当前密码","show-password":"",class:"w-full"},null,8,["modelValue"])]),_:1}),o(i,{label:"新密码",prop:"newPassword"},{default:l(()=>[o(d,{modelValue:t.newPassword,"onUpdate:modelValue":e[6]||(e[6]=s=>t.newPassword=s),type:"password",placeholder:"请输入新密码","show-password":"",class:"w-full"},null,8,["modelValue"])]),_:1}),o(i,{label:"确认密码",prop:"confirmPassword"},{default:l(()=>[o(d,{modelValue:t.confirmPassword,"onUpdate:modelValue":e[7]||(e[7]=s=>t.confirmPassword=s),type:"password",placeholder:"请再次输入新密码","show-password":"",class:"w-full"},null,8,["modelValue"])]),_:1})])]),a("div",ie,[a("button",{onClick:Z,class:"px-6 py-2 text-sm font-medium text-gray-600 dark:text-dark-text-secondary hover:text-gray-900 dark:hover:text-dark-text bg-gray-50 dark:bg-dark-border hover:bg-gray-100 dark:hover:bg-dark-card rounded-lg transition-all duration-200"}," 重置 "),a("button",{onClick:G,loading:k.value,class:"px-6 py-2 text-sm font-medium text-white bg-amber-500 hover:bg-amber-600 rounded-lg transition-colors"}," 保存修改 ",8,ue)])]),_:1},8,["model"])]),o(z,{modelValue:f.value,"onUpdate:modelValue":e[9]||(e[9]=s=>f.value=s),title:"更换头像",width:"500px","before-close":F},{footer:l(()=>[a("div",xe,[o(A,{onClick:e[8]||(e[8]=s=>f.value=!1)},{default:l(()=>e[16]||(e[16]=[j("取消")])),_:1,__:[16]}),o(A,{type:"primary",onClick:B,loading:w.value},{default:l(()=>e[17]||(e[17]=[j(" 确认上传 ")])),_:1,__:[17]},8,["loading"])])]),default:l(()=>[a("div",me,[o(D,{ref_key:"uploadRef",ref:P,"auto-upload":!1,"show-file-list":!1,"on-change":U,accept:"image/jpeg,image/png",drag:""},{default:l(()=>[a("div",ce,[o(M(O),{class:"w-12 h-12 text-gray-400 mx-auto mb-4"}),e[14]||(e[14]=a("p",{class:"text-gray-600 dark:text-dark-text-secondary"}," 点击或拖拽图片到此处上传 ",-1)),e[15]||(e[15]=a("p",{class:"text-sm text-gray-400 mt-2"}," 支持 JPG、PNG 格式，文件大小不超过 2MB ",-1))])]),_:1},512),p.value?(g(),c("div",ge,[a("img",{src:p.value,alt:"预览",class:"w-32 h-32 rounded-full object-cover mx-auto border-4 border-gray-200 dark:border-dark-border"},null,8,pe)])):L("",!0)])]),_:1},8,["modelValue"])])}}});export{be as default};
