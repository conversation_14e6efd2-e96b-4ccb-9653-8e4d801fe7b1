import{d as J,e as G,r as x,c as k,F as W,g as o,h as b,j as l,a as e,C as z,M as A,k as le,t as i,E as w,o as g,q,b as K,Q as X,n as Y,A as R,_ as Z,f as ne,p as de,G as ie}from"./index-84KJ6UvQ.js";import{_ as ue}from"./GallerySelectDialog.vue_vue_type_script_setup_true_lang-B2wBZjlb.js";import{r as Q}from"./ArrowDownTrayIcon-C6iGhkUM.js";const ce={class:"space-y-6"},pe={class:"flex justify-start space-x-3"},ge={class:"bg-gray-50 dark:bg-dark-card border-2 border-dashed border-gray-300 dark:border-dark-border rounded-lg min-h-[400px] p-6"},xe={key:0},ke={class:"grid grid-cols-6 gap-4"},me={class:"relative"},ve=["src","alt"],fe=["onClick"],be={class:"mt-1 text-xs text-gray-600 dark:text-dark-text-secondary truncate"},he={key:0,class:"flex justify-center mt-6"},we={key:1,class:"flex flex-col items-center justify-center h-full text-center"},ye={class:"flex justify-between items-center"},_e={class:"text-sm text-gray-500 dark:text-dark-text-secondary"},Ce={class:"flex space-x-3"},$e=J({__name:"CreateCutoutDialog",props:{modelValue:{type:Boolean}},emits:["update:modelValue","success"],setup(P,{emit:$}){const T=P,j=$,_=G({get:()=>T.modelValue,set:r=>j("update:modelValue",r)}),y=x(!1),c=x([]),p=x([]),h=x(1),C=x(18),B=G(()=>{const r=(h.value-1)*C.value,a=r+C.value;return p.value.slice(r,a)}),I=r=>{const a=new FileReader;a.onload=m=>{var V;const S=Date.now()+Math.random();p.value.push({id:S,name:r.name,url:(V=m.target)==null?void 0:V.result,file:r.raw})},a.readAsDataURL(r.raw)},M=r=>{const a=p.value.findIndex(m=>m.id===r.uid);a>-1&&p.value.splice(a,1)},D=r=>{const a=r.map(m=>({...m,id:m.id||Date.now()+Math.random()}));p.value.push(...a),y.value=!1},O=r=>{const a=(h.value-1)*C.value+r;p.value.splice(a,1),B.value.length===0&&h.value>1&&h.value--},L=r=>{h.value=r},U=()=>{if(p.value.length===0){w.warning("请先选择图片");return}w.success(`正在创建抠图任务，共 ${p.value.length} 张图片`),u(),j("success"),j("update:modelValue",!1)},u=()=>{p.value=[],c.value=[],h.value=1};return(r,a)=>{const m=b("el-button"),S=b("el-upload"),V=b("el-pagination"),H=b("el-dialog");return g(),k(W,null,[o(H,{modelValue:_.value,"onUpdate:modelValue":a[3]||(a[3]=v=>_.value=v),title:"新建抠图任务",width:"900px","align-center":"",onClose:u},{footer:l(()=>[e("div",ye,[e("div",_e,i(p.value.length>0?`将处理 ${p.value.length} 张图片`:"请先选择图片"),1),e("div",Ce,[o(m,{onClick:a[2]||(a[2]=v=>_.value=!1)},{default:l(()=>a[9]||(a[9]=[z("取消")])),_:1,__:[9]}),o(m,{type:"primary",onClick:U,disabled:p.value.length===0},{default:l(()=>a[10]||(a[10]=[z(" 提交任务 ")])),_:1,__:[10]},8,["disabled"])])])]),default:l(()=>[e("div",ce,[e("div",pe,[o(S,{ref:"uploadRef","file-list":c.value,"on-change":I,"on-remove":M,"auto-upload":!1,multiple:"",accept:"image/*","show-file-list":!1},{default:l(()=>[o(m,{type:"primary",size:"large"},{default:l(()=>a[5]||(a[5]=[e("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"})],-1),z(" 上传图片 ")])),_:1,__:[5]})]),_:1},8,["file-list"]),o(m,{onClick:a[0]||(a[0]=v=>y.value=!0),size:"large"},{default:l(()=>a[6]||(a[6]=[e("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"})],-1),z(" 图库选择 ")])),_:1,__:[6]})]),e("div",ge,[p.value.length>0?(g(),k("div",xe,[e("div",ke,[(g(!0),k(W,null,le(B.value,(v,s)=>(g(),k("div",{key:v.id||s,class:"relative group"},[e("div",me,[e("img",{src:v.url,alt:v.name,class:"w-full h-20 object-cover rounded-lg border border-gray-200 dark:border-dark-border"},null,8,ve),e("button",{onClick:F=>O(s),class:"absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200"},a[7]||(a[7]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]),8,fe)]),e("div",be,i(v.name),1)]))),128))]),p.value.length>C.value?(g(),k("div",he,[o(V,{"current-page":h.value,"onUpdate:currentPage":a[1]||(a[1]=v=>h.value=v),"page-size":C.value,total:p.value.length,layout:"prev, pager, next",onCurrentChange:L},null,8,["current-page","page-size","total"])])):A("",!0)])):(g(),k("div",we,a[8]||(a[8]=[e("svg",{class:"w-16 h-16 text-gray-400 mb-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1),e("p",{class:"text-lg text-gray-500 dark:text-dark-text-secondary mb-2"},"暂无图片",-1),e("p",{class:"text-sm text-gray-400 dark:text-dark-text-secondary"},"请点击上方按钮选择图片",-1)])))])])]),_:1},8,["modelValue"]),o(ue,{modelValue:y.value,"onUpdate:modelValue":a[4]||(a[4]=v=>y.value=v),"theme-color":"pink",onSelect:D},null,8,["modelValue"])],64)}}}),je={class:"flex items-center justify-between p-6 border-b border-gray-100 dark:border-dark-border"},ze={class:"flex items-center space-x-3"},Ve={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},Te={key:0,class:"p-6 grid grid-cols-2 md:grid-cols-4 gap-4"},Me={class:"bg-gradient-to-br from-pink-50 to-pink-100 dark:from-pink-900/20 dark:to-pink-800/20 p-4 rounded-xl border border-pink-200 dark:border-pink-800"},Se={class:"flex items-center space-x-2"},Be={class:"text-sm font-bold text-pink-900 dark:text-pink-100"},Ie={class:"bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 p-4 rounded-xl border border-blue-200 dark:border-blue-800"},De={class:"flex items-center space-x-2"},Oe={class:"text-sm font-bold text-blue-900 dark:text-blue-100"},Le={class:"bg-gradient-to-br from-emerald-50 to-emerald-100 dark:from-emerald-900/20 dark:to-emerald-800/20 p-4 rounded-xl border border-emerald-200 dark:border-emerald-800"},Ue={class:"flex items-center space-x-2"},He={class:"text-sm font-bold text-emerald-900 dark:text-emerald-100"},Ne={class:"bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 p-4 rounded-xl border border-purple-200 dark:border-purple-800"},Pe={class:"flex items-center space-x-2"},Re={class:"text-sm font-bold text-purple-900 dark:text-purple-100"},Ge={class:"px-6 pb-6"},Ae={class:"bg-white dark:bg-dark-surface rounded-xl border border-gray-200 dark:border-dark-border overflow-hidden"},Fe={key:0,class:"flex justify-center"},Ee={key:1,class:"text-gray-400 text-xs"},We={key:0,class:"flex justify-center"},qe={class:"relative w-16 h-16 rounded-lg overflow-hidden border border-gray-200 dark:border-dark-border"},Qe={key:1,class:"text-gray-400 text-xs"},Je={key:0,class:"text-sm font-medium text-gray-900 dark:text-dark-text"},Ke={key:1,class:"text-gray-400 text-xs"},Xe={key:0,class:"text-sm text-gray-600 dark:text-dark-text-secondary"},Ye={key:1,class:"text-gray-400 text-xs"},Ze=["onClick"],et={class:"flex justify-center p-4 border-t border-gray-200 dark:border-dark-border"},tt={class:"flex items-center justify-between p-6 border-t border-gray-100 dark:border-dark-border bg-gray-50 dark:bg-dark-card/50"},rt={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},at={class:"flex items-center space-x-3"},ot=J({__name:"ViewDetailsDialog",props:{modelValue:{type:Boolean},task:{}},emits:["update:modelValue"],setup(P,{emit:$}){const T=P,j=$,_=G({get:()=>T.modelValue,set:u=>j("update:modelValue",u)}),y=x(!1),c=x(1),p=x(10),h=G(()=>C.value.length),C=x([{fileName:"portrait1.png",originalSize:"1920x1080",status:"success",originalImage:"https://via.placeholder.com/100x100/6b7280/ffffff?text=ORIG1",thumbnail:"https://via.placeholder.com/100x100/ec4899/ffffff?text=CUT1",downloadUrl:"#"},{fileName:"object1.png",originalSize:"1600x900",status:"success",originalImage:"https://via.placeholder.com/100x100/6b7280/ffffff?text=ORIG2",thumbnail:"https://via.placeholder.com/100x100/f97316/ffffff?text=CUT2",downloadUrl:"#"},{fileName:"product1.png",originalSize:"1280x720",status:"failed",originalImage:"https://via.placeholder.com/100x100/6b7280/ffffff?text=ORIG3",thumbnail:"https://via.placeholder.com/100x100/ef4444/ffffff?text=FAIL",errorMessage:"图片背景过于复杂"},{fileName:"portrait2.png",originalSize:"2048x1536",status:"success",originalImage:"https://via.placeholder.com/100x100/6b7280/ffffff?text=ORIG4",thumbnail:"https://via.placeholder.com/100x100/8b5cf6/ffffff?text=CUT3",downloadUrl:"#"},{fileName:"object2.png",originalSize:"1440x900",status:"success",originalImage:"https://via.placeholder.com/100x100/6b7280/ffffff?text=ORIG5",thumbnail:"https://via.placeholder.com/100x100/f59e0b/ffffff?text=CUT4",downloadUrl:"#"}]),B=u=>({completed:"已完成",processing:"处理中",failed:"失败",pending:"等待中"})[u]||"未知",I=u=>({success:"成功",failed:"失败"})[u]||"未知",M=()=>{_.value=!1},D=u=>{w.success(`正在下载 ${u.fileName}`)},O=()=>{w.success("正在导出详情...")},L=u=>{p.value=u,c.value=1},U=u=>{c.value=u};return(u,r)=>{const a=b("el-table-column"),m=b("el-image"),S=b("el-table"),V=b("el-pagination"),H=b("el-dialog"),v=X("loading");return g(),q(H,{modelValue:_.value,"onUpdate:modelValue":r[2]||(r[2]=s=>_.value=s),width:"1200px","before-close":M,"show-close":!1,class:"modern-dialog"},{header:l(()=>{var s;return[e("div",je,[e("div",ze,[r[4]||(r[4]=e("div",{class:"w-10 h-10 bg-gradient-to-br from-pink-500 to-pink-600 rounded-xl flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})])],-1)),e("div",null,[r[3]||(r[3]=e("h3",{class:"text-xl font-bold text-gray-900 dark:text-dark-text"},"抠图详情",-1)),e("p",Ve,"任务ID: "+i(((s=u.task)==null?void 0:s.id)||""),1)])]),e("button",{onClick:M,class:"p-2 text-gray-400 hover:text-gray-600 dark:text-dark-text-secondary dark:hover:text-dark-text rounded-lg hover:bg-gray-100 dark:hover:bg-dark-card transition-all duration-200"},r[5]||(r[5]=[e("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))])]}),footer:l(()=>[e("div",tt,[e("div",rt," 共 "+i(h.value)+" 条抠图结果 ",1),e("div",at,[e("button",{onClick:M,class:"px-6 py-2.5 text-gray-700 dark:text-dark-text font-medium rounded-lg border border-gray-300 dark:border-dark-border hover:bg-gray-50 dark:hover:bg-dark-border transition-all duration-200"}," 关闭 "),e("button",{onClick:O,class:"inline-flex items-center px-6 py-2.5 bg-gradient-to-r from-pink-500 to-pink-600 hover:from-pink-600 hover:to-pink-700 text-white font-medium rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"},[o(R(Q),{class:"w-5 h-5 mr-2"}),r[16]||(r[16]=z(" 导出详情 "))])])])]),default:l(()=>[u.task?(g(),k("div",Te,[e("div",Me,[e("div",Se,[r[7]||(r[7]=e("div",{class:"w-8 h-8 bg-pink-500 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1)),e("div",null,[r[6]||(r[6]=e("p",{class:"text-xs text-pink-600 dark:text-pink-400 font-medium"},"任务状态",-1)),e("p",Be,i(B(u.task.status)),1)])])]),e("div",Ie,[e("div",De,[r[9]||(r[9]=e("div",{class:"w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})])],-1)),e("div",null,[r[8]||(r[8]=e("p",{class:"text-xs text-blue-600 dark:text-blue-400 font-medium"},"目标数量",-1)),e("p",Oe,i(u.task.targetCount),1)])])]),e("div",Le,[e("div",Ue,[r[11]||(r[11]=e("div",{class:"w-8 h-8 bg-emerald-500 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1)),e("div",null,[r[10]||(r[10]=e("p",{class:"text-xs text-emerald-600 dark:text-emerald-400 font-medium"},"成功数量",-1)),e("p",He,i(u.task.successCount),1)])])]),e("div",Ne,[e("div",Pe,[r[13]||(r[13]=e("div",{class:"w-8 h-8 bg-purple-500 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})])],-1)),e("div",null,[r[12]||(r[12]=e("p",{class:"text-xs text-purple-600 dark:text-purple-400 font-medium"},"操作人",-1)),e("p",Re,i(u.task.operator),1)])])])])):A("",!0),e("div",Ge,[r[15]||(r[15]=e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-dark-text mb-4"},"抠图结果",-1)),e("div",Ae,[K((g(),q(S,{data:C.value,style:{width:"100%"},"max-height":"400",class:"modern-table"},{default:l(()=>[o(a,{prop:"index",label:"序号",width:"80",align:"center"}),o(a,{label:"原图",width:"100",align:"center"},{default:l(s=>[s.row.status==="success"?(g(),k("div",Fe,[o(m,{src:s.row.originalImage,"preview-src-list":[s.row.originalImage],fit:"cover",class:"w-16 h-16 rounded-lg border border-gray-200 dark:border-dark-border","preview-teleported":!0},null,8,["src","preview-src-list"])])):(g(),k("div",Ee,"无图片"))]),_:1}),o(a,{label:"结果图",width:"100",align:"center"},{default:l(s=>[s.row.status==="success"?(g(),k("div",We,[e("div",qe,[r[14]||(r[14]=e("div",{class:"absolute inset-0 checkerboard-bg"},null,-1)),o(m,{src:s.row.thumbnail,"preview-src-list":[s.row.thumbnail],fit:"cover",class:"relative z-10 w-full h-full","preview-teleported":!0},null,8,["src","preview-src-list"])])])):(g(),k("div",Qe,"无图片"))]),_:1}),o(a,{label:"文件名","min-width":"200"},{default:l(s=>[s.row.fileName?(g(),k("div",Je,i(s.row.fileName),1)):(g(),k("div",Ke,"无文件名"))]),_:1}),o(a,{prop:"originalSize",label:"原始尺寸",width:"120",align:"center"},{default:l(s=>[s.row.originalSize?(g(),k("span",Xe,i(s.row.originalSize),1)):(g(),k("span",Ye,"-"))]),_:1}),o(a,{prop:"status",label:"状态",width:"100",align:"center"},{default:l(s=>[e("span",{class:Y(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",s.row.status==="success"?"bg-pink-100 dark:bg-pink-900/30 text-pink-800 dark:text-pink-300":"bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300"])},i(I(s.row.status)),3)]),_:1}),o(a,{label:"操作",width:"120",align:"center"},{default:l(s=>[s.row.status==="success"?(g(),k("button",{key:0,onClick:F=>D(s.row),class:"text-pink-600 dark:text-pink-400 hover:text-pink-700 dark:hover:text-pink-300 text-sm font-medium"}," 下载结果 ",8,Ze)):A("",!0)]),_:1})]),_:1},8,["data"])),[[v,y.value]]),e("div",et,[o(V,{"current-page":c.value,"onUpdate:currentPage":r[0]||(r[0]=s=>c.value=s),"page-size":p.value,"onUpdate:pageSize":r[1]||(r[1]=s=>p.value=s),"page-sizes":[10,20,50],total:h.value,layout:"total, sizes, prev, pager, next",onSizeChange:L,onCurrentChange:U},null,8,["current-page","page-size","total"])])])])]),_:1},8,["modelValue"])}}}),st=Z(ot,[["__scopeId","data-v-0bb5a421"]]),lt={class:"space-y-6"},nt={class:"grid grid-cols-1 md:grid-cols-4 gap-6"},dt={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},it={class:"flex items-center justify-between"},ut={class:"text-2xl font-bold text-gray-900 dark:text-dark-text"},ct={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},pt={class:"flex items-center justify-between"},gt={class:"text-2xl font-bold text-green-600 dark:text-green-400"},xt={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},kt={class:"flex items-center justify-between"},mt={class:"text-2xl font-bold text-blue-600 dark:text-blue-400"},vt={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},ft={class:"flex items-center justify-between"},bt={class:"text-2xl font-bold text-orange-600 dark:text-orange-400"},ht={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border"},wt={class:"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0"},yt={class:"flex items-center space-x-3"},_t={key:0,class:"flex items-center space-x-2 ml-4 pl-4 border-l border-gray-200 dark:border-dark-border"},Ct={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},$t={class:"bg-white dark:bg-dark-surface rounded-xl shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border overflow-hidden"},jt={class:"overflow-x-auto"},zt={class:"font-mono text-sm text-gray-600 dark:text-dark-text-secondary"},Vt={class:"flex items-center space-x-2"},Tt={class:"font-medium text-gray-900 dark:text-dark-text"},Mt={class:"font-medium text-pink-600 dark:text-pink-400"},St={class:"flex items-center space-x-2"},Bt={class:"w-6 h-6 bg-gradient-to-br from-pink-400 to-pink-500 rounded-full flex items-center justify-center"},It={class:"text-white text-xs font-medium"},Dt={class:"text-sm text-gray-900 dark:text-dark-text"},Ot={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},Lt={class:"flex items-center space-x-2"},Ut=["onClick"],Ht={class:"flex justify-between items-center px-6 py-4 border-t border-gray-100 dark:border-dark-border bg-gray-50 dark:bg-dark-card/50"},Nt={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},Pt=J({__name:"index",setup(P){const $=x(!1),T=x(!1),j=x(!1),_=x(null),y=x([]),c=x({currentPage:1,pageSize:20,total:0}),p=x(1248),h=x(94.2),C=x(28),B=x(5),I=x([{id:"CO001",targetCount:25,status:"completed",successCount:24,operator:"张三",createTime:"2024-01-15 14:30:25"},{id:"CO002",targetCount:18,status:"processing",successCount:12,operator:"李四",createTime:"2024-01-15 13:45:12"},{id:"CO003",targetCount:32,status:"completed",successCount:30,operator:"王五",createTime:"2024-01-15 12:20:08"},{id:"CO004",targetCount:15,status:"failed",successCount:8,operator:"赵六",createTime:"2024-01-15 11:15:33"},{id:"CO005",targetCount:42,status:"completed",successCount:41,operator:"钱七",createTime:"2024-01-15 10:30:45"},{id:"CO006",targetCount:28,status:"processing",successCount:15,operator:"孙八",createTime:"2024-01-15 09:45:22"},{id:"CO007",targetCount:36,status:"completed",successCount:35,operator:"周九",createTime:"2024-01-15 08:20:15"},{id:"CO008",targetCount:22,status:"completed",successCount:21,operator:"吴十",createTime:"2024-01-14 16:30:40"}]),M=d=>{const t={completed:"bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300",processing:"bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300",failed:"bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300",pending:"bg-gray-100 dark:bg-gray-900/30 text-gray-800 dark:text-gray-300"};return t[d]||t.pending},D=d=>({completed:"已完成",processing:"处理中",failed:"失败",pending:"等待中"})[d]||"未知",O=d=>{y.value=d},L=d=>{_.value=d,j.value=!0},U=d=>{const{action:t,row:f}=d;switch(t){case"titleGenerate":S(f);break;case"batchListing":V(f);break;case"smartCrop":H(f);break;case"superSplit":v(f);break;case"copyrightDetection":s(f);break}},u=()=>{w.success("导出表格功能开发中...")},r=()=>{w.success(`正在批量导出 ${y.value.length} 个任务...`)},a=()=>{w.success("抠图任务创建成功！"),m()},m=()=>{$.value=!0,setTimeout(()=>{$.value=!1},500)},S=d=>{w.success(`正在为抠图任务 ${d.id} 创建标题生成任务...`)},V=d=>{w.success(`正在为抠图任务 ${d.id} 创建批量刊登任务...`)},H=d=>{w.success(`正在为抠图任务 ${d.id} 创建智能裁图任务...`)},v=d=>{w.success(`正在为抠图任务 ${d.id} 创建超级裂变任务...`)},s=d=>{w.success(`正在为抠图任务 ${d.id} 创建侵权检测任务...`)},F=d=>{c.value.pageSize=d,c.value.currentPage=1,E()},ee=d=>{c.value.currentPage=d,E()},E=()=>{$.value=!0,setTimeout(()=>{const d=[{id:"CO001",targetCount:25,status:"completed",successCount:24,operator:"张三",createTime:"2024-01-15 14:30:25"},{id:"CO002",targetCount:18,status:"processing",successCount:12,operator:"李四",createTime:"2024-01-15 13:45:12"},{id:"CO003",targetCount:32,status:"completed",successCount:30,operator:"王五",createTime:"2024-01-15 12:20:08"},{id:"CO004",targetCount:15,status:"failed",successCount:8,operator:"赵六",createTime:"2024-01-15 11:15:33"},{id:"CO005",targetCount:42,status:"completed",successCount:41,operator:"钱七",createTime:"2024-01-15 10:30:45"},{id:"CO006",targetCount:28,status:"processing",successCount:15,operator:"孙八",createTime:"2024-01-15 09:45:22"},{id:"CO007",targetCount:36,status:"completed",successCount:35,operator:"周九",createTime:"2024-01-15 08:20:15"},{id:"CO008",targetCount:22,status:"completed",successCount:21,operator:"吴十",createTime:"2024-01-14 16:30:40"}],t=(c.value.currentPage-1)*c.value.pageSize,f=t+c.value.pageSize;I.value=d.slice(t,f),c.value.total=d.length,$.value=!1},500)};return ne(()=>{E()}),(d,t)=>{const f=b("el-table-column"),N=b("el-dropdown-item"),te=b("el-dropdown-menu"),re=b("el-dropdown"),ae=b("el-table"),oe=b("el-pagination"),se=X("loading");return g(),k(W,null,[e("div",lt,[t[26]||(t[26]=de('<div class="bg-gradient-to-r from-pink-50 to-rose-50 dark:from-pink-900/20 dark:to-rose-900/20 rounded-2xl p-6 border border-pink-100 dark:border-pink-800" data-v-50dcc0ff><div class="flex items-center space-x-3" data-v-50dcc0ff><div class="w-10 h-10 bg-gradient-to-br from-pink-500 to-pink-600 rounded-xl flex items-center justify-center" data-v-50dcc0ff><svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-50dcc0ff><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" data-v-50dcc0ff></path></svg></div><div data-v-50dcc0ff><h1 class="text-2xl font-bold text-gray-900 dark:text-dark-text" data-v-50dcc0ff>一键抠图</h1><p class="mt-1 text-sm text-gray-600 dark:text-dark-text-secondary" data-v-50dcc0ff>AI智能图片抠图和背景移除工具</p></div></div></div>',1)),e("div",nt,[e("div",dt,[e("div",it,[e("div",null,[t[5]||(t[5]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"总抠图数",-1)),e("p",ut,i(p.value),1)]),t[6]||(t[6]=e("div",{class:"w-12 h-12 bg-pink-100 dark:bg-pink-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-pink-600 dark:text-pink-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})])],-1))])]),e("div",ct,[e("div",pt,[e("div",null,[t[7]||(t[7]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"成功率",-1)),e("p",gt,i(h.value)+"%",1)]),t[8]||(t[8]=e("div",{class:"w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-green-600 dark:text-green-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1))])]),e("div",xt,[e("div",kt,[e("div",null,[t[9]||(t[9]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"今日抠图",-1)),e("p",mt,i(C.value),1)]),t[10]||(t[10]=e("div",{class:"w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-blue-600 dark:text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1))])]),e("div",vt,[e("div",ft,[e("div",null,[t[11]||(t[11]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"处理中",-1)),e("p",bt,i(B.value),1)]),t[12]||(t[12]=e("div",{class:"w-12 h-12 bg-orange-100 dark:bg-orange-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-orange-600 dark:text-orange-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})])],-1))])])]),e("div",ht,[e("div",wt,[e("div",yt,[e("button",{onClick:t[0]||(t[0]=n=>T.value=!0),class:"inline-flex items-center px-4 py-2.5 bg-gradient-to-r from-pink-500 to-pink-600 hover:from-pink-600 hover:to-pink-700 text-white font-medium rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"},[o(R(ie),{class:"w-5 h-5 mr-2"}),t[13]||(t[13]=z(" 新建抠图 "))]),e("button",{onClick:u,class:"inline-flex items-center px-4 py-2.5 bg-white dark:bg-dark-card text-gray-700 dark:text-dark-text font-medium rounded-lg border border-gray-300 dark:border-dark-border hover:bg-gray-50 dark:hover:bg-dark-border shadow-sm hover:shadow-md transition-all duration-200"},[o(R(Q),{class:"w-5 h-5 mr-2"}),t[14]||(t[14]=z(" 导出表格 "))]),y.value.length>0?(g(),k("div",_t,[e("span",Ct," 已选择 "+i(y.value.length)+" 项 ",1),e("button",{onClick:r,class:"inline-flex items-center px-3 py-1.5 bg-pink-500 hover:bg-pink-600 text-white text-sm font-medium rounded-lg transition-all duration-200"},[o(R(Q),{class:"w-4 h-4 mr-1"}),t[15]||(t[15]=z(" 批量导出 "))])])):A("",!0)])])]),e("div",$t,[t[25]||(t[25]=e("div",{class:"px-6 py-4 border-b border-gray-100 dark:border-dark-border"},[e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-dark-text"},"抠图任务列表"),e("p",{class:"text-sm text-gray-600 dark:text-dark-text-secondary mt-1"},"管理和监控您的所有抠图任务")],-1)),e("div",jt,[K((g(),q(ae,{data:I.value,style:{width:"100%"},onSelectionChange:O,class:"modern-table","header-cell-style":{backgroundColor:"var(--el-bg-color-page)",color:"var(--el-text-color-primary)",fontWeight:"600",borderBottom:"1px solid var(--el-border-color-light)"},"row-style":{backgroundColor:"transparent"}},{default:l(()=>[o(f,{type:"selection",width:"55"}),o(f,{prop:"id",label:"抠图ID",width:"120"},{default:l(n=>[e("span",zt,i(n.row.id),1)]),_:1}),o(f,{label:"抠图数量",width:"150"},{default:l(n=>[e("div",Vt,[t[16]||(t[16]=e("span",{class:"text-sm text-gray-600 dark:text-dark-text-secondary"},"目标:",-1)),e("span",Tt,i(n.row.targetCount),1),t[17]||(t[17]=e("span",{class:"text-gray-400"},"|",-1)),t[18]||(t[18]=e("span",{class:"text-sm text-gray-600 dark:text-dark-text-secondary"},"成功:",-1)),e("span",Mt,i(n.row.successCount),1)])]),_:1}),o(f,{prop:"status",label:"抠图状态",width:"120"},{default:l(n=>[e("span",{class:Y([M(n.row.status),"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"])},i(D(n.row.status)),3)]),_:1}),o(f,{prop:"operator",label:"操作人",width:"100"},{default:l(n=>[e("div",St,[e("div",Bt,[e("span",It,i(n.row.operator.charAt(0)),1)]),e("span",Dt,i(n.row.operator),1)])]),_:1}),o(f,{prop:"createTime",label:"创建时间",width:"180"},{default:l(n=>[e("div",Ot,i(n.row.createTime),1)]),_:1}),o(f,{label:"操作",width:"180"},{default:l(n=>[e("div",Lt,[e("button",{onClick:Rt=>L(n.row),class:"inline-flex items-center px-3 py-1.5 text-sm font-medium text-pink-600 dark:text-pink-400 hover:text-pink-700 dark:hover:text-pink-300 bg-pink-50 dark:bg-pink-900/20 hover:bg-pink-100 dark:hover:bg-pink-900/30 rounded-lg transition-all duration-200"}," 查看详情 ",8,Ut),o(re,{onCommand:U,trigger:"click"},{dropdown:l(()=>[o(te,null,{default:l(()=>[o(N,{command:{action:"titleGenerate",row:n.row}},{default:l(()=>t[19]||(t[19]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-blue-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})]),e("span",null,"标题生成")],-1)])),_:2,__:[19]},1032,["command"]),o(N,{command:{action:"batchListing",row:n.row}},{default:l(()=>t[20]||(t[20]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-purple-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"})]),e("span",null,"批量刊登")],-1)])),_:2,__:[20]},1032,["command"]),o(N,{command:{action:"smartCrop",row:n.row}},{default:l(()=>t[21]||(t[21]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-green-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m0 0V1a1 1 0 011-1h2a1 1 0 011 1v18a1 1 0 01-1 1H4a1 1 0 01-1-1V1a1 1 0 011-1h2a1 1 0 011 1v3m0 0h8m-8 0H4a1 1 0 00-1 1v3a1 1 0 001 1h3m0 0h10m0 0h3a1 1 0 001-1V5a1 1 0 00-1-1h-3m-3 0v8a1 1 0 01-1 1H8a1 1 0 01-1-1V4"})]),e("span",null,"智能裁图")],-1)])),_:2,__:[21]},1032,["command"]),o(N,{command:{action:"superSplit",row:n.row}},{default:l(()=>t[22]||(t[22]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-orange-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 10V3L4 14h7v7l9-11h-7z"})]),e("span",null,"超级裂变")],-1)])),_:2,__:[22]},1032,["command"]),o(N,{command:{action:"copyrightDetection",row:n.row}},{default:l(()=>t[23]||(t[23]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-blue-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"})]),e("span",null,"侵权检测")],-1)])),_:2,__:[23]},1032,["command"])]),_:2},1024)]),default:l(()=>[t[24]||(t[24]=e("button",{class:"inline-flex items-center px-2 py-1.5 text-sm font-medium text-gray-600 dark:text-dark-text-secondary hover:text-gray-700 dark:hover:text-dark-text bg-gray-50 dark:bg-dark-card hover:bg-gray-100 dark:hover:bg-dark-border rounded-lg transition-all duration-200"},[z(" 更多 "),e("svg",{class:"w-4 h-4 ml-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 9l-7 7-7-7"})])],-1))]),_:2,__:[24]},1024)])]),_:1})]),_:1},8,["data"])),[[se,$.value]])]),e("div",Ht,[e("div",Nt," 共 "+i(c.value.total)+" 条记录 ",1),o(oe,{"current-page":c.value.currentPage,"onUpdate:currentPage":t[1]||(t[1]=n=>c.value.currentPage=n),"page-size":c.value.pageSize,"onUpdate:pageSize":t[2]||(t[2]=n=>c.value.pageSize=n),"page-sizes":[10,20,50,100],total:c.value.total,layout:"sizes, prev, pager, next, jumper",onSizeChange:F,onCurrentChange:ee,class:"modern-pagination"},null,8,["current-page","page-size","total"])])])]),o($e,{modelValue:T.value,"onUpdate:modelValue":t[3]||(t[3]=n=>T.value=n),onSuccess:a},null,8,["modelValue"]),o(st,{modelValue:j.value,"onUpdate:modelValue":t[4]||(t[4]=n=>j.value=n),task:_.value},null,8,["modelValue","task"])],64)}}}),Et=Z(Pt,[["__scopeId","data-v-50dcc0ff"]]);export{Et as default};
