import{d as X,e as I,r as k,c as x,F as q,g as a,h as b,j as l,a as e,C as T,M as P,k as ne,t as i,A as N,E as _,o as c,q as J,b as Y,Q as Z,n as ee,_ as te,f as de,p as ie,G as ue}from"./index-BTucjZQh.js";import{_ as ce}from"./GallerySelectDialog.vue_vue_type_script_setup_true_lang-CrzPKmtJ.js";import{g as pe,c as ge,a as xe}from"./billing-DsHNwwW1.js";import{r as K}from"./ArrowDownTrayIcon-B03b1yfF.js";import"./billing-Ciq-WFQK.js";const me={class:"space-y-6"},ke={class:"flex justify-start space-x-3"},ve={class:"bg-gray-50 dark:bg-dark-card border-2 border-dashed border-gray-300 dark:border-dark-border rounded-lg min-h-[400px] p-6"},fe={key:0},he={class:"grid grid-cols-6 gap-4"},be={class:"relative"},we=["src","alt"],_e=["onClick"],ye={class:"mt-1 text-xs text-gray-600 dark:text-dark-text-secondary truncate"},Ce={key:0,class:"flex justify-center mt-6"},$e={key:1,class:"flex flex-col items-center justify-center h-full text-center"},je={class:"flex justify-between items-center"},ze={class:"text-sm text-gray-500 dark:text-dark-text-secondary"},Ve={key:0},Te={key:0,class:"text-green-600 dark:text-green-400"},Me={key:1,class:"text-blue-600 dark:text-blue-400"},Se={key:1},Be={class:"flex space-x-3"},W="one-click-cutout",Ie=X({__name:"CreateCutoutDialog",props:{modelValue:{type:Boolean}},emits:["update:modelValue","success"],setup(E,{emit:z}){const M=E,V=z,$=I({get:()=>M.modelValue,set:p=>V("update:modelValue",p)}),y=k(!1),m=k([]),u=k([]),w=k(1),j=k(18),D=I(()=>{const p=(w.value-1)*j.value,o=p+j.value;return u.value.slice(p,o)}),O=p=>{const o=new FileReader;o.onload=f=>{var B;const s=Date.now()+Math.random();u.value.push({id:s,name:p.name,url:(B=f.target)==null?void 0:B.result,file:p.raw})},o.readAsDataURL(p.raw)},S=p=>{const o=u.value.findIndex(f=>f.id===p.uid);o>-1&&u.value.splice(o,1)},R=p=>{const o=p.map(f=>({...f,id:f.id||Date.now()+Math.random()}));u.value.push(...o),y.value=!1},A=p=>{const o=(w.value-1)*j.value+p;u.value.splice(o,1),D.value.length===0&&w.value>1&&w.value--},G=p=>{w.value=p},L=pe(W),g=I(()=>u.value.length===0?0:ge(W,u.value.length)),r=I(()=>xe(W,u.value.length)),C=I(()=>{if(u.value.length===0)return"提交任务";const p=g.value;return p===0?"提交任务（免费）":`提交任务（¥${p.toFixed(2)}）`}),U=()=>{if(u.value.length===0){_.warning("请先选择图片");return}_.success(`正在创建抠图任务，共 ${u.value.length} 张图片`),H(),V("success"),V("update:modelValue",!1)},H=()=>{u.value=[],m.value=[],w.value=1};return(p,o)=>{const f=b("el-button"),s=b("el-upload"),B=b("el-pagination"),Q=b("el-dialog");return c(),x(q,null,[a(Q,{modelValue:$.value,"onUpdate:modelValue":o[3]||(o[3]=v=>$.value=v),title:"新建抠图任务",width:"900px","align-center":"",onClose:H},{footer:l(()=>[e("div",je,[e("div",ze,[u.value.length>0?(c(),x("div",Ve,[e("div",null,"将处理 "+i(u.value.length)+" 张图片",1),r.value.hasFreeQuota&&r.value.freeItems>0?(c(),x("div",Te," 免费额度："+i(r.value.freeItems)+" 张，付费："+i(r.value.chargeableItems)+" 张 ",1)):P("",!0),N(L)?(c(),x("div",Me," 单价：¥"+i(N(L).unitPrice.toFixed(2))+"/张 ",1)):P("",!0)])):(c(),x("div",Se,"请先选择图片"))]),e("div",Be,[a(f,{onClick:o[2]||(o[2]=v=>$.value=!1)},{default:l(()=>o[9]||(o[9]=[T("取消")])),_:1,__:[9]}),a(f,{type:"primary",onClick:U,disabled:u.value.length===0},{default:l(()=>[T(i(C.value),1)]),_:1},8,["disabled"])])])]),default:l(()=>[e("div",me,[e("div",ke,[a(s,{ref:"uploadRef","file-list":m.value,"on-change":O,"on-remove":S,"auto-upload":!1,multiple:"",accept:"image/*","show-file-list":!1},{default:l(()=>[a(f,{type:"primary",size:"large"},{default:l(()=>o[5]||(o[5]=[e("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"})],-1),T(" 上传图片 ")])),_:1,__:[5]})]),_:1},8,["file-list"]),a(f,{onClick:o[0]||(o[0]=v=>y.value=!0),size:"large"},{default:l(()=>o[6]||(o[6]=[e("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"})],-1),T(" 图库选择 ")])),_:1,__:[6]})]),e("div",ve,[u.value.length>0?(c(),x("div",fe,[e("div",he,[(c(!0),x(q,null,ne(D.value,(v,n)=>(c(),x("div",{key:v.id||n,class:"relative group"},[e("div",be,[e("img",{src:v.url,alt:v.name,class:"w-full h-20 object-cover rounded-lg border border-gray-200 dark:border-dark-border"},null,8,we),e("button",{onClick:t=>A(n),class:"absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200"},o[7]||(o[7]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]),8,_e)]),e("div",ye,i(v.name),1)]))),128))]),u.value.length>j.value?(c(),x("div",Ce,[a(B,{"current-page":w.value,"onUpdate:currentPage":o[1]||(o[1]=v=>w.value=v),"page-size":j.value,total:u.value.length,layout:"prev, pager, next",onCurrentChange:G},null,8,["current-page","page-size","total"])])):P("",!0)])):(c(),x("div",$e,o[8]||(o[8]=[e("svg",{class:"w-16 h-16 text-gray-400 mb-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1),e("p",{class:"text-lg text-gray-500 dark:text-dark-text-secondary mb-2"},"暂无图片",-1),e("p",{class:"text-sm text-gray-400 dark:text-dark-text-secondary"},"请点击上方按钮选择图片",-1)])))])])]),_:1},8,["modelValue"]),a(ce,{modelValue:y.value,"onUpdate:modelValue":o[4]||(o[4]=v=>y.value=v),"theme-color":"pink",onSelect:R},null,8,["modelValue"])],64)}}}),De={class:"flex items-center justify-between p-6 border-b border-gray-100 dark:border-dark-border"},Oe={class:"flex items-center space-x-3"},Le={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},Ue={key:0,class:"p-6 grid grid-cols-2 md:grid-cols-4 gap-4"},He={class:"bg-gradient-to-br from-pink-50 to-pink-100 dark:from-pink-900/20 dark:to-pink-800/20 p-4 rounded-xl border border-pink-200 dark:border-pink-800"},Pe={class:"flex items-center space-x-2"},Ne={class:"text-sm font-bold text-pink-900 dark:text-pink-100"},Re={class:"bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 p-4 rounded-xl border border-blue-200 dark:border-blue-800"},Ae={class:"flex items-center space-x-2"},Ge={class:"text-sm font-bold text-blue-900 dark:text-blue-100"},Fe={class:"bg-gradient-to-br from-emerald-50 to-emerald-100 dark:from-emerald-900/20 dark:to-emerald-800/20 p-4 rounded-xl border border-emerald-200 dark:border-emerald-800"},Ee={class:"flex items-center space-x-2"},Qe={class:"text-sm font-bold text-emerald-900 dark:text-emerald-100"},We={class:"bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 p-4 rounded-xl border border-purple-200 dark:border-purple-800"},qe={class:"flex items-center space-x-2"},Je={class:"text-sm font-bold text-purple-900 dark:text-purple-100"},Ke={class:"px-6 pb-6"},Xe={class:"bg-white dark:bg-dark-surface rounded-xl border border-gray-200 dark:border-dark-border overflow-hidden"},Ye={key:0,class:"flex justify-center"},Ze={key:1,class:"text-gray-400 text-xs"},et={key:0,class:"flex justify-center"},tt={class:"relative w-16 h-16 rounded-lg overflow-hidden border border-gray-200 dark:border-dark-border"},rt={key:1,class:"text-gray-400 text-xs"},at={key:0,class:"text-sm font-medium text-gray-900 dark:text-dark-text"},ot={key:1,class:"text-gray-400 text-xs"},st={key:0,class:"text-sm text-gray-600 dark:text-dark-text-secondary"},lt={key:1,class:"text-gray-400 text-xs"},nt=["onClick"],dt={class:"flex justify-center p-4 border-t border-gray-200 dark:border-dark-border"},it={class:"flex items-center justify-between p-6 border-t border-gray-100 dark:border-dark-border bg-gray-50 dark:bg-dark-card/50"},ut={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},ct={class:"flex items-center space-x-3"},pt=X({__name:"ViewDetailsDialog",props:{modelValue:{type:Boolean},task:{}},emits:["update:modelValue"],setup(E,{emit:z}){const M=E,V=z,$=I({get:()=>M.modelValue,set:g=>V("update:modelValue",g)}),y=k(!1),m=k(1),u=k(10),w=I(()=>j.value.length),j=k([{fileName:"portrait1.png",originalSize:"1920x1080",status:"success",originalImage:"https://via.placeholder.com/100x100/6b7280/ffffff?text=ORIG1",thumbnail:"https://via.placeholder.com/100x100/ec4899/ffffff?text=CUT1",downloadUrl:"#"},{fileName:"object1.png",originalSize:"1600x900",status:"success",originalImage:"https://via.placeholder.com/100x100/6b7280/ffffff?text=ORIG2",thumbnail:"https://via.placeholder.com/100x100/f97316/ffffff?text=CUT2",downloadUrl:"#"},{fileName:"product1.png",originalSize:"1280x720",status:"failed",originalImage:"https://via.placeholder.com/100x100/6b7280/ffffff?text=ORIG3",thumbnail:"https://via.placeholder.com/100x100/ef4444/ffffff?text=FAIL",errorMessage:"图片背景过于复杂"},{fileName:"portrait2.png",originalSize:"2048x1536",status:"success",originalImage:"https://via.placeholder.com/100x100/6b7280/ffffff?text=ORIG4",thumbnail:"https://via.placeholder.com/100x100/8b5cf6/ffffff?text=CUT3",downloadUrl:"#"},{fileName:"object2.png",originalSize:"1440x900",status:"success",originalImage:"https://via.placeholder.com/100x100/6b7280/ffffff?text=ORIG5",thumbnail:"https://via.placeholder.com/100x100/f59e0b/ffffff?text=CUT4",downloadUrl:"#"}]),D=g=>({completed:"已完成",processing:"处理中",failed:"失败",pending:"等待中"})[g]||"未知",O=g=>({success:"成功",failed:"失败"})[g]||"未知",S=()=>{$.value=!1},R=g=>{_.success(`正在下载 ${g.fileName}`)},A=()=>{_.success("正在导出详情...")},G=g=>{u.value=g,m.value=1},L=g=>{m.value=g};return(g,r)=>{const C=b("el-table-column"),U=b("el-image"),H=b("el-table"),p=b("el-pagination"),o=b("el-dialog"),f=Z("loading");return c(),J(o,{modelValue:$.value,"onUpdate:modelValue":r[2]||(r[2]=s=>$.value=s),width:"1200px","before-close":S,"show-close":!1,class:"modern-dialog"},{header:l(()=>{var s;return[e("div",De,[e("div",Oe,[r[4]||(r[4]=e("div",{class:"w-10 h-10 bg-gradient-to-br from-pink-500 to-pink-600 rounded-xl flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})])],-1)),e("div",null,[r[3]||(r[3]=e("h3",{class:"text-xl font-bold text-gray-900 dark:text-dark-text"},"抠图详情",-1)),e("p",Le,"任务ID: "+i(((s=g.task)==null?void 0:s.id)||""),1)])]),e("button",{onClick:S,class:"p-2 text-gray-400 hover:text-gray-600 dark:text-dark-text-secondary dark:hover:text-dark-text rounded-lg hover:bg-gray-100 dark:hover:bg-dark-card transition-all duration-200"},r[5]||(r[5]=[e("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))])]}),footer:l(()=>[e("div",it,[e("div",ut," 共 "+i(w.value)+" 条抠图结果 ",1),e("div",ct,[e("button",{onClick:S,class:"px-6 py-2.5 text-gray-700 dark:text-dark-text font-medium rounded-lg border border-gray-300 dark:border-dark-border hover:bg-gray-50 dark:hover:bg-dark-border transition-all duration-200"}," 关闭 "),e("button",{onClick:A,class:"inline-flex items-center px-6 py-2.5 bg-gradient-to-r from-pink-500 to-pink-600 hover:from-pink-600 hover:to-pink-700 text-white font-medium rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"},[a(N(K),{class:"w-5 h-5 mr-2"}),r[16]||(r[16]=T(" 导出详情 "))])])])]),default:l(()=>[g.task?(c(),x("div",Ue,[e("div",He,[e("div",Pe,[r[7]||(r[7]=e("div",{class:"w-8 h-8 bg-pink-500 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1)),e("div",null,[r[6]||(r[6]=e("p",{class:"text-xs text-pink-600 dark:text-pink-400 font-medium"},"任务状态",-1)),e("p",Ne,i(D(g.task.status)),1)])])]),e("div",Re,[e("div",Ae,[r[9]||(r[9]=e("div",{class:"w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})])],-1)),e("div",null,[r[8]||(r[8]=e("p",{class:"text-xs text-blue-600 dark:text-blue-400 font-medium"},"目标数量",-1)),e("p",Ge,i(g.task.targetCount),1)])])]),e("div",Fe,[e("div",Ee,[r[11]||(r[11]=e("div",{class:"w-8 h-8 bg-emerald-500 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1)),e("div",null,[r[10]||(r[10]=e("p",{class:"text-xs text-emerald-600 dark:text-emerald-400 font-medium"},"成功数量",-1)),e("p",Qe,i(g.task.successCount),1)])])]),e("div",We,[e("div",qe,[r[13]||(r[13]=e("div",{class:"w-8 h-8 bg-purple-500 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})])],-1)),e("div",null,[r[12]||(r[12]=e("p",{class:"text-xs text-purple-600 dark:text-purple-400 font-medium"},"操作人",-1)),e("p",Je,i(g.task.operator),1)])])])])):P("",!0),e("div",Ke,[r[15]||(r[15]=e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-dark-text mb-4"},"抠图结果",-1)),e("div",Xe,[Y((c(),J(H,{data:j.value,style:{width:"100%"},"max-height":"400",class:"modern-table"},{default:l(()=>[a(C,{prop:"index",label:"序号",width:"80",align:"center"}),a(C,{label:"原图",width:"100",align:"center"},{default:l(s=>[s.row.status==="success"?(c(),x("div",Ye,[a(U,{src:s.row.originalImage,"preview-src-list":[s.row.originalImage],fit:"cover",class:"w-16 h-16 rounded-lg border border-gray-200 dark:border-dark-border","preview-teleported":!0},null,8,["src","preview-src-list"])])):(c(),x("div",Ze,"无图片"))]),_:1}),a(C,{label:"结果图",width:"100",align:"center"},{default:l(s=>[s.row.status==="success"?(c(),x("div",et,[e("div",tt,[r[14]||(r[14]=e("div",{class:"absolute inset-0 checkerboard-bg"},null,-1)),a(U,{src:s.row.thumbnail,"preview-src-list":[s.row.thumbnail],fit:"cover",class:"relative z-10 w-full h-full","preview-teleported":!0},null,8,["src","preview-src-list"])])])):(c(),x("div",rt,"无图片"))]),_:1}),a(C,{label:"文件名","min-width":"200"},{default:l(s=>[s.row.fileName?(c(),x("div",at,i(s.row.fileName),1)):(c(),x("div",ot,"无文件名"))]),_:1}),a(C,{prop:"originalSize",label:"原始尺寸",width:"120",align:"center"},{default:l(s=>[s.row.originalSize?(c(),x("span",st,i(s.row.originalSize),1)):(c(),x("span",lt,"-"))]),_:1}),a(C,{prop:"status",label:"状态",width:"100",align:"center"},{default:l(s=>[e("span",{class:ee(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",s.row.status==="success"?"bg-pink-100 dark:bg-pink-900/30 text-pink-800 dark:text-pink-300":"bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300"])},i(O(s.row.status)),3)]),_:1}),a(C,{label:"操作",width:"120",align:"center"},{default:l(s=>[s.row.status==="success"?(c(),x("button",{key:0,onClick:B=>R(s.row),class:"text-pink-600 dark:text-pink-400 hover:text-pink-700 dark:hover:text-pink-300 text-sm font-medium"}," 下载结果 ",8,nt)):P("",!0)]),_:1})]),_:1},8,["data"])),[[f,y.value]]),e("div",dt,[a(p,{"current-page":m.value,"onUpdate:currentPage":r[0]||(r[0]=s=>m.value=s),"page-size":u.value,"onUpdate:pageSize":r[1]||(r[1]=s=>u.value=s),"page-sizes":[10,20,50],total:w.value,layout:"total, sizes, prev, pager, next",onSizeChange:G,onCurrentChange:L},null,8,["current-page","page-size","total"])])])])]),_:1},8,["modelValue"])}}}),gt=te(pt,[["__scopeId","data-v-0bb5a421"]]),xt={class:"space-y-6"},mt={class:"grid grid-cols-1 md:grid-cols-4 gap-6"},kt={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},vt={class:"flex items-center justify-between"},ft={class:"text-2xl font-bold text-gray-900 dark:text-dark-text"},ht={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},bt={class:"flex items-center justify-between"},wt={class:"text-2xl font-bold text-green-600 dark:text-green-400"},_t={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},yt={class:"flex items-center justify-between"},Ct={class:"text-2xl font-bold text-blue-600 dark:text-blue-400"},$t={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},jt={class:"flex items-center justify-between"},zt={class:"text-2xl font-bold text-orange-600 dark:text-orange-400"},Vt={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border"},Tt={class:"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0"},Mt={class:"flex items-center space-x-3"},St={key:0,class:"flex items-center space-x-2 ml-4 pl-4 border-l border-gray-200 dark:border-dark-border"},Bt={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},It={class:"bg-white dark:bg-dark-surface rounded-xl shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border overflow-hidden"},Dt={class:"overflow-x-auto"},Ot={class:"font-mono text-sm text-gray-600 dark:text-dark-text-secondary"},Lt={class:"flex items-center space-x-2"},Ut={class:"font-medium text-gray-900 dark:text-dark-text"},Ht={class:"font-medium text-pink-600 dark:text-pink-400"},Pt={class:"flex items-center space-x-2"},Nt={class:"w-6 h-6 bg-gradient-to-br from-pink-400 to-pink-500 rounded-full flex items-center justify-center"},Rt={class:"text-white text-xs font-medium"},At={class:"text-sm text-gray-900 dark:text-dark-text"},Gt={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},Ft={class:"flex items-center space-x-2"},Et=["onClick"],Qt={class:"flex justify-between items-center px-6 py-4 border-t border-gray-100 dark:border-dark-border bg-gray-50 dark:bg-dark-card/50"},Wt={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},qt=X({__name:"index",setup(E){const z=k(!1),M=k(!1),V=k(!1),$=k(null),y=k([]),m=k({currentPage:1,pageSize:20,total:0}),u=k(1248),w=k(94.2),j=k(28),D=k(5),O=k([{id:"CO001",targetCount:25,status:"completed",successCount:24,operator:"张三",createTime:"2024-01-15 14:30:25"},{id:"CO002",targetCount:18,status:"processing",successCount:12,operator:"李四",createTime:"2024-01-15 13:45:12"},{id:"CO003",targetCount:32,status:"completed",successCount:30,operator:"王五",createTime:"2024-01-15 12:20:08"},{id:"CO004",targetCount:15,status:"failed",successCount:8,operator:"赵六",createTime:"2024-01-15 11:15:33"},{id:"CO005",targetCount:42,status:"completed",successCount:41,operator:"钱七",createTime:"2024-01-15 10:30:45"},{id:"CO006",targetCount:28,status:"processing",successCount:15,operator:"孙八",createTime:"2024-01-15 09:45:22"},{id:"CO007",targetCount:36,status:"completed",successCount:35,operator:"周九",createTime:"2024-01-15 08:20:15"},{id:"CO008",targetCount:22,status:"completed",successCount:21,operator:"吴十",createTime:"2024-01-14 16:30:40"}]),S=n=>{const t={completed:"bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300",processing:"bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300",failed:"bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300",pending:"bg-gray-100 dark:bg-gray-900/30 text-gray-800 dark:text-gray-300"};return t[n]||t.pending},R=n=>({completed:"已完成",processing:"处理中",failed:"失败",pending:"等待中"})[n]||"未知",A=n=>{y.value=n},G=n=>{$.value=n,V.value=!0},L=n=>{const{action:t,row:h}=n;switch(t){case"titleGenerate":H(h);break;case"batchListing":p(h);break;case"smartCrop":o(h);break;case"superSplit":f(h);break;case"copyrightDetection":s(h);break}},g=()=>{_.success("导出表格功能开发中...")},r=()=>{_.success(`正在批量导出 ${y.value.length} 个任务...`)},C=()=>{_.success("抠图任务创建成功！"),U()},U=()=>{z.value=!0,setTimeout(()=>{z.value=!1},500)},H=n=>{_.success(`正在为抠图任务 ${n.id} 创建标题生成任务...`)},p=n=>{_.success(`正在为抠图任务 ${n.id} 创建批量刊登任务...`)},o=n=>{_.success(`正在为抠图任务 ${n.id} 创建智能裁图任务...`)},f=n=>{_.success(`正在为抠图任务 ${n.id} 创建超级裂变任务...`)},s=n=>{_.success(`正在为抠图任务 ${n.id} 创建侵权检测任务...`)},B=n=>{m.value.pageSize=n,m.value.currentPage=1,v()},Q=n=>{m.value.currentPage=n,v()},v=()=>{z.value=!0,setTimeout(()=>{const n=[{id:"CO001",targetCount:25,status:"completed",successCount:24,operator:"张三",createTime:"2024-01-15 14:30:25"},{id:"CO002",targetCount:18,status:"processing",successCount:12,operator:"李四",createTime:"2024-01-15 13:45:12"},{id:"CO003",targetCount:32,status:"completed",successCount:30,operator:"王五",createTime:"2024-01-15 12:20:08"},{id:"CO004",targetCount:15,status:"failed",successCount:8,operator:"赵六",createTime:"2024-01-15 11:15:33"},{id:"CO005",targetCount:42,status:"completed",successCount:41,operator:"钱七",createTime:"2024-01-15 10:30:45"},{id:"CO006",targetCount:28,status:"processing",successCount:15,operator:"孙八",createTime:"2024-01-15 09:45:22"},{id:"CO007",targetCount:36,status:"completed",successCount:35,operator:"周九",createTime:"2024-01-15 08:20:15"},{id:"CO008",targetCount:22,status:"completed",successCount:21,operator:"吴十",createTime:"2024-01-14 16:30:40"}],t=(m.value.currentPage-1)*m.value.pageSize,h=t+m.value.pageSize;O.value=n.slice(t,h),m.value.total=n.length,z.value=!1},500)};return de(()=>{v()}),(n,t)=>{const h=b("el-table-column"),F=b("el-dropdown-item"),re=b("el-dropdown-menu"),ae=b("el-dropdown"),oe=b("el-table"),se=b("el-pagination"),le=Z("loading");return c(),x(q,null,[e("div",xt,[t[26]||(t[26]=ie('<div class="bg-gradient-to-r from-pink-50 to-rose-50 dark:from-pink-900/20 dark:to-rose-900/20 rounded-2xl p-6 border border-pink-100 dark:border-pink-800" data-v-50dcc0ff><div class="flex items-center space-x-3" data-v-50dcc0ff><div class="w-10 h-10 bg-gradient-to-br from-pink-500 to-pink-600 rounded-xl flex items-center justify-center" data-v-50dcc0ff><svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-50dcc0ff><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" data-v-50dcc0ff></path></svg></div><div data-v-50dcc0ff><h1 class="text-2xl font-bold text-gray-900 dark:text-dark-text" data-v-50dcc0ff>一键抠图</h1><p class="mt-1 text-sm text-gray-600 dark:text-dark-text-secondary" data-v-50dcc0ff>AI智能图片抠图和背景移除工具</p></div></div></div>',1)),e("div",mt,[e("div",kt,[e("div",vt,[e("div",null,[t[5]||(t[5]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"总抠图数",-1)),e("p",ft,i(u.value),1)]),t[6]||(t[6]=e("div",{class:"w-12 h-12 bg-pink-100 dark:bg-pink-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-pink-600 dark:text-pink-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})])],-1))])]),e("div",ht,[e("div",bt,[e("div",null,[t[7]||(t[7]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"成功率",-1)),e("p",wt,i(w.value)+"%",1)]),t[8]||(t[8]=e("div",{class:"w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-green-600 dark:text-green-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1))])]),e("div",_t,[e("div",yt,[e("div",null,[t[9]||(t[9]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"今日抠图",-1)),e("p",Ct,i(j.value),1)]),t[10]||(t[10]=e("div",{class:"w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-blue-600 dark:text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1))])]),e("div",$t,[e("div",jt,[e("div",null,[t[11]||(t[11]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"处理中",-1)),e("p",zt,i(D.value),1)]),t[12]||(t[12]=e("div",{class:"w-12 h-12 bg-orange-100 dark:bg-orange-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-orange-600 dark:text-orange-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})])],-1))])])]),e("div",Vt,[e("div",Tt,[e("div",Mt,[e("button",{onClick:t[0]||(t[0]=d=>M.value=!0),class:"inline-flex items-center px-4 py-2.5 bg-gradient-to-r from-pink-500 to-pink-600 hover:from-pink-600 hover:to-pink-700 text-white font-medium rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"},[a(N(ue),{class:"w-5 h-5 mr-2"}),t[13]||(t[13]=T(" 新建抠图 "))]),e("button",{onClick:g,class:"inline-flex items-center px-4 py-2.5 bg-white dark:bg-dark-card text-gray-700 dark:text-dark-text font-medium rounded-lg border border-gray-300 dark:border-dark-border hover:bg-gray-50 dark:hover:bg-dark-border shadow-sm hover:shadow-md transition-all duration-200"},[a(N(K),{class:"w-5 h-5 mr-2"}),t[14]||(t[14]=T(" 导出表格 "))]),y.value.length>0?(c(),x("div",St,[e("span",Bt," 已选择 "+i(y.value.length)+" 项 ",1),e("button",{onClick:r,class:"inline-flex items-center px-3 py-1.5 bg-pink-500 hover:bg-pink-600 text-white text-sm font-medium rounded-lg transition-all duration-200"},[a(N(K),{class:"w-4 h-4 mr-1"}),t[15]||(t[15]=T(" 批量导出 "))])])):P("",!0)])])]),e("div",It,[t[25]||(t[25]=e("div",{class:"px-6 py-4 border-b border-gray-100 dark:border-dark-border"},[e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-dark-text"},"抠图任务列表"),e("p",{class:"text-sm text-gray-600 dark:text-dark-text-secondary mt-1"},"管理和监控您的所有抠图任务")],-1)),e("div",Dt,[Y((c(),J(oe,{data:O.value,style:{width:"100%"},onSelectionChange:A,class:"modern-table","header-cell-style":{backgroundColor:"var(--el-bg-color-page)",color:"var(--el-text-color-primary)",fontWeight:"600",borderBottom:"1px solid var(--el-border-color-light)"},"row-style":{backgroundColor:"transparent"}},{default:l(()=>[a(h,{type:"selection",width:"55"}),a(h,{prop:"id",label:"抠图ID",width:"120"},{default:l(d=>[e("span",Ot,i(d.row.id),1)]),_:1}),a(h,{label:"抠图数量",width:"150"},{default:l(d=>[e("div",Lt,[t[16]||(t[16]=e("span",{class:"text-sm text-gray-600 dark:text-dark-text-secondary"},"目标:",-1)),e("span",Ut,i(d.row.targetCount),1),t[17]||(t[17]=e("span",{class:"text-gray-400"},"|",-1)),t[18]||(t[18]=e("span",{class:"text-sm text-gray-600 dark:text-dark-text-secondary"},"成功:",-1)),e("span",Ht,i(d.row.successCount),1)])]),_:1}),a(h,{prop:"status",label:"抠图状态",width:"120"},{default:l(d=>[e("span",{class:ee([S(d.row.status),"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"])},i(R(d.row.status)),3)]),_:1}),a(h,{prop:"operator",label:"操作人",width:"100"},{default:l(d=>[e("div",Pt,[e("div",Nt,[e("span",Rt,i(d.row.operator.charAt(0)),1)]),e("span",At,i(d.row.operator),1)])]),_:1}),a(h,{prop:"createTime",label:"创建时间",width:"180"},{default:l(d=>[e("div",Gt,i(d.row.createTime),1)]),_:1}),a(h,{label:"操作",width:"180"},{default:l(d=>[e("div",Ft,[e("button",{onClick:Jt=>G(d.row),class:"inline-flex items-center px-3 py-1.5 text-sm font-medium text-pink-600 dark:text-pink-400 hover:text-pink-700 dark:hover:text-pink-300 bg-pink-50 dark:bg-pink-900/20 hover:bg-pink-100 dark:hover:bg-pink-900/30 rounded-lg transition-all duration-200"}," 查看详情 ",8,Et),a(ae,{onCommand:L,trigger:"click"},{dropdown:l(()=>[a(re,null,{default:l(()=>[a(F,{command:{action:"titleGenerate",row:d.row}},{default:l(()=>t[19]||(t[19]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-blue-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})]),e("span",null,"标题生成")],-1)])),_:2,__:[19]},1032,["command"]),a(F,{command:{action:"batchListing",row:d.row}},{default:l(()=>t[20]||(t[20]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-purple-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"})]),e("span",null,"批量刊登")],-1)])),_:2,__:[20]},1032,["command"]),a(F,{command:{action:"smartCrop",row:d.row}},{default:l(()=>t[21]||(t[21]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-green-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m0 0V1a1 1 0 011-1h2a1 1 0 011 1v18a1 1 0 01-1 1H4a1 1 0 01-1-1V1a1 1 0 011-1h2a1 1 0 011 1v3m0 0h8m-8 0H4a1 1 0 00-1 1v3a1 1 0 001 1h3m0 0h10m0 0h3a1 1 0 001-1V5a1 1 0 00-1-1h-3m-3 0v8a1 1 0 01-1 1H8a1 1 0 01-1-1V4"})]),e("span",null,"智能裁图")],-1)])),_:2,__:[21]},1032,["command"]),a(F,{command:{action:"superSplit",row:d.row}},{default:l(()=>t[22]||(t[22]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-orange-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 10V3L4 14h7v7l9-11h-7z"})]),e("span",null,"超级裂变")],-1)])),_:2,__:[22]},1032,["command"]),a(F,{command:{action:"copyrightDetection",row:d.row}},{default:l(()=>t[23]||(t[23]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-blue-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"})]),e("span",null,"侵权检测")],-1)])),_:2,__:[23]},1032,["command"])]),_:2},1024)]),default:l(()=>[t[24]||(t[24]=e("button",{class:"inline-flex items-center px-2 py-1.5 text-sm font-medium text-gray-600 dark:text-dark-text-secondary hover:text-gray-700 dark:hover:text-dark-text bg-gray-50 dark:bg-dark-card hover:bg-gray-100 dark:hover:bg-dark-border rounded-lg transition-all duration-200"},[T(" 更多 "),e("svg",{class:"w-4 h-4 ml-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 9l-7 7-7-7"})])],-1))]),_:2,__:[24]},1024)])]),_:1})]),_:1},8,["data"])),[[le,z.value]])]),e("div",Qt,[e("div",Wt," 共 "+i(m.value.total)+" 条记录 ",1),a(se,{"current-page":m.value.currentPage,"onUpdate:currentPage":t[1]||(t[1]=d=>m.value.currentPage=d),"page-size":m.value.pageSize,"onUpdate:pageSize":t[2]||(t[2]=d=>m.value.pageSize=d),"page-sizes":[10,20,50,100],total:m.value.total,layout:"sizes, prev, pager, next, jumper",onSizeChange:B,onCurrentChange:Q,class:"modern-pagination"},null,8,["current-page","page-size","total"])])])]),a(Ie,{modelValue:M.value,"onUpdate:modelValue":t[3]||(t[3]=d=>M.value=d),onSuccess:C},null,8,["modelValue"]),a(gt,{modelValue:V.value,"onUpdate:modelValue":t[4]||(t[4]=d=>V.value=d),task:$.value},null,8,["modelValue","task"])],64)}}}),tr=te(qt,[["__scopeId","data-v-50dcc0ff"]]);export{tr as default};
